package main

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	net "net/url"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"time"
)

func main() {
	hostTxt,err := ioutil.ReadFile("/usr/local/bin/host.txt")///usr/local/bin/host.txt
	if err != nil {
		fmt.Println("err ", err)
		sendWhatsApp("no host file - " + err.Error())
		return
	}
	hostArr := strings.Split(string(hostTxt),"\n")
	//ls, err := exeCommand("ls","-al")
	//fmt.Println("dir : \n", ls, err)

	for _,url := range hostArr {
		host := strings.Split(url, ",")
		title := host[0]
		if len(host) > 1 &&  strings.TrimSpace(host[1]) != "" {
			title =  strings.TrimSpace(host[1])
		}

		if strings.TrimSpace(url) != "" {
			checkWeb(host[0], title)
			//fmt.Println("check : ", title)
		}
	}
}

func readFile(path string) (string, error) {
	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()
	result, err := ioutil.ReadAll(file)
	return  string(result), nil
}


func checkWeb(url, title string) {
	resp, err := http.Get(url)
	if checkErr(err) {
		sendErrorMessage(fmt.Sprintf("*SERVICE DOWN* %s (%s) might be down.\n%v", url, title, err))
		return
	}

	fmt.Println(url, " Status Code : ", resp.StatusCode)
	if resp.StatusCode >= 300 {
		msg := "*SERVER DOWN ALERT* : "+title + " is Down, Reason : "+resp.Status+" (" + strconv.Itoa(resp.StatusCode) +")"
		//6285742257881-1495197570
		sendErrorMessage(msg)
	}
}

func sendErrorMessage(msg string){
	//exeCommand("yowsup-cli", "demos", "-d", "-s", "6285742257881", msg, "-c","/home/<USER>/yowsup/config")
	//exeCommand("yowsup-cli", "demos", "-d", "-s", "6285742257881-1495197570", msg, "-c","/home/<USER>/yowsup/config")

	err := sendWhatsApp(msg)
	if err != nil {
		//exeCommand("yowsup-cli", "demos", "-d", "-s", "6285742257881-1495197570", msg, "-c","/home/<USER>/yowsup/config")
	}
	sendSlack(msg)
}

func sendWhatsApp(message string) error {
	data := net.Values{}
	data.Set("phone", "6285742257881-1495197570")
	data.Set("message", message)

	res, err := http.PostForm("http://127.0.0.1:1719/send/wa", data)
	if err != nil{
		fmt.Println("Send WA to Local Error ", err)
		return err
	}

	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println("Read body response WA error, ", err)
		return err
	}

	if res.StatusCode != 200 {
		return errors.New("can not send to whatsapp")
	}

	fmt.Println("Send WA Body : ", string(body))

	return err
}


func sendSlack(message string) {
	url := "*****************************************************************************"

	body := map[string]interface{}{
		"text" : message,
	}
	var jsonStr,_ = json.Marshal(body)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
}


func checkErr(err error) bool {
	if err != nil {
		date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
		//fmt.Println(date, "   Error While ", onDoing, ". ", err)
		_, fn, line, _ := runtime.Caller(1)
		//fmt.Printf("%s  [error] %s:%d %v", date, fn, line, err)
		fmt.Printf("%s  [error] %s:%d %v", date, fn, line, err)

		return true
	}

	return false
}

func exeCommand(name string, args ...string) (string, error) {
	cmd := exec.Command(name, args...)

	cmdOutput := &bytes.Buffer{}
	// Attach buffer to command
	cmd.Stdout = cmdOutput

	err := cmd.Run()
	if err != nil {
		fmt.Printf("==> Error When Executing : %s\n", strings.Join(cmd.Args, " "))
		fmt.Println("==> Error Message : ", err.Error())
		return "", err
	}

	outputByte := cmdOutput.Bytes()
	if len(outputByte) > 0 {
		return string(outputByte), nil
	}else {
		return "", nil
	}
}
