SELECT
  s.sales_id,
  sd.sub_total                                                as sub_total,
  sd.discount                                                 as discountSd,
  sd.qty                                                      as qty,
  sd.price                                                    as price,
  ifnull(sdd.total, 0)                                            as detailDis,
  case when g.tax_category = 'tax' then sdt.total else 0 end as taxDetail,
  case when g.tax_category = 'discount' then sdt.total else 0 end as discountTax,
  case when g.tax_category = 'service' then sdt.total else 0 end as serviceDetail,
  case when g.tax_category = 'voucher' then sdt.total else 0 end as voucherDetail,
  g.tax_category, sp.total, sd.sales_detail_id, s.grand_total
FROM sales s
  LEFT JOIN sales_detail sd ON sd.sales_fkid = s.sales_id
  LEFT JOIN products_detail pd ON pd.product_detail_id = sd.product_detail_fkid
  LEFT JOIN products p ON p.product_id = pd.product_fkid
  LEFT JOIN open_shift os ON os.open_shift_id = s.open_shift_fkid
  LEFT JOIN sales_detail_discount sdd ON sdd.sales_detail_fkid = sd.sales_detail_id
  LEFT JOIN sales_detail_tax sdt ON sdt.sales_detail_fkid = sd.sales_detail_id
  LEFT JOIN gratuity g ON g.gratuity_id = sdt.tax_fkid
  left join sales_payment sp on sp.sales_fkid=s.sales_id
WHERE s.outlet_fkid = '29'
      AND s.status = 'success'
      and sp.method != 'COMPLIMENT'
      AND from_unixtime(s.time_created / 1000 + 25200, '%Y-%m-%d') = '2018-09-04'