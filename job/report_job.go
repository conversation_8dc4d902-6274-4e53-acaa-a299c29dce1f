package job

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/valyala/fasthttp"
	v1 "gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"html/template"
	"os"
	"time"
)

func PubSubSendAllOutletReport(ctx *fasthttp.RequestCtx) {
	var pubsub models.PubSub
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&pubsub)
	if err != nil {
		fmt.Fprintf(os.Stderr, "decode json error - %v", err)
	}

	data, err := base64.StdEncoding.DecodeString(pubsub.Message.Data)
	fmt.Println("[PubSubSendAllOutletReport] data : ", string(data))
	if string(data) != "" {
		go SendAllOutletReport()
	}

	ctx.SetStatusCode(fasthttp.StatusCreated)
}

func SendAllOutletReport() {
	offset := int64(25200)
	sql := `
select admin_fkid, min(os.open_shift_id) as open_shift_id, count(os.outlet_fkid) as total_outlet, min(os.outlet_fkid) as outlet_id 
from outlets o
join (
    select outlet_fkid, min(open_shift_id) as open_shift_id
    from open_shift
    where from_unixtime(time_open / 1000 + 25200, '%d-%m-%Y') = ?
    group by outlet_fkid
    ) os on os.outlet_fkid=o.outlet_id
group by admin_fkid
having total_outlet > 1 `

	dateYesterday := time.Unix(time.Now().Unix()+offset, 0).AddDate(0, 0, -1)
	date := dateYesterday.Format("02-01-2006")
	log.Info("checking undelivered report at '%s'", date)

	dataArr, err := db.QueryArray(sql, date)
	if log.IfError(err) {
		return
	}

	for _, data := range dataArr {
		//isSent, err := v1.DecideToSendShiftAllOutlet(utils.ToInt(data["admin_fkid"]), dateYesterday)
		//if err == nil && !isSent {
		//	log.Info("process for sending recap all outlet (by schedule) for admin : %d ...", data["admin_fkid"])
		//	v1.SendRecapAllOutlet(models.CashRecap{OpenShiftFkid: utils.ToInt(data["open_shift_id"]), OutletFkid: utils.ToInt(data["outlet_id"])})
		//} else {
		//	log.Info("recap all outlet for admin %d already sent", data["admin_fkid"])
		//}

		//check from scheduled message
		identifierId := fmt.Sprintf("recapalloutlet_%d_%s", data["admin_fkid"], date)
		count, err := db.Query("select count(*) as total from scheduled_message where identifier_id=?", identifierId)
		log.Info("checking for identifierId: %s ---> %v", identifierId, count)
		if !log.IfError(err) && utils.ToInt(count["total"]) == 0 {
			v1.SendRecapAllOutlet(models.CashRecap{OpenShiftFkid: utils.ToInt(data["open_shift_id"]), OutletFkid: utils.ToInt(data["outlet_id"])})
		}
	}
}

func PiutangReminder() {
	log.Info("creating piutang reminder...")

	offset := int64(25200)
	sql := `select p.*,
       s.display_nota,
       s.customer_name,
       datediff(from_unixtime(p.due_date / 1000 + ?, '%Y-%m-%d'),
                from_unixtime((select unix_timestamp(now())) + ?, '%Y-%m-%d')) as count_down,
       from_unixtime(p.due_date / 1000 + ?, '%d %M %Y')                        as due_date_format,
       a.admin_id,
       a.business_name,
       a.email
from piutang p
         join sales s on p.sales_fkid = s.sales_id
         join outlets o on s.outlet_fkid = o.outlet_id
         join admin a on o.admin_fkid = a.admin_id
where s.status = 'success'
  and unpaid > 0
  and datediff(from_unixtime(p.due_date / 1000 + 25200, '%Y-%m-%d'),
               from_unixtime((select unix_timestamp(now())) + 25200, '%Y-%m-%d')) <= 7
order by admin_id, p.due_date`

	data, err := db.QueryArray(sql, offset, offset, offset)
	if log.IfError(err) {
		return
	}

	piutangReminderList := make([]models.PiutangReminder, 0)
	piutangList := make([]models.Piutang, 0)
	overDueCount := 0
	for index, piutang := range data {
		if utils.ToInt(piutang["count_down"]) >= 0 {
			piutangModel := models.Piutang{
				Number:   len(piutangList) + 1,
				Invoice:  utils.ToString(piutang["display_nota"]),
				Customer: utils.ToString(piutang["customer_name"]),
				Total:    fmt.Sprintf("Rp%s", utils.CurrencyFormat(utils.ToInt(piutang["unpaid"]))),
				DueDate:  utils.ToString(piutang["due_date_format"]),
			}
			piutangList = append(piutangList, piutangModel)
		} else {
			overDueCount++
		}
		if index == (len(data)-1) || utils.ToString(piutang["admin_id"]) != utils.ToString(data[index+1]["admin_id"]) {
			if len(piutangList) == 0 { //if user has no due date >= 0 (all overdue) then skip
				overDueCount = 0
				continue
			}

			warning := ""
			if overDueCount > 0 {
				warning = fmt.Sprintf("*terdapat juga %d transaksi lainnya yang masa jatuh temponya telah berakhir, cek lebih detail nya via aplikasi UNIQ POS", overDueCount)
			}
			piutangReminderList = append(piutangReminderList, models.PiutangReminder{
				Business:      utils.ToString(piutang["business_name"]),
				BusinessEmail: utils.ToString(piutang["email"]),
				Warning:       warning,
				PiutangList:   piutangList,
			})

			//reset data
			overDueCount = 0
			piutangList = make([]models.Piutang, 0)
		}
	}

	log.Info("business to reminder : %d", len(piutangReminderList))

	receiptTemplate := "config/template/piutang_reminder_template.html"
	tmpl, err := utils.ReadFile(receiptTemplate)
	if log.IfError(err) {
		return
	}

	templateReport, err := template.New("receipt_note").Parse(tmpl)
	if log.IfError(err) {
		return
	}

	for _, piutangData := range piutangReminderList {
		var result bytes.Buffer
		err = templateReport.Execute(&result, piutangData)
		if log.IfError(err) {
			break
		}

		utils.SendEmailZoho("", piutangData.BusinessEmail, "PIUTANG REMINDER", result.String())
	}
}
