package job

import (
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/go-co-op/gocron"
	v1 "gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/gdrive"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
)

var s *gocron.Scheduler

func GetCron() *gocron.Scheduler {
	if s == nil {
		s = gocron.NewScheduler(time.UTC)
	}
	return s
}

func InitTaskSchedule() {
	if os.Getenv("disable_cron") == "true" {
		fmt.Println("cron is disabled..")
		return
	}

	fmt.Println("init cronjob...")
	GetCron()

	s.Every(15).Minutes().Do(uploadLogs)
	//gocron.Every(1).Hour().Do(WriteLog) //save nohup log, and organize by time
	//gocron.Every(1).Hour().Do(UploadSystemLog) //upload system log to storage bucket

	s.Every(1).Day().At("01:00").Do(SendAllOutletReport)
	s.Every(1).Day().At("01:30").Do(PiutangReminder)
	s.Every(1).Day().At("19:00").Do(gdrive.RemoveOldLogs)
	s.Every(1).Day().At("19:30").Do(checkStockSummary)
	s.Every(1).Day().At("22:00").Do(deleteGDriveIds)
	s.Every(1).Day().At("22:30").Do(v1.LoadHelpFromFirebase)

	s.Every(1).Day().At("02:30").Do(v1.CheckVoucherAnomaly)

	//tmp job
	//gocron.Every(1).Day().At("20:00").Do(AdjustBreakdown)
	//gocron.Every(3).Minutes().Do(UpdateQtyBreakdownZero)

	s.StartAsync()
	log.Info("cronjob initialized....")
}

func AdjustBreakdown() {
	log.Info("update breakdown...")
	start := time.Now()

	var wg sync.WaitGroup
	maxPage := 500
	maxGoRoutine := 5
	totalItem := 0

	sql := `select sb.sales_breakdown_id,sd.time_created, s.status
from sales_breakdown sb
join sales_detail sd on sd.sales_detail_id = sb.sales_detail_fkid
join sales s on s.sales_id=sd.sales_fkid
where sb.time_created = 0 limit ? offset ? `

	for i := 0; i < maxGoRoutine; i++ {
		wg.Add(1)
		go func(page int) {
			defer wg.Done()
			breakdowns, err := db.QueryArray(sql, maxPage, maxPage*page)
			log.IfError(err)
			totalItem += len(breakdowns)

			for _, breakdown := range breakdowns {
				_, err = db.Update("sales_breakdown", map[string]any{
					"sales_status":  breakdown["status"],
					"time_created":  breakdown["time_created"],
					"time_modified": breakdown["time_created"],
				}, "sales_breakdown_id = ?", breakdown["sales_breakdown_id"])
				if log.IfError(err) {
					break
				}
			}
			fmt.Printf("page %d done, total %d\n", page+1, len(breakdowns))
		}(i)
	}

	log.Info("wait...")
	wg.Wait()

	log.Info("done... %d updated, in %v,  at %d o'clock", totalItem, time.Since(start), time.Now().Hour())

	time.Sleep(3 * time.Second)
	if totalItem > 0 && (time.Now().Hour() == 20) {
		AdjustBreakdown()
	}
}

func UpdateQtyBreakdownZero() {
	start := time.Now()
	sql := `
select sb.sales_breakdown_id,
       sd.product_detail_fkid as main_product_id,
       sb.product_detail_fkid as item_product_id,
       sd.qty,
       sb.qty_total,
       sb.status
from sales_breakdown sb
         join sales_detail sd on sb.sales_detail_fkid = sd.sales_detail_id
         left join
     (select max(time_created) last_opname, product_detail_fkid
      from stock_opname
      group by product_detail_fkid) op
     on op.product_detail_fkid = sb.product_detail_fkid
where sb.time_created > coalesce(op.last_opname, 0)
  and sb.qty_total = 0
limit ? offset ?
`
	maxPage := 500
	maxGoroutine := 5
	shouldStopUpdate := false
	itemCount := 0

	var wg sync.WaitGroup
	for i := 0; i < maxGoroutine; i++ {
		wg.Add(1)
		go func(page int) {
			defer wg.Done()

			safeFailed := 0
			breakdowns, err := db.QueryArray(sql, maxPage, maxPage*page)
			log.IfError(err)

			sql = `select qty from breakdown b where product_detail_fkid = ? and item_product_detail_fkid = ? `
			for _, breakdown := range breakdowns {
				data, err := db.Query(sql, breakdown["main_product_id"], breakdown["item_product_id"])
				if log.IfError(err) {
					break
				}

				qty := utils.ToFloat(breakdown["qty"]) * utils.ToFloat(data["qty"])
				_, err = db.Update("sales_breakdown", map[string]any{"qty_total": qty}, "sales_breakdown_id = ?", breakdown["sales_breakdown_id"])
				if log.IfError(err) {
					break
				}
				itemCount++

				if qty == 0 {
					safeFailed++
				}

				if safeFailed >= 10 {
					shouldStopUpdate = true
				}
			}
		}(i)
	}

	fmt.Println(shouldStopUpdate)

	wg.Wait()
	fmt.Printf("done... total updated: %d | in : %v\n", itemCount, time.Since(start))
}
