package job

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"strings"
	"time"

	"github.com/onatm/clockwerk"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/gdrive"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

type SystemJob struct{}

func ScheduleSystemJob() {
	var job SystemJob
	c := clockwerk.New()
	c.Every(15 * time.Minute).Do(job)
	c.Start()
}

func (j SystemJob) Run() {
	go uploadLogs()
}

// re-upload log (android log), which failed at previous attempt
func uploadLogs() {
	db := db.GetDbJson()
	records, err := db.ReadAll("system_log")
	if err != nil {
		return
	}

	for _, log := range records {
		data := models.SystemLog{}
		err := json.Unmarshal([]byte(log), &data)
		if !utils.CheckErr(err) {
			if _, err := os.Stat(data.FilePath); os.IsNotExist(err) {
				fmt.Println("File not found", err)
				db.Delete("system_log", data.Id)
				// utils.CheckErr(db.Delete("system_log", data.Id))
				continue
			}
			_, err = gdrive.UploadToGoogleDrive(data.FilePath, data.DestinationPath)
			if err != nil {
				fmt.Printf("re-uploading error : %v\n", err)
			} else {
				err := db.Delete("system_log", data.Id)
				// utils.CheckErr(err)
				if err = os.Remove(data.FilePath); err != nil {
					fmt.Println("Removing tmp file : ", data.FilePath, "error : ", err)
				}
			}
		}
	}
}

func deleteGDriveIds() {
	parentPath := "temp/db/gdrive_parent_id"
	files, err := ioutil.ReadDir(parentPath)
	if err != nil {
		log.Info("error deleting gdrive id : %v", err)
	}

	for _, file := range files {
		fileName := file.Name()

		//check extension and length
		if !strings.HasSuffix(fileName, ".json") || len(fileName) < 30 {
			continue
		}

		fileName = strings.Replace(fileName, ".json", "", -1)
		fileName = fileName[len(fileName)-10:]

		if !utils.IsNumber(strings.Replace(fileName, "_", "", -1)) {
			continue
		}

		t := time.Unix(time.Now().Unix()+25200, 0)
		timeNow := t.Format("2006_01_02")

		if fileName != timeNow {
			if err = os.Remove(fmt.Sprintf("%s/%s", parentPath, file.Name())); err != nil {
				fmt.Println("Deleting ", file.Name(), "error", err)
			}
		}
	}
}

func TestCleanUp() {
	cleanUpLogs()
}

// remove old logs to save some spaces
func cleanUpLogs() {

}
