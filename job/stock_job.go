package job

import (
	"fmt"
	"math/rand"
	"os"
	"strings"
	"sync"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
)

var mu sync.Mutex

func checkStockSummary() {
	//don't run in production yet
	if os.Getenv("server") == "production" {
		fmt.Println("checkStockSummary disabled on production server")
		return
	}

	//random sleep, to make mutex works
	time.Sleep(time.Duration(rand.Intn(15)) * time.Second)

	mu.Lock()
	defer mu.Unlock()

	processStart := time.Now()
	logProcess := ""

	//get data from stock summary
	timeStart := time.Now().Add(-24*time.Hour).Unix() * 1000
	log.Info("checking stock start at: %d", timeStart)

	sql := "select * from stock_summary where date_modified > ? "
	stocks, err := db.QueryArray(sql, timeStart)
	log.IfError(err)

	if len(stocks) <= 0 {
		log.Info("no stock to be checked....")
		return
	}

	log.Info("%d products will be updated", len(stocks))

	productDetailIds := make([]any, 0)
	for _, stock := range stocks {
		productDetailIds = append(productDetailIds, stock["product_detail_fkid"])
	}

	log.Info("product will update: %v", utils.SimplyToJson(productDetailIds))

	//hard refresh
	processStartPerJob := time.Now()

	// _, err = db.GetDb().Exec("call refresh_all_recent_stock(?)", timeStart)
	// if log.IfError(err) {
	// 	return
	// }

	checkIdx := int(0.1 * float64(len(productDetailIds)))
	log.Info("checking every %v loop", checkIdx)
	for i, id := range productDetailIds {
		_, err = db.GetDb().Exec("CALL update_qty_stock(?)", id)
		if i%checkIdx == 0 {
			log.Info("progress: %v out of %v", i, len(productDetailIds))
		}
	}

	logProcess += fmt.Sprintf(" | hardRefresh: %v", time.Since(processStartPerJob))
	processStartPerJob = time.Now()

	//get new stock after refresh
	stocksNew, err := db.QueryArray("select * from stock_summary where date_modified > ?", timeStart)
	log.IfError(err)

	//compare data
	log.Info("total stock old: %d | new: %d", len(stocks), len(stocksNew))
	stocksMap := make(map[int]map[string]any)
	for _, stock := range stocks {
		stocksMap[utils.ToInt(stock["product_detail_fkid"])] = stock
	}

	stocksNewMap := make(map[int]map[string]any)
	for _, stock := range stocksNew {
		stocksNewMap[utils.ToInt(stock["product_detail_fkid"])] = stock
	}

	diffInKeys := make(map[string]int)
	diffProds := make([]int, 0)
	report := make([]string, 0)
	keys := []string{"open", "purchase", "sales", "refund", "spoil", "transfer_in", "transfer_out", "retur", "production", "closing"}
	for prodId, stock := range stocksMap {
		stockNew := stocksNewMap[prodId]
		for _, key := range keys {
			isSame := utils.ToInt(stock[key]) == utils.ToInt(stockNew[key])
			if !isSame {
				report = append(report, fmt.Sprintf("%s of %v -- before: %v VS after: %v", key, prodId, stock[key], stockNew[key]))
				diffInKeys[key] = diffInKeys[key] + 1
				diffProds = append(diffProds, prodId)
			}
		}
	}

	fmt.Println("diff keys: ", utils.SimplyToJson(diffInKeys))
	fmt.Println("product ids: ", diffProds)

	//make report
	if len(report) > 0 {
		log.IfError(fmt.Errorf("%d stock data is different", len(report)))
		dataToReport := report
		if len(dataToReport) > 50 {
			dataToReport = dataToReport[:50]
		}
		log.Info(fmt.Sprintf("STOCK COMPARE: \n%s", strings.Join(dataToReport, "\n")))
		utils.SendMessageToSlack(fmt.Sprintf("STOCK COMPARE: \n%s", strings.Join(dataToReport, "\n")))
	}

	log.Info("checking stock took: %v", time.Since(processStart))
	fmt.Println("checking stock log: ", logProcess)
}
