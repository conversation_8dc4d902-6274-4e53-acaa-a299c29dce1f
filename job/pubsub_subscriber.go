package job

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"sync"

	"cloud.google.com/go/pubsub"
	v1 "gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
)

func PullPubSubMessages() {
	if os.Getenv("ENABLE_PUBSUB") == "false" {
		fmt.Println("Pub/Sub disabled...")
		return
	}

	client := google.GetPubSubClient()
	if client == nil {
		fmt.Println("[Pub/Sub] client has not been initialized")
		return
	}

	env := os.Getenv("server")
	if env == "demo" {
		env = "staging"
	}

	go subscriptionMemberLevel(client, env)
	go subscriptionOrderStatus(client, env)
	go subscriptionPayment(client, env)
	go subscibtionIncomingMessage(client, env)
}

func subscriptionMemberLevel(client *pubsub.Client, env string) {
	fmt.Println("subscription running... member level")
	var mu sync.Mutex
	ctx := context.Background()
	sub := client.Subscription(fmt.Sprintf("refresh_member_level_%s", env))
	err := sub.Receive(ctx, func(ctx context.Context, message *pubsub.Message) {
		mu.Lock()
		defer mu.Unlock()
		log.Info("[Pub/Sub] got message with id '%s' : %q", message.ID, string(message.Data))
		go actionRefreshMemberLevel(message.Data)
		message.Ack()
	})
	log.IfError(err)
}

func subscriptionOrderStatus(client *pubsub.Client, env string) {
	fmt.Println("subscription running... order status")
	var mu sync.Mutex
	ctx := context.Background()

	subsOrder := client.Subscription(fmt.Sprintf("crm-order-status-%s", env))
	err := subsOrder.Receive(ctx, func(ctx context.Context, message *pubsub.Message) {
		mu.Lock()
		defer mu.Unlock()
		log.Info("[Pub/Sub] got message with id '%s' : %q", message.ID, string(message.Data))
		//go checkOrderStatus(message.Data)
		if checkOrderStatus(message.Data) {
			message.Ack()
		} else {
			//log.IfError(fmt.Errorf("pubsub nack: %s", string(message.Data)))
			message.Nack()
			log.Info("Pub/Sub not ack")
		}
	})
	log.IfError(err)
}

func subscriptionPayment(client *pubsub.Client, env string) {
	//staging enviroment needs to subscribe to payment, as webhook only sent to production env
	if env != "staging" && env != "demo" {
		log.Info("not subscribe to payment in env %s", env)
		return
	}

	var mu sync.Mutex
	ctx := context.Background()
	subsPayment := client.Subscription("billing_webhook_production_subs")
	err := subsPayment.Receive(ctx, func(ctx context.Context, message *pubsub.Message) {
		mu.Lock()
		defer mu.Unlock()
		log.Info("[Pub/Sub] got message with id '%s' : %q (billing webhook)", message.ID, string(message.Data))

		var payment struct {
			PaymentGateway string         `json:"payment_gateway"`
			Payload        map[string]any `json:"payload"`
		}

		err := json.Unmarshal(message.Data, &payment)
		if !log.IfError(err) {
			err = v1.HandlePaymentWebhook(payment.Payload)
			fmt.Println("handle payment result err: ", err)
		}

		message.Ack()
	})
	log.IfError(err)
}

func subscibtionIncomingMessage(client *pubsub.Client, env string) {
	fmt.Println("subscription running... incoming message")
	var mu sync.Mutex
	ctx := context.Background()
	subsIncomingMessage := client.Subscription(fmt.Sprintf("incoming_message_%v", env))
	err := subsIncomingMessage.Receive(ctx, func(ctx context.Context, message *pubsub.Message) {
		mu.Lock()
		defer mu.Unlock()
		log.Info("[Pub/Sub] got message with id '%s' : %q", message.ID, string(message.Data))

		var incomingMessage struct {
			Message   string `json:"message"`
			Timestamp int64  `json:"timestamp"`
			Type      string `json:"type"`
			Sender    string `json:"sender"`
		}

		err := json.Unmarshal(message.Data, &incomingMessage)
		if log.IfError(err) {
			return
		}

		if strings.ToLower(incomingMessage.Message) == "ya" {
			v1.AskResendReceipt(incomingMessage.Sender)
		}

		// v1.HandleIncomingMessage(incomingMessage.MessageId, incomingMessage.MemberId, incomingMessage.AdminId)
		message.Ack()
	})
	log.IfError(err)
}

func actionRefreshMemberLevel(data []byte) {
	var member struct {
		MemberId any `json:"member_id"`
		AdminId  any `json:"admin_id"`
	}
	err := json.Unmarshal(data, &member)
	if log.IfError(err) {
		return
	}

	v1.CheckMemberLevel(utils.ToString(member.MemberId), utils.ToString(member.AdminId))
}

func checkOrderStatus(data []byte) bool {
	var orderStatus struct {
		OrderSalesId string `json:"order_sales_id"`
		Status       string `json:"status"`
	}

	err := json.Unmarshal(data, &orderStatus)
	if log.IfError(err) {
		log.Info("marshal err: %v", string(data))
		return false
	}

	if orderStatus.Status == "pending" {
		v1.SendNotificationOrder(orderStatus.OrderSalesId)
	}

	result := v1.HandleOrderSales(orderStatus.OrderSalesId)
	fmt.Println("Pub/Sub saving order result: ", utils.SimplyToJson(result))
	if orderStatus.OrderSalesId == "INV-HHN202307281" {
		return true
	}

	//if the data in db is not paid, but from pub-sub is paid, return false
	//this might be happened because the (web) publish the message before updating/inserting process finish
	//by setting to false, pub-sub will re-send again
	if result.Code == 20 && orderStatus.Status == "payment_verified" {
		return false
	}

	return result.Status || result.Code == 21 || result.Code == 20 //20: not paid, 21: already inserted, return true
}
