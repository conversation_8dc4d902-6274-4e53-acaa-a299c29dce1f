package job

import (
	"fmt"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"io/ioutil"
	"os"
	"time"
)

func WriteLog() {
	fileDest := logFilePath()

	text, err := ioutil.ReadFile("nohup.out")
	if err != nil {
		log.Warn("reading nohup.out failed... %v", err)
		return
	}

	if string(text) == "" {
		return
	}

	err = utils.CopyFile("nohup.out", fileDest)
	log.IfError(err)

	err = os.Truncate("nohup.out", 0)
	log.IfError(err)
}

func UploadSystemLog() {
	fileDest := os.Getenv("server") + "/" + logFilePath()
	file, err := os.Open("nohup.out")
	defer file.Close()
	if err != nil {
		log.Warn("reading nohup.out failed... %v", err)
		return
	}

	_, err = google.UploadFile(file, fileDest, false)

	err = os.Truncate("nohup.out", 0)
	log.IfError(err)
}

func logFilePath() string {
	_, offset := time.Now().Zone()
	diff := int64(25200 - offset) //25200 is developer offset
	timeNow := time.Unix(time.Now().Unix()+diff, 0)
	date := timeNow.Format("02_01_2006")
	dateTime := timeNow.Add(time.Minute * time.Duration(5)).Format("02_01_2006_15")
	return fmt.Sprintf("logs/api-pos/%s/%s.txt", date, dateTime)
}
