# build stage
FROM golang:alpine AS builder
#RUN apk --no-cache add build-base git bzr mercurial gcc
WORKDIR /src
COPY go.mod .
COPY go.sum .
RUN GO111MODULE=on go mod download
COPY . .
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o app-pos server.go

#final stage
# FROM alpine
FROM ghcr.io/surnet/alpine-wkhtmltopdf:3.17.0-0.12.6-full
COPY config/ /config
# COPY .env .
COPY *.env *.yml /
COPY --from=builder src/app-pos ./
ENTRYPOINT ["./app-pos"]
