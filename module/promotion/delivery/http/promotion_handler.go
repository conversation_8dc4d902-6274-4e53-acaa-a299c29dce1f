package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"gitlab.com/uniqdev/backend/api-pos/module/promotion"
)

type promotionHandler struct {
	uc promotion.UseCase
}

func NewHttpPromotionHandler(app *fasthttprouter.Router, useCase promotion.UseCase) {
	handler := &promotionHandler{useCase}
	app.GET("/v1/promotion-usage", auth.ValidateToken(handler.FetchPromotionUsage))
}

func (h promotionHandler) Sample(ctx *fasthttp.RequestCtx) {
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"message": "this is sample of promotion feature"})
}

func (h promotionHandler) FetchPromotionUsage(ctx *fasthttp.RequestCtx) {
	query := domain.PromotionUsageRequest{
		MemberId:        cast.ToInt64(ctx.QueryArgs().Peek("member_id")),
		PromoParentType: string(ctx.QueryArgs().Peek("promo_parent_type")),
	}

	result, err := h.uc.FetchPromotionUsage(query, domain.UserSessionFastHttp(ctx))
	if err != nil {
		if errWithCode, ok := err.(utils.ErrWithCode); ok {
			_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Code: errWithCode.Code, Status: false, Message: err.Error()})
			return
		}

		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: result})
}
