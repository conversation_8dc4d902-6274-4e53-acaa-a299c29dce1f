package usecase

import (
	"fmt"

	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/module/promotion"
)

type promotionUseCase struct {
	repo promotion.Repository
}

func NewPromotionUseCase(repository promotion.Repository) promotion.UseCase {
	return &promotionUseCase{repository}
}

func (p *promotionUseCase) FetchPromotionUsage(query domain.PromotionUsageRequest, user domain.UserSession) ([]domain.PromotionUsageResponse, error) {
	promoTypes, err := p.repo.FetchPromotionType(domain.PromotionTypeRequest{
		NameFormatted: query.PromoParentType,
	})

	if err != nil {
		return nil, err
	}

	if len(promoTypes) == 0 {
		return nil, fmt.Errorf("promo_parent_type not found: %s", query.PromoParentType)
	}

	query.PromoParentTypeId = promoTypes[0].PromotionTypeID
	result, err := p.repo.FetchPromotionUsage(query, user)
	log.IfError(err)
	return result, err
}
