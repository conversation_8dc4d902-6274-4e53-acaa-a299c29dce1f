package mysql

import (
	"database/sql"
	"strings"

	db "gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"gitlab.com/uniqdev/backend/api-pos/module/promotion"
)

type promotionRepository struct {
	db db.Repository
}

func NewMysqlPromotionRepository(conn *sql.DB) promotion.Repository {
	return &promotionRepository{db.Repository{Conn: conn}}
}

func (p promotionRepository) FetchPromotionType(query domain.PromotionTypeRequest) ([]models.PromotionTypeEntity, error) {
	sql := `SELECT * from promotion_types`

	whereSql := make([]string, 0)
	if query.NameFormatted != "" {
		whereSql = append(whereSql, "LOWER(REPLACE(name, ' ','_')) = @nameFormatted")
	}

	if len(whereSql) > 0 {
		sql += " WHERE " + strings.Join(whereSql, " AND ")
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"nameFormatted": query.NameFormatted,
	})

	var result []models.PromotionTypeEntity
	err := p.db.Set(sql, params...).Get(&result)
	return result, err
}

func (p promotionRepository) FetchPromotionUsage(query domain.PromotionUsageRequest, user domain.UserSession) ([]domain.PromotionUsageResponse, error) {
	sql := `SELECT count(*) as 'usage', sp.promotion_fkid as promotion_id 
	FROM sales_promotion sp 
	join sales s on s.sales_id=sp.sales_fkid
	join promotions p on p.promotion_id=sp.promotion_fkid
	join promotion_types tp on tp.promotion_type_id=p.promotion_type_id
	where tp.parent_id= @promoParentId
	and s.time_created > (UNIX_TIMESTAMP()-(86400 * COALESCE(p.maximum_redeem_period_days, 1)))*1000
	and member_fkid= @memberId
	and p.admin_fkid= @adminId
	group by sp.promotion_fkid`

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":       user.AdminId,
		"memberId":      query.MemberId,
		"promoParentId": query.PromoParentTypeId,
	})

	var result []domain.PromotionUsageResponse
	err := p.db.Set(sql, params...).Get(&result)
	return result, err
}
