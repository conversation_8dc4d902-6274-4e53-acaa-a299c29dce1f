package mysql

import (
	"reflect"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	db "gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/domain"
)

func Test_promotionRepository_FetchPromotionUsage(t *testing.T) {
	dbConn, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer dbConn.Close()

	result := []domain.PromotionUsageResponse{
		{Usage: 1, PromotionId: 1},
	}

	type fields struct {
		db db.Repository
	}
	type args struct {
		query domain.PromotionUsageRequest
		user  domain.UserSession
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []domain.PromotionUsageResponse
		wantErr bool
	}{
		{"test1", fields{db: db.Repository{Conn: dbConn}}, args{query: domain.PromotionUsageRequest{PromoParentTypeId: 1, MemberId: 1}, user: domain.UserSession{AdminId: 1}}, result, false},
	}

	rows := sqlmock.NewRows([]string{"usage", "promotion_id"}).
		AddRow(1, 1)

	mock.ExpectQuery("^SELECT .* FROM sales_promotion").WithArgs(1, 1, 1).WillReturnRows(rows)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := promotionRepository{
				db: tt.fields.db,
			}
			got, err := p.FetchPromotionUsage(tt.args.query, tt.args.user)
			if (err != nil) != tt.wantErr {
				t.Errorf("promotionRepository.FetchPromotionUsage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("promotionRepository.FetchPromotionUsage() = %v, want %v", got, tt.want)
			}
		})
	}
}
