package http

import (
	"encoding/json"
	"github.com/buaazp/fasthttprouter"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	domain "gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

type selfOrderHandler struct {
	uc domain.SelfOrderUseCase
}

func NewHttpSelfOrderHandler(app *fasthttprouter.Router, useCase domain.SelfOrderUseCase) {
	handler := &selfOrderHandler{useCase}
	app.GET("/v1/self-order/outlet/:outletId", auth.ValidateToken(handler.FetchSelfOrder))
	app.GET("/v1/transaction/self_order/:code", auth.ValidateToken(handler.FetchSelfOrderByCode))
}

func (h selfOrderHandler) FetchSelfOrder(ctx *fasthttp.RequestCtx) {
	outletId := ctx.UserValue("outletId")
	result, err := h.uc.FetchSelfOrder(utils.ToInt(outletId), domain.UserSessionFastHttp(ctx))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: result})
}

func (h selfOrderHandler) FetchSelfOrderByCode(ctx *fasthttp.RequestCtx) {
	code := ctx.UserValue("code")
	result, err := h.uc.FetchSelfOrderByCode(code, domain.UserSessionFastHttp(ctx))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: result})
}
