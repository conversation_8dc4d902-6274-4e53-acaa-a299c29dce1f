package mysql

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	domain "gitlab.com/uniqdev/backend/api-pos/domain"
	"time"
)

type selfOrderRepository struct {
	db db.Repository
}

func NewMysqlSelfOrderRepository(conn *sql.DB) domain.SelfOrderRepository {
	return &selfOrderRepository{db.Repository{Conn: conn}}
}

func (s selfOrderRepository) FetchSelfOrder(outletId int, session domain.UserSession) ([]map[string]interface{}, error) {
	query := `SELECT
    so.*,
    COALESCE(so.customer_name, m.name) AS customer_name,
    COALESCE(so.contact, m.phone) AS contact
FROM
    self_order so
LEFT JOIN members m ON
    m.member_id = so.member_fkid
WHERE
    outlet_fkid = ? AND expired_at >(UNIX_TIMESTAMP(NOW()) * 1000)
ORDER BY
    applied_at,
    time_created DESC`
	result, err := db.QueryArray(query, outletId)
	if log.IfError(err) {
		return nil, err
	}

	return result, err
}

func (s selfOrderRepository) FetchSelfOrderByCode(code interface{}, session domain.UserSession) (map[string]interface{}, error) {
	sql := `
select s.*,
       m.member_id,
       m.name as name,
       m.phone,
       p.name as customer_level
from self_order s
         left join members m on s.member_fkid = m.member_id
         left join members_detail md on m.member_id = md.member_fkid
         left join members_type mt on md.type_fkid = mt.type_id
         left join products p on mt.product_fkid = p.product_id
where order_code = ?
  and if(m.member_id > 0, md.admin_fkid = ?, true)
order by s.self_order_id desc
LIMIT 1 `

	data, err := db.Query(sql, code, session.AdminId)
	if log.IfError(err) {
		return nil, err
	}

	if len(data) == 0 {
		return nil, fmt.Errorf("kode tidak valid! data tidak ditemukan")
	}

	if !auth.ValidateOutletIds(session.AuthorizedOutletIds, utils.ToInt(data["outlet_fkid"])) {
		return nil, fmt.Errorf("order tidak dapat dilakukan di outlet ini")
	}

	if utils.ToInt64(data["expired_at"]) < (time.Now().Unix() * 1000) {
		return nil, fmt.Errorf("order sudah kadaluarsa")
	}

	go func(selfOrderId interface{}) {
		_, err := db.Update("self_order", map[string]interface{}{"applied_at": time.Now().Unix() * 1000}, "self_order_id = ?", selfOrderId)
		log.IfError(err)
	}(data["self_order_id"])

	orderList := make(map[string]interface{})
	err = json.Unmarshal([]byte(utils.ToString(data["order_item"])), &orderList)
	log.IfError(err)

	orderList["customer_name"] = data["customer_name"]
	orderList["contact"] = data["contact"]
	orderList["member"] = utils.TakesOnly(data, "member_id", "phone", "name", "customer_level")
	return orderList, nil
}
