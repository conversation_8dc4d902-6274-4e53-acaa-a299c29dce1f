package usecase

import (
	"fmt"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	domain "gitlab.com/uniqdev/backend/api-pos/domain"
)

type selfOrderUseCase struct {
	repo domain.SelfOrderRepository
}

func NewSelfOrderUseCase(repository domain.SelfOrderRepository) domain.SelfOrderUseCase {
	return &selfOrderUseCase{repository}
}

func (s selfOrderUseCase) FetchSelfOrder(outletId int, session domain.UserSession) ([]map[string]interface{}, error) {
	//validate authorization
	authorized := false
	for _, id := range session.AuthorizedOutletIds {
		if id == outletId {
			authorized = true
			break
		}
	}

	if !authorized {
		log.Warn("attempting to get self-order from outlet %d, while authorized ids is: %v", outletId, session.AuthorizedOutletIds)
		return nil, fmt.Errorf("not authorized")
	}

	return s.repo.FetchSelfOrder(outletId, session)
}

func (s selfOrderUseCase) FetchSelfOrderByCode(code interface{}, userSession domain.UserSession) (map[string]interface{}, error) {
	return s.repo.FetchSelfOrderByCode(code, userSession)
}

