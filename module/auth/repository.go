package auth

import (
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

type Repository interface {
	FetchAccount(id int, idType string) (domain.Account, error)
	FetchAccountByFilter(domain.Account) (domain.Account, error)
	FetchMultiAccount(id int) ([]map[string]interface{}, error)
	FetchOutletAccess(id interface{}, idType string) ([]map[string]interface{}, error)

	FetchEmployee(id int) (models.EmployeeEntity, error)

	AddUserSession(model models.UserSessionEntity) error
	FetchUserSession(id string) (models.UserSessionEntity, error)
	RemoveUserSession(id string) error
	UpdateDeviceStatus(deviceId string, status string) error
}
