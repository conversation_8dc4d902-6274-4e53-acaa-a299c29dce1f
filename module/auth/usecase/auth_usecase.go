package usecase

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v4"
	v2 "gitlab.com/uniqdev/backend/api-pos/controllers/v2"
	authMiddleware "gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/array"
	domain "gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"gitlab.com/uniqdev/backend/api-pos/module/auth"
)

type authUseCase struct {
	repo auth.Repository
}

func NewAuthUseCase(repository auth.Repository) auth.UseCase {
	return &authUseCase{repository}
}

func (a *authUseCase) AuthMultiAccount(user domain.UserSession, token string, adminId int) (map[string]interface{}, error) {
	//validate token
	result := make(map[string]interface{})
	adminIds, err := claimMultiAccountToken(token)
	fmt.Println("multi acc ids: ", adminIds, "request: ", adminId)
	if !array.Contain(adminIds, adminId) {
		log.Info("user request out of access, has to: %v | but request: %v", adminIds, adminId)
		return nil, fmt.Errorf("invalid admin_id")
	}

	fmt.Println("role---", user.Role, "user: ", user.Insurer)

	employeeId := ""
	outletIds := make([]string, 0)
	accountId := 0
	role := utils.ROLE_OWNER

	accountBusiness, err := a.repo.FetchAccount(int(user.AdminId), "admin")
	log.IfError(err)

	if user.Role == utils.ROLE_EMPLOYEE {
		account, err := a.repo.FetchAccount(cast.ToInt(user.Insurer), "employee")
		log.IfError(err)
		accountId = account.AccountID
		account.BusinessName = accountBusiness.BusinessName
		result["user"] = account
	} else if user.Role == utils.ROLE_OWNER {
		account := accountBusiness
		accountId = account.AccountID
		result["user"] = account
	}

	accounts, err := a.repo.FetchMultiAccount(accountId)
	log.IfError(err)

	var outletAccess []map[string]interface{}
	for _, account := range accounts {
		if utils.ToInt(account["admin_id"]) == adminId {
			if utils.ToString(account["account_type"]) == "employee" {
				employeeId = utils.ToString(account["user_id"])
				role = utils.ROLE_EMPLOYEE
				outletAccess, _ = a.repo.FetchOutletAccess(employeeId, "employee")
				log.Info("employee %v has %v outlet access", employeeId, len(outletAccess))
				employeeId = utils.Encrypt(employeeId)
			} else {
				outletAccess, _ = a.repo.FetchOutletAccess(adminId, "admin")
			}
			break
		}
	}

	log.Info("total outlet access: %v | %v", len(outletAccess), outletAccess)
	for _, outlet := range outletAccess {
		outletIds = append(outletIds, utils.ToString(outlet["outlet_id"]))
	}
	fmt.Println(strings.Join(outletIds, ","))
	auth := authMiddleware.InitJWTAuth()

	authToken := auth.GenerateToken(utils.Encrypt(utils.ToString(adminId)), employeeId, role, strings.Join(outletIds, ","))
	result["authorization"] = authToken

	return result, err
}

func claimMultiAccountToken(token string) ([]int, error) {
	tokenResult, err := jwt.Parse(token, func(t *jwt.Token) (interface{}, error) {
		if _, ok := t.Method.(*jwt.SigningMethodHMAC); !ok {
			fmt.Println("invalid signature")
			return nil, fmt.Errorf("invalid signature")
		}
		fmt.Println("valid signature")
		return []byte(v2.MultiAccountTokenSecret), nil
	})

	if err == nil && tokenResult.Valid {
		if claims, ok := tokenResult.Claims.(jwt.MapClaims); ok {
			ids := claims["ids"]
			fmt.Println(ids)
			if adminIds, ok := ids.([]interface{}); ok {
				result := make([]int, 0)
				for _, id := range adminIds {
					result = append(result, utils.ToInt(id))
				}
				return result, nil
			} else {
				fmt.Println("type is not []interface")
			}
		}
	} else {
		log.Info("invalid token: ", err)
	}
	return nil, err
}

func (a *authUseCase) SendAuthCode(email string) (domain.AuthCodeResponse, error) {
	//validate email
	if !utils.IsValidEmail(email) {
		log.Info("invalid email: %v", email)
		return domain.AuthCodeResponse{}, fmt.Errorf("invalid email address %v", email)
	}

	//email should either: admin or employee with login access
	account, err := a.repo.FetchAccountByFilter(domain.Account{Email: email})
	if log.IfError(err) {
		return domain.AuthCodeResponse{}, err
	}

	//if account is not found, return
	if account.AccountID == 0 {
		return domain.AuthCodeResponse{}, fmt.Errorf("account not found")
	}

	//TODO: if auth code has been generated, use that instead
	// a.repo.FetchUserSession()

	//validate role
	if account.AccountType == "employee" {
		employee, err := a.repo.FetchEmployee(account.UserID)
		if log.IfError(err) {
			return domain.AuthCodeResponse{}, err
		}

		log.Info("auth-code %s, statusMobile: %v", email, employee.AccessStatusMobile)
		// if employee.AccessStatusMobile != "activated" {
		// 	return domain.AuthCodeResponse{}, fmt.Errorf("mobile role is not activated")
		// }

		var mobileRole models.MobileRole
		err = json.Unmarshal([]byte(employee.RoleMobile), &mobileRole)
		log.IfError(err)

		log.Info("auth-code %s, statusMobile: %v, masterLogin: %v", email, employee.AccessStatusMobile, mobileRole.Masterlogin)
		if !mobileRole.Masterlogin {
			return domain.AuthCodeResponse{}, fmt.Errorf("account has not access")
		}
	}

	//generate code
	userSession := models.UserSessionEntity{
		ID:        fmt.Sprintf("%s%v.%d.authcode", utils.RandStringBytes(16), time.Now().UnixNano(), account.AccountID),
		Timestamp: time.Now().Unix(),
		Token:     fmt.Sprintf("%v", utils.RandomNumber(100000, 999999)),
		CreatedAt: time.Now().Unix(),
		ExpiredAt: time.Now().Add(10 * time.Minute).Unix(),
	}
	err = a.repo.AddUserSession(userSession)

	if log.IfError(err) {
		return domain.AuthCodeResponse{}, err
	}

	log.Info("auth code %s: %v", email, userSession.Token)
	//send to to email
	message := models.ScheduledMessage{
		Title:       "Login Code",
		Message:     fmt.Sprintf("Hi, <b>%s</b> <br/></br>Untuk Login ke aplikasi UNIQ POS silahkan gunakan kode berikut :</br> <h3> %v </h3> </br></br> <i>kode akan kadaluarasa dalam waktu 10 menit</i>", account.BusinessName, userSession.Token),
		TimeDeliver: time.Now().Unix() * 1000,
		DataCreated: time.Now().Unix() * 1000,
		Media:       models.MediaEmail,
		Receiver:    account.Email,
	}

	err = google.PublishMessage(message, "messaging-gateway-production")
	if err != nil {
		_, err = db.Insert("scheduled_message", array.RemoveEmpty(message.ToMap()))
		log.IfError(err)
	}

	return domain.AuthCodeResponse{
		Token:     userSession.ID,
		ExpiredAt: userSession.ExpiredAt * 1000,
	}, nil
}

func (a *authUseCase) LoginWithCode(code string, token string) (domain.LoginResponse, error) {
	//validate the code and the token
	if code == "" || token == "" {
		return domain.LoginResponse{}, fmt.Errorf("code or token can not be empty")
	}

	session, err := a.repo.FetchUserSession(token)
	if log.IfError(err) {
		return domain.LoginResponse{}, err
	}

	if session.ID == "" {
		return domain.LoginResponse{}, fmt.Errorf("code is invalid or already expired")
	}

	if session.Token != code {
		return domain.LoginResponse{}, fmt.Errorf("invalid code")
	}

	if session.ExpiredAt < time.Now().Unix() {
		return domain.LoginResponse{}, fmt.Errorf("code has already expired")
	}

	log.Info("login success, %s", code)
	err = a.repo.RemoveUserSession(token)
	log.IfError(err)

	account, err := a.repo.FetchAccountByFilter(domain.Account{AccountID: extractAccountId(token)})
	log.IfError(err)

	loginData := a.getLoginResponse(account)

	return loginData, nil
}

func extractAccountId(token string) int {
	p := `[a-zA-Z0-9]+\.([0-9]+)\.authcode`
	matchs := regexp.MustCompile(p).FindAllStringSubmatch(token, -1)
	fmt.Println(matchs)
	if len(matchs) > 0 && len(matchs[0]) > 1 {
		return cast.ToInt(matchs[0][1])
	}
	return 0
}

func (a *authUseCase) getLoginResponse(account domain.Account) domain.LoginResponse {
	var result domain.LoginResponse
	role := utils.ROLE_OWNER
	employeeId := ""

	if account.AccountType == "employee" {
		role = utils.ROLE_EMPLOYEE
		employeeId = utils.Encrypt(cast.ToString(account.UserID))
	}

	outletIdList, err := a.repo.FetchOutletAccess(account.UserID, account.AccountType)
	log.IfError(err)

	ids := make([]string, 0)
	for _, id := range outletIdList {
		ids = append(ids, cast.ToString(id["outlet_id"]))
	}

	//Generate Token
	auth := authMiddleware.InitJWTAuth()
	authToken := auth.GenerateToken(utils.Encrypt(cast.ToString(account.AdminID)), employeeId, role, strings.Join(ids, ","))
	result.Authorization = *authToken
	result.User = account

	return result
}

func (a *authUseCase) LogoutEmployee(deviceId string) error {
	return a.repo.UpdateDeviceStatus(deviceId, "off")
}
