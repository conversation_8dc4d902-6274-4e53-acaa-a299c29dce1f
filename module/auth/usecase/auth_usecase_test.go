package usecase

import (
	"reflect"
	"testing"

	v2 "gitlab.com/uniqdev/backend/api-pos/controllers/v2"
)

func Test_claimMultiAccountToken(t *testing.T) {
	token, _ := v2.GenerateTokenMultiAccount([]map[string]interface{}{
		{"admin_id": 1},
		{"admin_id": 7},
	})

	type args struct {
		token string
	}
	tests := []struct {
		name    string
		args    args
		want    []int
		wantErr bool
	}{
		{"test1", args{token: token}, []int{1, 7}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := claimMultiAccountToken(tt.args.token)
			if (err != nil) != tt.wantErr {
				t.Errorf("claimMultiAccountToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("claimMultiAccountToken() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_extractAccountId(t *testing.T) {
	token1 := `FpLSjFbcXoEFfRsW1678953362846744000.310.authcode`

	type args struct {
		token string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{"test1", args{token: token1}, 310},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := extractAccountId(tt.args.token); got != tt.want {
				t.Errorf("extractAccountId() = %v, want %v", got, tt.want)
			}
		})
	}
}
