package http

import (
	"encoding/json"
	"fmt"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	middleware "gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	domain "gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"gitlab.com/uniqdev/backend/api-pos/module/auth"
)

type authHandler struct {
	uc auth.UseCase
}

func NewHttpAuthHandler(app *fasthttprouter.Router, useCase auth.UseCase) {
	handler := &authHandler{useCase}
	app.POST("/v1/multi-account", middleware.ValidateToken(handler.AuthMultiAccount))
	app.POST("/v1/auth/send-code", middleware.ValidateKey(handler.SendAuthCode))

	//login
	app.POST("/v1/auth/with-code", middleware.ValidateKey(handler.LoginWithCode))
	app.POST("/v1/auth/logout_employee", middleware.ValidateToken(handler.LogoutEmployee))
}

func (h *authHandler) AuthMultiAccount(ctx *fasthttp.RequestCtx) {
	adminId := ctx.PostArgs().Peek("admin_id")
	token := ctx.PostArgs().Peek("token")
	result, err := h.uc.AuthMultiAccount(domain.UserSessionFastHttp(ctx), string(token), utils.ToInt((adminId)))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: result})
}

func (h *authHandler) SendAuthCode(ctx *fasthttp.RequestCtx) {
	email := ctx.PostArgs().Peek("email")
	device := string(ctx.PostArgs().Peek("device"))
	log.Info("request code %s -> %s / %s | device: %v", email, ctx.RemoteAddr().String(), utils.GetIPAdress(ctx), device)
	if string(email) == "<EMAIL>" {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false})
		headers := make(map[string]string)
		ctx.Request.Header.VisitAll(func(key, value []byte) {
			headers[string(key)] = string(value)
		})

		if headers["Cf-Ipcountry"] == "US" {
			ctx.SetStatusCode(fasthttp.StatusForbidden)
			return
		}
		log.IfError(fmt.Errorf("send auth code, from: %v | %s | device: %v", utils.GetIPAdress(ctx), utils.SimplyToJson(headers), device))
		return
	}

	result, err := h.uc.SendAuthCode(string(email))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: result})
}

func (h *authHandler) LoginWithCode(ctx *fasthttp.RequestCtx) {
	code := ctx.PostArgs().Peek("code")
	token := ctx.PostArgs().Peek("token")
	result, err := h.uc.LoginWithCode(string(code), string(token))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: result})
}

func (h *authHandler) LogoutEmployee(ctx *fasthttp.RequestCtx) {
	deviceId := ctx.FormValue("device_id")
	if string(deviceId) == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		json.NewEncoder(ctx).Encode(map[string]interface{}{
			"message": "device_id can not be empty",
		})
		return
	}

	err := h.uc.LogoutEmployee(string(deviceId))
	response := models.Response{Status: true}
	if err != nil {
		response.Status = false
		response.Message = err.Error()
	} else {
		response.Message = "Successfully updated!"
	}

	ctx.SetContentType("application/json")
	json.NewEncoder(ctx).Encode(response)
}
