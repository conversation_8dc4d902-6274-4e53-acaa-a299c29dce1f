package mysql

import (
	"database/sql"
	"strings"

	db "gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/array"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"gitlab.com/uniqdev/backend/api-pos/module/auth"
)

type authRepository struct {
	db db.Repository
}

func NewMysqlAuthRepository(conn *sql.DB) auth.Repository {
	return &authRepository{db.Repository{Conn: conn}}
}

func (a authRepository) FetchAccount(id int, idType string) (domain.Account, error) {
	sql := ``
	if idType == "employee" {
		sql = `select employee_id as user_id, e.admin_fkid as admin_id, e.name, a.business_name, 'employee' as account_type, e.account_id 
		from employee e 
		join admin a on a.admin_id=e.admin_fkid
		where e.employee_id=?
		`
	} else if idType == "admin" {
		sql = `SELECT admin_id as user_id, admin_id, name, business_name, 'admin' as account_type, account_id 
		from admin 
		where admin_id = ?`
	}

	var result domain.Account
	err := a.db.Set(sql, id).Get(&result)
	return result, err
}

func (a authRepository) FetchMultiAccount(id int) ([]map[string]interface{}, error) {
	sql := `SELECT admin_id as user_id, admin_id, name, business_name, 'admin' as account_type 
	from admin where account_id= @accountId
	UNION 
	select employee_id, e.admin_fkid,e.name, a.business_name, 'employee' from employee e 
	join admin a on a.admin_id=e.admin_fkid
	 where e.account_id= @accountId`

	sql, params := db.MapParam(sql, map[string]interface{}{
		"accountId": id,
	})
	return db.QueryArray(sql, params...)
}

func (a authRepository) FetchOutletAccess(id interface{}, idType string) ([]map[string]interface{}, error) {
	sql := ``
	if idType == "admin" {
		sql = `SELECT outlet_id from outlets where admin_fkid = ?`
	} else {
		sql = `SELECT outlet_fkid as outlet_id from employee_outlet where employee_fkid = ?`
	}
	return db.QueryArray(sql, id)
}

func (a authRepository) FetchAccountByFilter(filter domain.Account) (domain.Account, error) {
	sql := `SELECT COALESCE(ad.admin_id, e.employee_id) as user_id, COALESCE(ad.admin_id, e.admin_fkid) as admin_id, 
	COALESCE(ad.name, e.name) as name, 
	COALESCE(ad.business_name, ad_e.business_name) as business_name,
	if(ad.admin_id is null, 'employee', 'admin') as account_type,
	a.id as account_id, COALESCE(ad.email, e.email) as email
	from accounts a 
	left join admin ad on ad.account_id=a.id
	left join employee e on e.account_id=a.id
	left join admin ad_e on ad_e.admin_id=e.admin_fkid
	 where `

	whereSql := make([]string, 0)
	if filter.Email != "" {
		whereSql = append(whereSql, "a.email = @email")
	}
	if filter.AccountID != 0 {
		whereSql = append(whereSql, "a.id = @accountId")
	}

	sql += strings.Join(whereSql, " AND ")

	var params []interface{}
	sql, params = db.MapParam(sql, map[string]interface{}{
		"email":     filter.Email,
		"accountId": filter.AccountID,
	})

	var result domain.Account
	err := a.db.Set(sql, params...).Get(&result)
	return result, err
}

func (a authRepository) FetchEmployee(id int) (models.EmployeeEntity, error) {
	sql := `select * from employee where employee_id = ?`
	var result models.EmployeeEntity
	err := a.db.Set(sql, id).Get(&result)
	return result, err
}

func (a authRepository) AddUserSession(model models.UserSessionEntity) error {
	_, err := db.Insert("users_session", array.RemoveEmpty(model.ToMap()))
	return err
}

func (a authRepository) FetchUserSession(id string) (models.UserSessionEntity, error) {
	var result models.UserSessionEntity
	err := a.db.Set(`select * from users_session where id =?`, id).Get(&result)
	return result, err
}

func (a authRepository) RemoveUserSession(id string) error {
	_, err := db.Delete("users_session", "id = ?", id)
	return err
}

func (a authRepository) UpdateDeviceStatus(deviceId string, status string) error {
	_, err := db.Update("devices", map[string]interface{}{
		"device_status": status,
	}, "imei = ?", deviceId)
	return err
}
