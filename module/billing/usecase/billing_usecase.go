package usecase

import (
	"fmt"
	"os"

	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	billing "gitlab.com/uniqdev/backend/api-pos/module/billing"
)

type billingUseCase struct {
	repo billing.Repository
}

func NewBillingUseCase(repository billing.Repository) billing.UseCase {
	return &billingUseCase{repository}
}

func (uc *billingUseCase) CreateBilling(billing []domain.BillingCreateRequest, user domain.UserSession, token string) (domain.Billing, error) {
	// Create the map from the array of BillingCreateRequest
	details := make([]map[string]interface{}, len(billing))
	for i, b := range billing {
		details[i] = map[string]interface{}{
			"sys_service_fkid": b.ServiceId,
			"qty":              b.Qty,
			"service_period":   b.ServicePeriod,
		}
	}

	billingMap := map[string]interface{}{
		"detail": details,
	}

	req := utils.HttpRequest{
		Method: "POST",
		Url:    fmt.Sprintf("%s/v1/subscription", os.Getenv("API_BILLING")),
		Header: map[string]interface{}{
			"Authorization": token,
		},
		PostRequest: utils.PostRequest{
			Body: billingMap,
		},
	}

	resp, err := req.Execute()
	log.IfError(err)
	log.Info("resp ---> ", resp)
	return domain.Billing{}, nil
}
