package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	billing "gitlab.com/uniqdev/backend/api-pos/module/billing"
)

type billingHandler struct {
	uc billing.UseCase
}

func NewHttpBillingHandler(app *fasthttprouter.Router, useCase billing.UseCase) {
	handler := &billingHandler{useCase}
	app.POST("/v2/billing", auth.ValidateToken(handler.CreateBilling))
}

func (h *billingHandler) Sample(ctx *fasthttp.RequestCtx) {
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"message": "this is sample of billing feature"})
}

func (h *billingHandler) CreateBilling(ctx *fasthttp.RequestCtx) {
	token := ctx.Request.Header.Peek("Authorization")
	user := domain.UserSessionFastHttp(ctx)
	var billing []domain.BillingCreateRequest
	err := json.Unmarshal(ctx.Request.Body(), &billing)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusUnprocessableEntity)
		return
	}
	result, err := h.uc.CreateBilling(billing, user, string(token))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"message": result})
}
