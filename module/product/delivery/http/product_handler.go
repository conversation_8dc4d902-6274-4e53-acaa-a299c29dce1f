package http

import (
	"encoding/json"
	"fmt"
	"os"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	domain "gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"gitlab.com/uniqdev/backend/api-pos/module/product"
)

type productHandler struct {
	uc product.UseCase
}

func NewHttpProductHandler(app *fasthttprouter.Router, useCase product.UseCase) {
	handler := &productHandler{useCase}

	app.GET("/v2/product", handler.FetchProduct)
	app.POST("/v2/product", auth.ValidateToken(handler.AddProductBatch))

	app.GET("/product-reset/request/:email", handler.RequestDeleteAllProduct)
	app.GET("/product-reset/confirm/:email/:key", handler.DeleteAllProduct)

	//ai
	app.POST("/v1/extract-menu", auth.ValidateToken(handler.ExtractMenu)) //
	// app.POST("/v1/extract-menu-test", (handler.ExtractMenu))
	app.POST("/v1/product/generate-image", auth.ValidateToken(handler.GenerateProductImage))

	//unit
	app.GET("/v2/unit", handler.FetchUnit)

	app.PUT("/v1/product/:id/photo", auth.ValidateToken(handler.UpdateProductPhoto))
}

func (h *productHandler) AddProductBatch(ctx *fasthttp.RequestCtx) {
	var requestBody domain.ProductAddRequest
	err := json.Unmarshal(ctx.PostBody(), &requestBody)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: fmt.Sprintf("invalid body: %v", err.Error())})
		return
	}

	_, err = h.uc.AddProduct(requestBody, domain.UserSessionFastHttp(ctx))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success"})
}

func (h *productHandler) FetchProduct(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)

	filter := models.RequestFilter{
		OutletId: utils.ToInt(ctx.QueryArgs().Peek("outlet_id")),
		LastSync: utils.ToInt64(ctx.QueryArgs().Peek("last_sync"))}

	result, err := h.uc.FetchProduct(filter, user)
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(err.Error())
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: result})
}

func (h *productHandler) DeleteAllProduct(ctx *fasthttp.RequestCtx) {
	key := utils.ToString(ctx.UserValue("key"))
	email := utils.ToString(ctx.UserValue("email"))

	log.Info("confirm delete all products of: '%s' with key: %s", email, key)
	err := h.uc.DeleteAllProduct(email, key)
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(err.Error())
		return
	}

	_ = json.NewEncoder(ctx).Encode("data product berhasil di reset")
}

func (h *productHandler) RequestDeleteAllProduct(ctx *fasthttp.RequestCtx) {
	email := utils.ToString(ctx.UserValue("email"))
	log.Info("request delete all products of: '%s'", email)
	err := h.uc.RequestDeleteAllProduct(email)
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(err.Error())
		return
	}

	_ = json.NewEncoder(ctx).Encode("untuk reset menu anda, klik link yang telah kami kirimkan via email (link akan kadaluarsa dalam waktu 1 jam)")
}

// /fetch unit
func (h *productHandler) FetchUnit(ctx *fasthttp.RequestCtx) {
	param := domain.RequestParam{
		OutletId: cast.ToInt(ctx.QueryArgs().Peek("outlet_id")),
		LastSync: cast.ToInt64(ctx.QueryArgs().Peek("last_sync")),
	}
	user := domain.UserSessionFastHttp(ctx)
	user.AdminId = 1
	result, err := h.uc.FetchUnit(&param, &user)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(err.Error())
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: result})
}

func (h *productHandler) ExtractMenu(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	imgFileName := fmt.Sprintf("%v_%v.jpg", user.AdminId, utils.CurrentMillis())
	tmpFileName := fmt.Sprintf("%s/%s", utils.GetTmpDir("product"), imgFileName)

	tempFile, err := os.CreateTemp("", "product_*.jpg")
	if !log.IfError(err) {
		tmpFileName = tempFile.Name()
	}

	fileBase64Byte := ctx.FormValue("file")
	log.Info("use base64 image: %v", fileBase64Byte != nil)

	if fileBase64Byte != nil {
		err := utils.DownloadFileBase64(string(fileBase64Byte), tmpFileName)
		if log.IfError(err) {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
			_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
			return
		}

	} else {
		fileHeader, err := ctx.FormFile("file")
		if err != nil {
			ctx.SetStatusCode(fasthttp.StatusBadRequest)
			_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
			return
		}

		err = fasthttp.SaveMultipartFile(fileHeader, tmpFileName)
		if err != nil {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
			_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
			return
		}
	}

	log.Info("extract menu, file downloaded at: %v", tmpFileName)
	result, err := h.uc.ExtractMenu(tmpFileName, user)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(err.Error())
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: result})
}

func (h *productHandler) UpdateProductPhoto(ctx *fasthttp.RequestCtx) {
	productId := cast.ToInt64(ctx.UserValue("id"))
	user := domain.UserSessionFastHttp(ctx)

	fileHeader, err := ctx.FormFile("file")
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "No file uploaded"})
		return
	}

	tempFile, err := os.CreateTemp("", "product_*.jpg")
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "Failed to process file"})
		return
	}
	defer os.Remove(tempFile.Name())

	if err := fasthttp.SaveMultipartFile(fileHeader, tempFile.Name()); err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "Failed to save file"})
		return
	}

	photoUrl, err := h.uc.UpdateProductPhoto(productId, tempFile.Name(), user)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{
		Status:  true,
		Message: "Product photo updated successfully",
		Data: map[string]interface{}{
			"image_url": photoUrl,
		},
	})
}

func (h *productHandler) GenerateProductImage(ctx *fasthttp.RequestCtx) {
	var request domain.GenerateProductImageRequest
	if err := json.Unmarshal(ctx.PostBody(), &request); err != nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		json.NewEncoder(ctx).Encode(map[string]interface{}{
			"error": "Invalid request body: " + err.Error(),
		})
		return
	}

	// Validate required fields
	if request.Name == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		json.NewEncoder(ctx).Encode(map[string]interface{}{
			"error": "Product name is required",
		})
		return
	}

	result, err := h.uc.GenerateProductImage(request)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		json.NewEncoder(ctx).Encode(map[string]interface{}{
			"error": "Failed to generate image: " + err.Error(),
		})
		return
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
	ctx.Response.Header.Set("Content-Type", "application/json")
	json.NewEncoder(ctx).Encode(models.ResponseAny{
		Status:  true,
		Message: "Image generated successfully",
		Data:    result})
}
