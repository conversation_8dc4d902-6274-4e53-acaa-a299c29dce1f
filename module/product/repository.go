package product

import (
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

type Repository interface {
	AddProduct(adminId int64, products ...models.ProductWithDetailAndVariant) ([]int64, error)
	AddProductCategory(adminId int, names ...string) (map[string]int64, error)
	AddSubcategory(adminId int64, names ...string) (map[string]int64, error) //AddPurchaseReportCategory
	AddPurchaseReportCategory(adminId int64, names ...string) (map[string]int64, error)
	AddProductType(adminId int64, names ...string) (map[string]int64, error)
	FetchOrAddUnit(adminId int64, names ...string) (map[string]int, error)

	FetchProduct(filter models.RequestFilter, user domain.UserSession) ([]models.ProductWithDetailAndVariant, error)
	FetchProductByDetailIdSimple(ids ...interface{}) ([]map[string]interface{}, error)
	FetchMasterProductByName(adminId int, name ...string) ([]map[string]interface{}, error)

	FetchAdminByEmail(email string) (map[string]interface{}, error)
	AddUsersKey(data map[string]interface{}) (int64, error)
	FetchUserKey(email string, key string) (map[string]interface{}, error)
	DeleteAllProductSoftly(adminId int) error
	UpdateUserKey(keyId int, data map[string]interface{}) error
	FetchActiveUserKeysByEmail(email string, filterKeyType string) ([]map[string]interface{}, error)

	//unit
	FetchUnit(param *domain.RequestParam, user *domain.UserSession) ([]*models.UnitEntity, error)

	UpdateProductPhoto(productId int64, photoUrl string, adminId int64) error
}
