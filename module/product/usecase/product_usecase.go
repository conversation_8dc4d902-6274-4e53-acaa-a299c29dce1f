package usecase

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/uploader"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/array"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/file"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"gitlab.com/uniqdev/backend/api-pos/module/product"
)

type productUseCase struct {
	repo product.Repository
}

func NewProductUseCase(repository product.Repository) product.UseCase {
	return &productUseCase{repository}
}

// AddProduct implements product.UseCase.
func (p *productUseCase) AddProduct(request domain.ProductAddRequest, user domain.UserSession) ([]models.ProductWithDetailAndVariant, error) {
	//validation
	if request.OutletId == 0 {
		return nil, fmt.Errorf("outlet_id can not be empty")
	}

	categoryDataMap := make(map[string]int)
	subCategoryDataMap := make(map[string]int)
	purchaseRepCategoryDataMap := make(map[string]int)
	productTypeDataMap := make(map[string]int)
	unitDataMap := make(map[string]int)

	//use default unit
	unitDataMap["item"] = 0

	categoryMap := make(map[string]bool)
	for i, data := range request.Products {
		if strings.TrimSpace(data.CategoryName) == "" {
			return nil, fmt.Errorf("category_name at %v for %v can not be empty", i, data.Name)
		}
		if strings.TrimSpace(data.Name) == "" {
			return nil, fmt.Errorf("name is empty at index %v", i)
		}

		categoryMap[data.CategoryName] = true
		categoryDataMap[data.CategoryName] = 0
		subCategoryDataMap[utils.TakeNotEmpty(data.SubCategoryName, data.CategoryName)] = 0
		purchaseRepCategoryDataMap[utils.TakeNotEmpty(data.PurchaseReportCategoryName, data.CategoryName)] = 0
		productTypeDataMap[utils.TakeNotEmpty(data.ProductTypeName, data.CategoryName)] = 0
	}

	categoryNames := array.GetKeys(categoryMap)

	masterData, err := p.repo.FetchMasterProductByName(int(user.AdminId), categoryNames...)
	log.IfError(err)
	log.Info("maser data: %v | category: %v ", utils.SimplyToJson(masterData), categoryNames)

	for _, data := range masterData {
		id := cast.ToInt(data["id"])
		name := cast.ToString(data["name"])

		switch tableType := cast.ToString(data["table_type"]); tableType {
		case "products_category":
			categoryDataMap[name] = id
		case "products_subcategory":
			subCategoryDataMap[name] = id
		case "purchase_report_category":
			purchaseRepCategoryDataMap[name] = id
		case "products_type":
			productTypeDataMap[name] = id
		}
	}

	var wg sync.WaitGroup

	//find master data doens exist
	//category
	wg.Add(1)
	go func(data map[string]int, wg *sync.WaitGroup) {
		defer wg.Done()
		unsetData := array.GetZeroKeys(data)
		log.Info("unset for category: %v", unsetData)
		newData, err := p.repo.AddProductCategory(int(user.AdminId), unsetData...)
		log.IfError(err)
		for k, v := range newData {
			data[k] = int(v)
		}
	}(categoryDataMap, &wg)

	//subcategory
	wg.Add(1)
	go func(data map[string]int, wg *sync.WaitGroup) {
		defer wg.Done()
		unsetData := array.GetZeroKeys(data)
		log.Info("unset for subcategory: %v", unsetData)
		newData, err := p.repo.AddSubcategory(user.AdminId, unsetData...)
		log.IfError(err)
		for k, v := range newData {
			data[k] = int(v)
		}
	}(subCategoryDataMap, &wg)

	//purchase_report_category
	wg.Add(1)
	go func(data map[string]int, wg *sync.WaitGroup) {
		defer wg.Done()
		unsetData := array.GetZeroKeys(data)
		log.Info("unset for purchase_report_category: %v", unsetData)
		newData, err := p.repo.AddPurchaseReportCategory(user.AdminId, unsetData...)
		log.IfError(err)
		for k, v := range newData {
			data[k] = int(v)
		}
	}(purchaseRepCategoryDataMap, &wg)

	//products_type
	wg.Add(1)
	go func(data map[string]int, wg *sync.WaitGroup) {
		defer wg.Done()
		unsetData := array.GetZeroKeys(data)
		log.Info("unset for products_type: %v", unsetData)
		newData, err := p.repo.AddProductType(user.AdminId, unsetData...)
		log.IfError(err)
		for k, v := range newData {
			data[k] = int(v)
		}
	}(productTypeDataMap, &wg)

	//unit
	defaultUnitName := "item"
	units, err := p.repo.FetchOrAddUnit(user.AdminId, defaultUnitName)
	log.IfError(err)

	wg.Wait()

	//mapping data
	productList := make([]models.ProductWithDetailAndVariant, 0)
	for _, req := range request.Products {
		productList = append(productList, models.ProductWithDetailAndVariant{
			ProductEntity: models.ProductEntity{
				Name:                       req.Name,
				ProductCategoryFkid:        categoryDataMap[req.CategoryName],
				ProductSubcategoryFkid:     subCategoryDataMap[utils.TakeNotEmpty(req.SubCategoryName, req.CategoryName)],
				PurchaseReportCategoryFkid: purchaseRepCategoryDataMap[utils.TakeNotEmpty(req.PurchaseReportCategoryName, req.CategoryName)],
				ProductTypeFkid:            productTypeDataMap[utils.TakeNotEmpty(req.ProductTypeName, req.CategoryName)],
				UnitFkid:                   units[defaultUnitName],
				StockManagement:            req.StockManagement,
				Sku:                        req.Sku,
				Barcode:                    req.Barcode,
				AdminFkid:                  int(user.AdminId),
			},
			ProductDetailEntity: models.ProductDetailEntity{
				PriceSell:  req.Price,
				PriceBuy:   req.PriceBuy,
				OutletFkid: request.OutletId,
			},
		})
	}

	ids, err := p.repo.AddProduct(user.AdminId, productList...)
	if log.IfError(err) {
		return nil, err
	}

	log.Info("products added, %v becomes %v, ids %v", len(request.Products), len(ids), utils.SimplyToJson(ids))
	productsAdded, err := p.repo.FetchProduct(models.RequestFilter{Ids: ids}, user)
	return productsAdded, err
}

func (p *productUseCase) FetchProduct(filter models.RequestFilter, user domain.UserSession) ([]models.ProductWithDetailAndVariant, error) {
	products, err := p.repo.FetchProduct(filter, user)
	fmt.Println(len(products))
	return nil, err
}

func (p *productUseCase) RequestDeleteAllProduct(email string) error {
	admin, err := p.repo.FetchAdminByEmail(email)
	if err != nil {
		return err
	}

	if len(admin) == 0 {
		return errors.New("email tidak terdaftar! Pastikan anda menggunakan email admin")
	}

	secretKey := utils.Encrypt(email)
	id, err := p.repo.AddUsersKey(map[string]interface{}{
		"key_type":     "resetdata",
		"email":        email,
		"user_level":   "admin",
		"secret_key":   secretKey,
		"data_created": time.Now().Unix() * 1000,
		"data_expired": time.Now().Add(2*time.Hour).Unix() * 1000,
	})

	if err != nil {
		return errors.New("internal server error")
	}

	log.Info("users_key inserted with id: %v", id)

	emailKey, err := utils.HashPassword(secretKey)
	if log.IfError(err) {
		return errors.New("internal server error")
	}

	emailKey = url.QueryEscape(emailKey)
	resetLink := fmt.Sprintf("%s/product-reset/confirm/%s/%s", utils.BaseUrl(), email, emailKey)
	msg := "Untuk menghapus semua menu yang kamu miliki klik link di bawah ini. \n\n" +
		"NB: Setelah di reset, menu tidak dapat dikembalikan lagi. \n" +
		"LINK RESET MENU: " + resetLink + "\n\n\n" +
		"link akan kadaluarsa dalam waktu 1 jam"
	fmt.Println(msg)

	//sending email to reset data
	scheduledMessage := map[string]interface{}{
		"title":        "Reset Product Catalogue",
		"message":      msg,
		"media":        "email",
		"time_deliver": time.Now().Unix() * 1000,
		"data_created": time.Now().Unix() * 1000,
		"receiver":     email,
	}
	err = google.PublishMessage(scheduledMessage, "messaging-gateway-production")
	return err
}

func (p *productUseCase) DeleteAllProduct(email string, key string) error {
	userKeys, err := p.repo.FetchActiveUserKeysByEmail(email, "resetdata")
	if err != nil {
		return err
	}

	if len(userKeys) == 0 {
		log.IfError(fmt.Errorf("users key not found with email: [%s] and key: [%s]", email, key))
		return errors.New("invalid link, mungkin sudah kadaluarsa")
	}

	//if utils.ToString(userKey["key_type"]) != "resetdata" || utils.ToInt(userKey["status"]) == 0 {
	//	log.Info("key_type is: %s | status : %d", userKey["key_type"], userKey["status"])
	//	return errors.New("invalid link")
	//}
	//
	//if utils.ToInt64(userKey["data_expired"]) < time.Now().Unix()*1000 {
	//	log.Info("link expired at: %v", userKey["data_expired"])
	//	return errors.New("link sudah kadaluarsa")
	//}

	log.Info("total found: %d", len(userKeys))
	userKeyFound := make(map[string]interface{})
	for _, userKey := range userKeys {
		if utils.CheckPasswordHash(utils.ToString(userKey["secret_key"]), key) {
			userKeyFound = userKey
			break
		}
	}

	if len(userKeyFound) == 0 {
		log.Info("no match secret_key")
		return errors.New("invalid link")
	}

	admin, err := p.repo.FetchAdminByEmail(email)
	err = p.repo.DeleteAllProductSoftly(utils.ToInt(admin["admin_id"]))

	if err != nil {
		return errors.New("internal server error")
	}

	err = p.repo.UpdateUserKey(utils.ToInt(userKeyFound["key_id"]), map[string]interface{}{
		"status": 0,
	})
	return err
}

// ExtractMenu implements product.UseCase.
func (p *productUseCase) ExtractMenu(filePath string, user domain.UserSession) ([]map[string]interface{}, error) {
	///validation
	//check if file exist
	if !file.Exists(filePath) {
		return nil, fmt.Errorf("failed to download file")
	}

	var logs strings.Builder
	defer func() {
		log.Info(logs.String())
		imageUrl, err := uploader.UploadImageKit(filePath)
		if err == nil {
			fmt.Printf("ExtractMenu, imageUrl: %s | log: %s\n", imageUrl, logs.String())
		}
	}()

	logs.WriteString(fmt.Sprintf("file size of %s: %d bytes\n", filePath, file.Size(filePath)))

	log.Info("try resizing image...")
	err := file.ResizeImageToSize(filePath, 700)
	log.IfError(err)

	// Get file size in bytes
	log.Info("file size after resize of %s: %d bytes\n", filePath, file.Size(filePath))

	log.Info("getting ocr from %v", filePath)
	resp, err := ocrMenu(filePath)
	if log.IfError(err) {
		logs.WriteString(fmt.Sprintf("error ocrMenu: %v", err))
		return nil, err
	}
	log.Info("menu : '%s'", resp)
	logs.WriteString(fmt.Sprintf("menu : '%s'", resp))
	if string(resp) == "" {
		return []map[string]interface{}{}, nil
	}

	var result []map[string]interface{}
	err = json.Unmarshal(resp, &result)
	if log.IfError(err) {
		logs.WriteString(fmt.Sprintf("error unmarshal: %v", err))
		return nil, err
	}

	//format data
	for i, row := range result {
		result[i]["price"] = utils.ParseCurrencyToInt(cast.ToString(row["price"]))
	}
	log.Info("final result menu: %s", utils.SimplyToJson(result))
	logs.WriteString(fmt.Sprintf("final result menu: %s", utils.SimplyToJson(result)))
	return result, err
}

func ocrMenu(filePath string) ([]byte, error) {
	req := utils.HttpRequest{
		Method: "POST",
		Url:    "https://api-ai-nlp.uniq.id/ocr/v1/menu", //https://api-ai-nlp-5ssxrjgk6q-as.a.run.app
		MultipartRequest: utils.MultipartRequest{
			FilePath:  filePath,
			FileParam: "file",
		},
	}
	return req.Execute()
}

func (p *productUseCase) UpdateProductPhoto(productId int64, filePath string, user domain.UserSession) (string, error) {
	if productId == 0 {
		return "", fmt.Errorf("product_id cannot be empty")
	}

	// Upload the image to ImageKit
	imageUrl, err := uploader.UploadImageKit(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to upload image: %v", err)
	}

	// Update the product with the new image URL
	err = p.repo.UpdateProductPhoto(productId, imageUrl, user.AdminId)
	if err != nil {
		return "", err
	}

	return imageUrl, nil
}

func (p *productUseCase) GenerateProductImage(request domain.GenerateProductImageRequest) (*domain.GenerateProductImageResponse, error) {
	baseURL := "https://api-ai-nlp.uniq.id"
	if envURL := os.Getenv("API_AI_NLP_BASE_URL"); envURL != "" {
		baseURL = envURL
	}
	endpoint := fmt.Sprintf("%s/v1/images/generate-product", baseURL)

	req := utils.HttpRequest{
		Method: "POST",
		Url:    endpoint,
		Header: map[string]interface{}{
			"Content-Type": "application/json",
			"Accept":       "application/json",
		},
		PostRequest: utils.PostRequest{
			Body: request,
		},
	}

	resp, err := req.Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to generate image: %v", err)
	}

	var result domain.GenerateProductImageResponse
	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	return &result, nil
}
