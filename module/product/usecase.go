package product

import (
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

type UseCase interface {
	AddProduct(request domain.ProductAddRequest, user domain.UserSession) ([]models.ProductWithDetailAndVariant, error)
	FetchProduct(filter models.RequestFilter, user domain.UserSession) ([]models.ProductWithDetailAndVariant, error)

	RequestDeleteAllProduct(email string) error
	DeleteAllProduct(email string, key string) error

	//unit
	FetchUnit(param *domain.RequestParam, user *domain.UserSession) ([]*models.UnitEntity, error)

	//ai
	ExtractMenu(filePath string, user domain.UserSession) ([]map[string]interface{}, error)

	UpdateProductPhoto(productId int64, filePath string, user domain.UserSession) (string, error)

	// Generate AI image for product
	GenerateProductImage(request domain.GenerateProductImageRequest) (*domain.GenerateProductImageResponse, error)
}
