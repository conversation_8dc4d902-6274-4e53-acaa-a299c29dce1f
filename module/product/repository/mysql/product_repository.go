package mysql

import (
	"database/sql"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	db "gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/array"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"gitlab.com/uniqdev/backend/api-pos/module/product"
)

type productRepository struct {
	db db.Repository
}

func NewMysqlProductRepository(conn *sql.DB) product.Repository {
	return &productRepository{db.Repository{Conn: conn}}
}

// FetchOrAddUnit implements product.Repository.
func (p *productRepository) FetchOrAddUnit(adminId int64, names ...string) (map[string]int, error) {
	sql := "select * from unit where name in @names and admin_fkid = @adminId"
	sql, params := db.MapParam(sql, map[string]interface{}{
		"names":   names,
		"adminId": adminId,
	})
	existingData, err := db.QueryArray(sql, params...)
	log.IfError(err)

	dataMap := make(map[string]int)
	for _, name := range names {
		dataMap[name] = 0
	}

	for _, exist := range existingData {
		dataMap[cast.ToString(exist["name"])] = cast.ToInt(exist["unit_id"])
	}

	newNames := array.GetZeroKeys(dataMap)
	for _, name := range newNames {
		res, err := db.Insert("unit", map[string]interface{}{
			"name":          name,
			"admin_fkid":    adminId,
			"data_created":  utils.CurrentMillis(),
			"data_modified": utils.CurrentMillis(),
			"data_status":   "on",
		})
		if !log.IfError(err) {
			id, _ := res.LastInsertId()
			dataMap[name] = int(id)
		}
	}

	return dataMap, nil
}

// AddProductType implements product.Repository.
func (p *productRepository) AddProductType(adminId int64, names ...string) (map[string]int64, error) {
	idMap := make(map[string]int64)
	err := db.WithTransaction(func(t db.Transaction) error {
		for _, name := range names {
			res, _ := t.Insert("products_type", map[string]interface{}{
				"name":          name,
				"admin_fkid":    adminId,
				"data_created":  utils.CurrentMillis(),
				"data_modified": utils.CurrentMillis(),
				"data_status":   "on",
			})
			id, _ := res.LastInsertId()
			idMap[name] = id
		}
		return nil
	})
	return idMap, err
}

// AddPurchaseReportCategory implements product.Repository.
func (p *productRepository) AddPurchaseReportCategory(adminId int64, names ...string) (map[string]int64, error) {
	idMap := make(map[string]int64)
	err := db.WithTransaction(func(t db.Transaction) error {
		for _, name := range names {
			res, _ := t.Insert("purchase_report_category", map[string]interface{}{
				"name":          name,
				"admin_fkid":    adminId,
				"data_created":  utils.CurrentMillisFormat("2006-01-02 15:04:05", 25200),
				"data_modified": utils.CurrentMillisFormat("2006-01-02 15:04:05", 25200),
				"data_status":   "on",
			})
			id, _ := res.LastInsertId()
			idMap[name] = id
		}
		return nil
	})
	return idMap, err
}

// AddProduct implements product.Repository.
func (p *productRepository) AddProduct(adminId int64, products ...models.ProductWithDetailAndVariant) ([]int64, error) {
	ids := make([]int64, 0)
	err := db.WithTransaction(func(t db.Transaction) error {
		for _, product := range products {
			productMap := array.RemoveEmpty(product.ProductEntity.ToMap())
			delete(productMap, "product_id") //make sure id is auto increment
			res, _ := t.Insert("products", productMap)
			id, _ := res.LastInsertId()

			product.ProductDetailEntity.ProductFkid = int(id)
			productDetailMap := array.RemoveEmpty(product.ProductDetailEntity.ToMap())
			delete(productDetailMap, "product_detail_id")
			t.Insert("products_detail", productDetailMap)

			ids = append(ids, id)
		}
		return nil
	})
	return ids, err
}

// FetchProductByIdSimple implements product.Repository.
func (p *productRepository) FetchProductByDetailIdSimple(ids ...interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT CONCAT(p.name, COALESCE(concat(' (', pdv.variant_name, ')'), '')) as name, 
p.product_id, pd.product_detail_id, pdv.variant_id, p.product_subcategory_fkid, p.product_category_fkid
 from products p 
 join products_detail pd on pd.product_fkid=p.product_id
left join products_detail_variant pdv on p.product_id=pdv.product_fkid
where pd.product_detail_id in @ids `
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": ids,
	})
	return db.QueryArray(sql, params...)
}

func (p productRepository) FetchProduct(filter models.RequestFilter, user domain.UserSession) ([]models.ProductWithDetailAndVariant, error) {
	sql := `SELECT *
		FROM products p
			JOIN products_detail pd ON p.product_id = pd.product_fkid
			left join products_detail_variant pdv on pd.variant_fkid = pdv.variant_id
		WHERE outlet_fkid = @outletId
		AND (p.data_modified >= @lastSync OR pd.data_modified >= @lastSync ) 
		$WHERE`

	var sqlWhere strings.Builder
	if len(filter.Ids) > 0 {
		sqlWhere.WriteString(" AND p.product_id in @ids ")
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"outletId": filter.OutletId,
		"lastSync": filter.LastSync,
		"ids":      filter.Ids,
		"WHERE":    sqlWhere.String(),
	})

	var result []models.ProductWithDetailAndVariant
	err := p.db.Set(sql, params...).Get(&result)
	return result, err
}

func (p productRepository) FetchAdminByEmail(email string) (map[string]interface{}, error) {
	return db.Query("select * from admin where email = ?", email)
}

func (p productRepository) AddUsersKey(data map[string]interface{}) (int64, error) {
	result, err := db.Insert("users_key", data)
	if err != nil {
		return 0, err
	}

	return result.LastInsertId()
}

func (p productRepository) FetchUserKey(email string, key string) (map[string]interface{}, error) {
	return db.Query("select * from users_key where email = ? and secret_key = ?", email, key)
}

func (p productRepository) FetchActiveUserKeysByEmail(email string, filterKeyType string) ([]map[string]interface{}, error) {
	return db.QueryArray("select * from users_key where email = ? and key_type = ? and status = ? and data_expired > ?",
		email, filterKeyType, 1, time.Now().Unix()*1000)
}

func (p productRepository) DeleteAllProductSoftly(adminId int) error {
	result, err := db.Update("products", map[string]interface{}{
		"data_status":   "off",
		"data_modified": time.Now().Unix() * 1000,
	}, "admin_fkid = ?", adminId)

	if err == nil {
		totalRowUpdate, _ := result.RowsAffected()
		log.Info("total products row updated: %d", totalRowUpdate)
	}
	return err
}

func (p productRepository) UpdateUserKey(keyId int, data map[string]interface{}) error {
	_, err := db.Update("users_key", data, "key_id = ?", keyId)
	return err
}

// unit
func (p productRepository) FetchUnit(param *domain.RequestParam, user *domain.UserSession) ([]*models.UnitEntity, error) {
	var result []*models.UnitEntity
	sql := "SELECT * FROM unit WHERE admin_fkid=? and data_modified >= ? limit 1"
	err := p.db.Set(sql, user.AdminId, param.LastDataModified).Get(&result)
	return result, err
}

// FetchMasterProductByName implements product.Repository.
func (p *productRepository) FetchMasterProductByName(adminId int, name ...string) ([]map[string]interface{}, error) {
	sql := `
SELECT product_category_id as id, name, 'products_category' as table_type  
from products_category where name in @names and admin_fkid=@adminId and data_type='product'
union 
SELECT product_subcategory_id, name, 'products_subcategory' from products_subcategory 
where data_type='product' and admin_fkid=@adminId and name in @names
union 
SELECT purchase_report_category_id, name, 'purchase_report_category' from purchase_report_category 
where admin_fkid=@adminId and name in @names
union 
SELECT unit_id, name, 'unit' from unit where admin_fkid=@adminId and name in @names
union 
SELECT products_type_id, name, 'products_type' from products_type 
where admin_fkid=@adminId and name in @names
	`

	sql, params := db.MapParam(sql, map[string]interface{}{
		"names":   name,
		"adminId": adminId,
	})

	return db.QueryArray(sql, params...)
}

// UpdateProductPhoto implements product.Repository
func (p *productRepository) UpdateProductPhoto(productId int64, photoUrl string, adminId int64) error {
	_, err := db.Update("products", map[string]interface{}{
		"photo":         photoUrl,
		"data_modified": utils.CurrentMillis(),
	}, "product_id = ? and admin_fkid = ?", productId, adminId)
	return err
}
