package mysql

import (
	db "gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
)

// AddProductCategory implements product.Repository.
func (p *productRepository) AddProductCategory(adminId int, names ...string) (map[string]int64, error) {
	idMap := make(map[string]int64)
	err := db.WithTransaction(func(t db.Transaction) error {
		for _, name := range names {
			res, _ := t.Insert("products_category", map[string]interface{}{
				"name":          name,
				"admin_fkid":    adminId,
				"data_created":  utils.CurrentMillis(),
				"data_modified": utils.CurrentMillis(),
				"data_status":   "on",
				"data_type":     "product",
			})
			id, _ := res.LastInsertId()
			idMap[name] = id
		}
		return nil
	})
	return idMap, err
}
