package mysql

import (
	"database/sql"
	"encoding/json"
	db "gitlab.com/uniqdev/backend/api-pos/core/db"
	domain "gitlab.com/uniqdev/backend/api-pos/domain"
)

type employeeRepository struct {
	db db.Repository
}

func NewMysqlEmployeeRepository(conn *sql.DB) domain.EmployeeRepository {
	return &employeeRepository{db.Repository{Conn: conn}}
}

func (e employeeRepository) FetchEmployeePosition(userSession domain.UserSession) ([]domain.EmployeePosition, error) {
	sql := "select * from employees_jabatan  where admin_fkid = ?"
	data, err := db.QueryArray(sql, userSession.AdminId)
	if err != nil {
		return nil, err
	}

	var result []domain.EmployeePosition
	dataJson, err := json.Marshal(data)
	err = json.Unmarshal(dataJson, &result)
	return result, err
}
