package http

import (
	"encoding/json"
	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	domain "gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

type employeeHandler struct {
	uc domain.EmployeeUseCase
}

func NewHttpEmployeeHandler(app *fasthttprouter.Router, useCase domain.EmployeeUseCase) {
	handler := &employeeHandler{useCase}
	app.GET("/v1/employee-position", auth.ValidateToken(handler.FetchEmployeePosition))
}

func (h employeeHandler) FetchEmployeePosition(ctx *fasthttp.RequestCtx) {
	result, err := h.uc.FetchEmployeePosition(domain.UserSessionFastHttp(ctx))
	if err != nil {
		log.Info("err: %v", err)
		if errWithCode, ok := err.(utils.ErrWithCode); ok {
			_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Code: errWithCode.Code, Status: false, Message: err.Error()})
			return
		}

		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: result})
}
