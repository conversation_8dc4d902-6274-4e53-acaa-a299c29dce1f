package usecase

import domain "gitlab.com/uniqdev/backend/api-pos/domain"

type employeeUseCase struct {
	repo domain.EmployeeRepository
}

func NewEmployeeUseCase(repository domain.EmployeeRepository) domain.EmployeeUseCase {
	return &employeeUseCase{repository}
}

func (e employeeUseCase) FetchEmployeePosition(userSession domain.UserSession) ([]domain.EmployeePosition, error) {
	return e.repo.FetchEmployeePosition(userSession)
}
