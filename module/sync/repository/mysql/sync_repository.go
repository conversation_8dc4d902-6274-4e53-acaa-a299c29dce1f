package mysql

import (
	"context"
	"database/sql"
	"strings"
	"time"

	db "gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	sync "gitlab.com/uniqdev/backend/api-pos/module/sync"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type syncRepository struct {
	db      db.Repository
	dbCache *mongo.Client
}

func NewMysqlSyncRepository(conn *sql.DB) sync.Repository {
	return &syncRepository{db.Repository{Conn: conn}, db.MongoDbClient()}
}

func (s *syncRepository) GetSyncData() ([]domain.SyncDataEntity, error) {
	query := `select * from sync_data where status = 'on' and start_date <= UNIX_TIMESTAMP()*1000`

	var syncDataConfigs []domain.SyncDataEntity
	err := s.db.Set(query).Get(&syncDataConfigs)
	return syncDataConfigs, err
}

// FetchSalesFilter implements sync.Repository.
func (s *syncRepository) FetchSalesFilter(filter domain.SyncSalesFilter) ([]domain.SyncSalesResult, error) {

	// 	query := `SELECT  @row_num := @row_num + 1 AS row_num, sales_id,
	// time_created
	// from sales,  (SELECT @row_num := 0) AS init
	// where outlet_fkid= @outletId and time_created > @startDate`

	query := `SELECT s.sales_id, s.time_created, s.display_nota
from sales s 
join open_shift os on os.open_shift_id=s.open_shift_fkid
where s.outlet_fkid = @outletId  and (s.time_modified > @startDate OR s.time_created > @startDate ) 
and os.shift_fkid in @shiftIds 
$WHERE`

	var sqlWhere strings.Builder
	if filter.GrandTotalAbove > 0 {
		sqlWhere.WriteString(" AND s.grand_total > @totalAbove ")
	}
	if filter.GrandTotalBelow > 0 {
		sqlWhere.WriteString(" AND s.grand_total < @totalBelow ")
	}
	if len(filter.TagIds) > 0 {
		sqlWhere.WriteString(" AND s.sales_tag_fkid IN @tagIds ")
	}

	query, params := db.MapParam(query, map[string]interface{}{
		"outletId":   filter.OutletId,
		"startDate":  filter.StartDate,
		"shiftIds":   filter.ShiftIds,
		"totalAbove": filter.GrandTotalAbove,
		"totalBelow": filter.GrandTotalBelow,
		"tagIds":     filter.TagIds,
		"WHERE":      sqlWhere.String(),
	})

	var result []domain.SyncSalesResult
	err := s.db.Set(query, params...).Get(&result)
	return result, err
}

// CopySales implements sync.Repository.
func (s *syncRepository) CopySales(config domain.SyncDataEntity, data []domain.SyncSalesResult) error {
	log.Info("copying %v data of sales, config: %v", len(data), utils.SimplyToJson(config))
	err := db.WithTransaction(func(t db.Transaction) error {
		for _, sales := range data {
			// insert sales
			t.Exec(`INSERT INTO sales ( sales_id, display_nota, outlet_fkid, payment, dining_table, customer_name, member_fkid, qty_customers, 
			data_status, employee_fkid, status, time_prediction, open_shift_fkid, date_created, date_modified, time_created, 
			time_modified, discount, discount_info, voucher, voucher_info, grand_total, receipt_receiver, point_earned, sales_tag_fkid ) 
			SELECT ?, ?, ?, payment, dining_table, customer_name, member_fkid, qty_customers, data_status, 
			employee_fkid, status, time_prediction, open_shift_fkid, date_created, date_modified, time_created, time_modified, 
			discount, discount_info, voucher, voucher_info, grand_total, receipt_receiver, point_earned, sales_tag_fkid 
			FROM sales WHERE sales_id = ?`, sales.NewSalesId, sales.NewDisplayNota, config.OutletDestinationFKID, sales.SalesId)

			salesDetails, err := db.QueryArray("select sales_detail_id from sales_detail where sales_fkid = ?", sales.SalesId)
			if err != nil {
				return err
			}

			for _, detail := range salesDetails {
				//TODO:
				//change product_detail_fkid based on outlet destination

				//insert sales_detail
				resp, _ := t.Exec(`INSERT INTO sales_detail (sales_fkid, product_fkid, product_detail_fkid, qty, price, price_add, sub_total, discount, 
			discount_info, parent, child_type, employee_fkid, note, time_created, price_buy, price_buy_total, commission_staff, commission_customer) 
			SELECT ?, product_fkid, product_detail_fkid, qty, price, price_add, sub_total, discount, discount_info, parent, child_type, employee_fkid, 
			note, time_created, price_buy, price_buy_total, commission_staff, commission_customer FROM sales_detail WHERE sales_detail_id = ?`,
					sales.NewSalesId, detail["sales_detail_id"])
				detailId, _ := resp.LastInsertId()

				//insert sales_detail_discount
				t.Exec(`INSERT INTO sales_detail_discount (sales_detail_fkid, type, total, sales_promotion_fkid) 
				SELECT ?, type, total, sales_promotion_fkid FROM sales_detail_discount WHERE sales_detail_fkid = ?`, detailId, detail["sales_detail_id"])

				//insert sales_detail_promotion
				t.Exec(`INSERT INTO sales_detail_promotion (sales_fkid, sales_detail_fkid, sales_void_fkid, promotion_fkid, promotion_value) 
				SELECT ?, ?, sales_void_fkid, promotion_fkid, promotion_value FROM sales_detail_promotion WHERE sales_detail_fkid = ?`,
					sales.NewSalesId, detailId, detail["sales_detail_id"])

				//insert sales_tax_detail
				t.Exec(`INSERT INTO sales_detail_tax (tax_fkid, total, sales_detail_fkid, category) 
				SELECT tax_fkid, total, ?, category FROM sales_detail_tax WHERE sales_detail_fkid = ?`,
					detailId, detail["sales_detail_id"])
			}

			// resp, _ := t.Exec(`INSERT INTO sales_detail (sales_fkid, product_fkid, product_detail_fkid, qty, price, price_add, sub_total, discount,
			// discount_info, parent, child_type, employee_fkid, note, time_created, price_buy, price_buy_total, commission_staff, commission_customer)
			// SELECT ?, product_fkid, product_detail_fkid, qty, price, price_add, sub_total, discount, discount_info, parent, child_type, employee_fkid,
			// note, time_created, price_buy, price_buy_total, commission_staff, commission_customer FROM sales_detail WHERE sales_fkid = ?`,
			// 	sales.NewSalesId, sales.SalesId)
			// detailIds, err := db.GetIds(resp)
			// if err != nil {
			// 	return err
			// }
			// log.Info("detailIds: %v", utils.SimplyToJson(detailIds))

			//insert sales_payment
			// resp, _ := t.Exec(`INSERT INTO sales_payment (sales_fkid, method, total, pay, info, time_created)
			// SELECT ?, method, total, pay, info, time_created FROM sales_payment
			// WHERE sales_fkid = ?`, sales.NewSalesId, sales.SalesId)
			// paymentIds, err := db.GetIds(resp)
			// log.Info("paymentIds: %v", utils.SimplyToJson(paymentIds))
			// if err != nil {
			// 	return err
			// }

			salesPayments, err := db.QueryArray("select payment_id from sales_payment where sales_fkid = ?", sales.SalesId)
			if err != nil {
				return err
			}

			for _, payment := range salesPayments {
				//insert sales_payment
				resp, _ := t.Exec(`INSERT INTO sales_payment (sales_fkid, method, total, pay, info, time_created) 
				SELECT ?, method, total, pay, info, time_created FROM sales_payment 
				WHERE payment_id = ?`, sales.NewSalesId, payment["payment_id"])
				id, _ := resp.LastInsertId()

				//insert sales_payment_bank
				t.Exec(`INSERT INTO sales_payment_bank (sales_payment_fkid, bank_fkid, account_number)
				SELECT ?, bank_fkid, account_number
				FROM sales_payment_bank
				WHERE sales_payment_fkid=?`, id, payment["payment_id"])
			}

			//insert sales_tax
			t.Exec(`INSERT INTO sales_tax (tax_fkid, total, sales_fkid, category) 
			SELECT tax_fkid, total, ?, category FROM sales_tax WHERE sales_fkid = ?`, sales.NewSalesId, sales.SalesId)
		}
		return nil
	})
	return err
}

// UpdateLastSync implements sync.Repository.
func (s *syncRepository) UpdateLastSync(id int64, lastSync int64) error {
	log.Info("update sync_data '%v' lastSync to %v", id, lastSync)
	_, err := db.Update("sync_data", map[string]interface{}{
		"last_sync": lastSync,
	}, "sync_data_id=?", id)
	return err
}

// FetchTotalSalesEachDay implements sync.Repository.
func (s *syncRepository) FetchTotalSalesEachDay(startDate int64, endDate int64, outletId int, timeZoneOffsetMinutes int) ([]map[string]interface{}, error) {
	query := `SELECT FROM_UNIXTIME(s.time_created/1000 + $offset, '%Y/%m/%d') date , count(*) total 
from sales s 
where s.outlet_fkid= @outletId
and s.time_created BETWEEN @startDate and @endDate
group by FROM_UNIXTIME(s.time_created/1000 + $offset, '%Y/%m/%d')
`

	query, params := db.MapParam(query, map[string]interface{}{
		"outletId":  outletId,
		"startDate": startDate,
		"endDate":   endDate,
		"offset":    timeZoneOffsetMinutes,
	})

	return db.QueryArray(query, params...)
}

// AddSyncLog implements sync.Repository.
func (s *syncRepository) AddSyncLog(data []domain.SyncSalesResult, config domain.SyncDataEntity) error {
	//add log to mysql
	_, err := db.Update("sync_data", map[string]interface{}{
		"sync_log": utils.SimplyToJson(map[string]interface{}{
			"last_sync":  data[len(data)-1].TimeCreated,
			"config":     config,
			"total_data": len(data),
			"start_date": config.StartDate,
		}),
	}, "sync_data_id=?", config.SyncDataID)
	log.IfError(err)

	// Create a slice to store the sales mappings
	salesMappings := make([]map[string]interface{}, len(data))

	// Loop through the data to get salesId and newSalesId
	for i, sale := range data {
		salesMappings[i] = map[string]interface{}{
			"salesId":    sale.SalesId,
			"newSalesId": sale.NewSalesId,
		}
	}

	// Create the log document
	logDocument := bson.M{
		"timestamp":              time.Now().UnixNano() / int64(time.Millisecond),
		"config":                 utils.SimplyToJson(config),
		"outlet_source_id":       config.OutletSourceFKID,
		"outlet_destionation_id": config.OutletDestinationFKID,
		"sales_mapping":          salesMappings,
	}

	// Get the MongoDB collection
	collection := s.dbCache.Database("sync_logs").Collection("sales_sync")

	// Insert the log document into MongoDB
	_, err = collection.InsertOne(context.Background(), logDocument)
	if err != nil {
		log.Error("Failed to insert sync log: %v", err)
		return err
	}

	log.Info("Sync log added successfully for %d sales", len(data))
	return nil
}

// FetchSyncedSales implements sync.Repository.
func (s *syncRepository) FetchSyncedSales(salesIds []string, outletDestinationId int) ([]string, error) {
	collection := s.dbCache.Database("sync_logs").Collection("sales_sync")

	// Create a filter to match documents where salesId is in the given salesIds
	filter := bson.M{
		"sales_mapping.salesId": bson.M{
			"$in": salesIds,
		},
		"outlet_destionation_id": outletDestinationId,
	}

	// Project only the salesId field from sales_mapping
	projection := bson.M{
		"sales_mapping.salesId": 1,
	}

	// Perform the query
	cursor, err := collection.Find(context.Background(), filter, options.Find().SetProjection(projection))
	if err != nil {
		log.Error("Failed to query synced sales: %v", err)
		return nil, err
	}
	defer cursor.Close(context.Background())

	var syncedSales []string
	for cursor.Next(context.Background()) {
		var result struct {
			SalesMapping []struct {
				SalesId string `bson:"salesId"`
			} `bson:"sales_mapping"`
		}
		if err := cursor.Decode(&result); err != nil {
			log.Error("Failed to decode synced sales result: %v", err)
			return nil, err
		}
		for _, mapping := range result.SalesMapping {
			syncedSales = append(syncedSales, mapping.SalesId)
		}
	}

	if err := cursor.Err(); err != nil {
		log.Error("Error iterating synced sales cursor: %v", err)
		return nil, err
	}

	log.Info("Found %d synced sales", len(syncedSales))
	return syncedSales, nil
}

// FetchSalesDetailProducts implements sync.Repository.
func (s *syncRepository) FetchSalesDetailProducts(salesIds []string) ([]domain.SalesDetailProduct, error) {
	query := `
    SELECT sd.sales_detail_id, pd.product_detail_id, pd.outlet_fkid 
    FROM sales_detail sd 
    JOIN products_detail pd ON pd.product_detail_id = sd.product_detail_fkid
    WHERE sd.sales_fkid IN @ids
    `

	query, params := db.MapParam(query, map[string]interface{}{
		"ids": salesIds,
	})

	var result []domain.SalesDetailProduct
	err := s.db.Set(query, params...).Get(&result)
	return result, err
}

// InsertNewProductDetail implements sync.Repository.
func (s *syncRepository) InsertNewProductDetail(productDetail domain.ProductDetail) (int64, error) {
	query := `
    INSERT INTO products_detail 
    (product_fkid, outlet_fkid, price_buy_start, price_buy, price_sell, voucher, discount, active,
    transfer_markup_type, transfer_markup, commission_staff_type, commission_staff,
    commission_customer_type, commission_customer, data_modified, variant_fkid, stock, stock_qty, data_status)
    SELECT product_fkid, ?, price_buy_start, price_buy, price_sell, voucher, discount, active,
    transfer_markup_type, transfer_markup, commission_staff_type, commission_staff,
    commission_customer_type, commission_customer, UNIX_TIMESTAMP()*1000, variant_fkid, stock, stock_qty, data_status
    FROM products_detail
    WHERE product_detail_id = ?
    `

	result, err := s.db.Conn.Exec(query, productDetail.OutletFKID, productDetail.ProductDetailID)
	if err != nil {
		return 0, err
	}
	return result.LastInsertId()
}

// UpdateSalesDetailProductId implements sync.Repository.
func (s *syncRepository) UpdateSalesDetailProductId(salesDetailId int64, newProductDetailId int64) error {
	log.Info("adjusting product_detail_fkid of salesd_detail_id %v to %v", salesDetailId, newProductDetailId)
	query := "UPDATE sales_detail SET product_detail_fkid = ? WHERE sales_detail_id = ?"
	_, err := s.db.Conn.Exec(query, newProductDetailId, salesDetailId)
	return err
}
