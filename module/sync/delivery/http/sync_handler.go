package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	sync "gitlab.com/uniqdev/backend/api-pos/module/sync"
)

type syncHandler struct {
	uc sync.UseCase
}

func NewHttpSyncHandler(app *fasthttprouter.Router, useCase sync.UseCase) {
	handler := &syncHandler{useCase}
	app.GET("/v1/sync", handler.Sample)
}
func (h syncHandler) Sample(ctx *fasthttp.RequestCtx) {
	err := h.uc.RunSync()
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"message": err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"message": "sync successfully running...."})
}
