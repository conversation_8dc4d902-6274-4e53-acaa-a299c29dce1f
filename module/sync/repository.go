package sync

import "gitlab.com/uniqdev/backend/api-pos/domain"

type Repository interface {
	GetSyncData() ([]domain.SyncDataEntity, error)
	FetchSalesFilter(domain.SyncSalesFilter) ([]domain.SyncSalesResult, error)
	FetchTotalSalesEachDay(startDate, endDate int64, outletId, timeZoneOffsetMinutes int) ([]map[string]interface{}, error)
	FetchSyncedSales(salesIds []string, outletDestinationId int) ([]string, error) //returns salesId which already synced

	AddSyncLog(data []domain.SyncSalesResult, config domain.SyncDataEntity) error

	UpdateLastSync(id, lastSync int64) error
	CopySales(config domain.SyncDataEntity, data []domain.SyncSalesResult) error

	FetchSalesDetailProducts(salesIds []string) ([]domain.SalesDetailProduct, error)
	InsertNewProductDetail(productDetail domain.ProductDetail) (int64, error)
	UpdateSalesDetailProductId(salesDetailId int64, newProductDetailId int64) error
}
