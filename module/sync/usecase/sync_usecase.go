package usecase

import (
	"fmt"
	"sort"
	"strings"
	sy "sync"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/array"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/generate"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/job"
	sync "gitlab.com/uniqdev/backend/api-pos/module/sync"
)

var mu sy.Mutex

type syncUseCase struct {
	repo sync.Repository
}

func NewSyncUseCase(repository sync.Repository) sync.UseCase {
	uc := &syncUseCase{repository}
	c := job.GetCron()
	c.Every(1).Day().At("01:00").Do(uc.RunSync)
	return uc
}

func (s *syncUseCase) RunSync() error {
	log.Info("run sync called...", time.Now().UnixMilli())
	mu.Lock()
	defer mu.Unlock()

	log.Info("run sync...", time.Now().UnixMilli())
	startSyncTime := time.Now().UnixMilli()

	//fetch sync data from db
	syncDataConfigs, err := s.repo.GetSyncData()
	if log.IfError(err) {
		return err
	}

	log.Info("sync data config, size: %v - %v", len(syncDataConfigs), utils.SimplyToJson(syncDataConfigs))
	var errors strings.Builder
	for i, syncDataConfig := range syncDataConfigs {
		log.Info("%v. syncing data from %v to %v", i, syncDataConfig.OutletSourceFKID, syncDataConfig.OutletDestinationFKID)
		if syncDataConfig.StartDate == 0 {
			log.IfError(fmt.Errorf("can not sync with start date empty: '%v'", syncDataConfig.StartDate))
			continue
		}

		salesToSync := make([]domain.SyncSalesResult, 0)

		//fetch sales (ids) based on sync data config
		switch syncDataConfig.FilterType {
		case "volume":
			salesToSync = s.fetchSalesByVolume(&syncDataConfig)
		case "even_odd":
			salesToSync = s.fetchSalesByEvenOdd(&syncDataConfig)
		case "threshold":
			salesToSync = s.fetchSalesByThreshold(&syncDataConfig)
		case "tag":
			salesToSync = s.fetchSalesByTag(&syncDataConfig)
		}

		log.IfError(s.repo.UpdateLastSync(syncDataConfig.SyncDataID, startSyncTime))

		log.Info("total sales from type '%v': %v", syncDataConfig.FilterType, len(salesToSync))
		if len(salesToSync) == 0 {
			continue
		}

		salesToSync, err = s.removeSyncedSales(salesToSync, syncDataConfig.OutletDestinationFKID)
		if log.IfError(err) {
			errors.WriteString(err.Error())
			continue
		}

		if len(salesToSync) == 0 {
			log.Info("skipp... no sales to sync")
			continue
		}

		//sort sales by transaction time, so re-arrangement would work as intended
		sort.Slice(salesToSync, func(i, j int) bool {
			return salesToSync[i].TimeCreated < salesToSync[j].TimeCreated
		})

		//getting total sales at outlet destination for each day, used for adjusting display nota
		startDate := utils.GetStartOfDayMillis(salesToSync[0].TimeCreated, 25200)
		endDate := utils.GetEndOfDayMillis(salesToSync[len(salesToSync)-1].TimeCreated, 25200)
		salesCount, err := s.repo.FetchTotalSalesEachDay(startDate, endDate, syncDataConfig.OutletDestinationFKID, 25200)
		log.Info("salesCount, from %v to %v at %v: %v, 1st timeCreate: %v, lastTimeCreate: %v", startDate, endDate, syncDataConfig.OutletDestinationFKID, salesCount, salesToSync[0].TimeCreated, salesToSync[len(salesToSync)-1].TimeCreated)
		if log.IfError(err) {
			errors.WriteString(err.Error())
			continue
		}

		//getting new sales_id and display_nota
		adjustSalesIds(&salesToSync, array.FlatMapArray(salesCount, "date"), syncDataConfig.OutletDestinationFKID)

		//adjust time in sales
		for i, sales := range salesToSync {
			salesToSync[i].TimeCreated = sales.TimeCreated + (3600*2)*1000
		}

		//save sales to destination set from data config
		err = s.repo.CopySales(syncDataConfig, salesToSync)
		if !log.IfError(err) {
			// log.IfError(s.repo.UpdateLastSync(syncDataConfig.SyncDataID, time.Now().UnixMilli()))

			//save logs to mongodb
			log.IfError(s.repo.AddSyncLog(salesToSync, syncDataConfig))

			//adjust products_detail_fkid for sales_detail
			log.IfError(s.AdjustProductIds(salesToSync, syncDataConfig))
		} else {
			errors.WriteString(err.Error())
		}
		fmt.Println(strings.Repeat("-", 20))
	}

	if errors.Len() > 0 {
		return fmt.Errorf("some sync migh error: %v", errors.String())
	}

	return nil
}

func (s *syncUseCase) removeSyncedSales(salesToSync []domain.SyncSalesResult, outletDestionationId int) ([]domain.SyncSalesResult, error) {
	//check if salesIds has been synced, remove if yes
	// Extract salesIds from salesToSync
	salesIds := make([]string, len(salesToSync))
	for i, sale := range salesToSync {
		salesIds[i] = sale.SalesId
	}
	log.Info("Extracted salesIds: %v", len(salesIds))

	salesIdsSynced, err := s.repo.FetchSyncedSales(salesIds, outletDestionationId)
	if log.IfError(err) {
		return nil, err
	}

	log.Info("found total synced sales: %v", salesIdsSynced)
	if len(salesIdsSynced) == 0 {
		return salesToSync, nil
	}

	// Remove already synced sales from salesToSync
	var filteredSalesToSync []domain.SyncSalesResult
	for _, sale := range salesToSync {
		if !array.Contain(salesIdsSynced, sale.SalesId) {
			filteredSalesToSync = append(filteredSalesToSync, sale)
		}
	}
	log.Info("synced sales after remove synced: %v, before %v", len(filteredSalesToSync), len(salesToSync))
	return filteredSalesToSync, nil
}

// modify display nota (to use new position) and salesId
func adjustSalesIds(sales *[]domain.SyncSalesResult, salesCount map[string]map[string]interface{}, outletId int) {
	log.Info("salesCount: %v", utils.SimplyToJson(salesCount))
	var logs strings.Builder
	position := 0
	lastDisplayNota := ""
	for i, sale := range *sales {
		posOrigin, err := utils.GetPositionFromDisplayNota(sale.DisplayNota)
		if log.IfError(err) {
			panic(err)
		}

		//generate new salesId
		//("M", "N", "K", "O", "H", "S", "N", "I", "A", "L")
		//["A", "P", "L", "I", "K", "U", "N", "Q", "D", "E"];
		//M=0, N=1, K=2, O=3, H=4, S=5, N=6, I=7, A=8, L=9, P=1, U=5, Q=7, D=8, E=9

		//remove position from display nota
		// displayNotaOnly := strings.TrimRight(sale.DisplayNota, cast.ToString(posOrigin))
		displayNotaOnly := sale.DisplayNota[:len(sale.DisplayNota)-len(cast.ToString(posOrigin))]

		if lastDisplayNota == "" || (lastDisplayNota != "" && lastDisplayNota != displayNotaOnly) {
			date := time.Unix(0, sale.TimeCreated*int64(time.Millisecond)).Format("2006/01/02")
			if total, ok := salesCount[date]; ok {
				position = cast.ToInt(total["total"]) + 1
				logs.WriteString(fmt.Sprintf("total sales at %v: %v", date, total))
			} else {
				logs.WriteString(fmt.Sprintf("total sales at %v not found", date))
				position = 1
			}
		}

		sale.NewDisplayNota = "C" + generate.DisplayNota(0, outletId, position)[1:]  //fmt.Sprintf("%s%v", displayNotaOnly, position)
		sale.NewSalesId = generate.SalesIdFrom(utils.ConvertReceiptId(sale.SalesId)) // C: to identify its copy data

		(*sales)[i].NewDisplayNota = sale.NewDisplayNota
		(*sales)[i].NewSalesId = sale.NewSalesId

		logs.WriteString(fmt.Sprintf("%v display note changed from %v to %v, salesId to: %v | %v > %v, ", sale.SalesId, sale.DisplayNota, sale.NewDisplayNota, sale.NewSalesId, displayNotaOnly, posOrigin))

		lastDisplayNota = displayNotaOnly
		position += 1
	}
	// log.Info(logs.String())
}

func (s *syncUseCase) AdjustProductIds(data []domain.SyncSalesResult, config domain.SyncDataEntity) error {
	// Extract salesIds from data
	salesIds := make([]string, len(data))
	for i, sale := range data {
		salesIds[i] = sale.NewSalesId
	}

	// Fetch sales detail products
	salesDetailProducts, err := s.repo.FetchSalesDetailProducts(salesIds)
	if log.IfError(err) {
		return fmt.Errorf("failed to fetch sales detail products: %w", err)
	}

	log.Info("salesDetailProducts: %v", utils.SimplyToJson(salesDetailProducts))
	for _, sdp := range salesDetailProducts {
		if sdp.OutletFKID != config.OutletDestinationFKID {
			// Product detail doesn't match the destination outlet, create a new one
			newProductDetailId, err := s.repo.InsertNewProductDetail(domain.ProductDetail{
				ProductDetailID: sdp.ProductDetailID,
				OutletFKID:      config.OutletDestinationFKID,
			})
			log.Info("new productDetail inserted to %v id %v", config.OutletDestinationFKID, newProductDetailId)
			if log.IfError(err) {
				continue
			}

			// Update sales_detail with the new product_detail_id
			err = s.repo.UpdateSalesDetailProductId(sdp.SalesDetailID, newProductDetailId)
			if log.IfError(err) {
				continue
			}
		}
	}

	return nil
}
