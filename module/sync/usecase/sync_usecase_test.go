package usecase

import (
	"fmt"
	"testing"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/domain"
)

func TestModifyDisplayNota(t *testing.T) {
	// Setup test cases
	tests := []struct {
		name     string
		input    []domain.SyncSalesResult
		expected []domain.SyncSalesResult
	}{
		{
			name: "Single group",
			input: []domain.SyncSalesResult{
				{SalesId: "1", DisplayNota: "AN20240910100"},
				{SalesId: "2", DisplayNota: "AN20240910200"},
				{SalesId: "3", DisplayNota: "AN20240910250"},
			},
			expected: []domain.SyncSalesResult{
				{SalesId: "2", DisplayNota: "AN202409101"},
				{SalesId: "2", DisplayNota: "AN202409102"},
				{SalesId: "3", DisplayNota: "AN202409103"},
			},
		},
		{
			name: "Multiple groups",
			input: []domain.SyncSalesResult{
				{SalesId: "1", DisplayNota: "AN202409105"},
				{SalesId: "2", DisplayNota: "AN2024091015"},
				{SalesId: "3", DisplayNota: "AN2024091025"},
				{SalesId: "4", DisplayNota: "AN202409115"},
				{SalesId: "5", DisplayNota: "AN202409119"},
			},
			expected: []domain.SyncSalesResult{
				{SalesId: "1", DisplayNota: "AN202409101"},
				{SalesId: "2", DisplayNota: "AN202409102"},
				{SalesId: "3", DisplayNota: "AN202409103"},
				{SalesId: "4", DisplayNota: "AN202409111"},
				{SalesId: "5", DisplayNota: "AN202409112"},
			},
		},
	}

	// Run tests
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			input := tt.input
			// adjustSalesIds(&input)

			for i, inp := range input {
				if inp.DisplayNota != tt.expected[i].DisplayNota {
					t.Errorf("modifyDisplayNota() = %v, want %v", inp, tt.expected[i])
				}
			}

			// if !reflect.DeepEqual(input, tt.expected) {
			// 	t.Errorf("modifyDisplayNota() = %v, want %v", input, tt.expected)
			// }
		})
	}
}

func TestFetchSalesByThreshold(t *testing.T) {
	su := &syncUseCase{nil}
	su.fetchSalesByThreshold(&domain.SyncDataEntity{
		FilterDetail: `{"key": "<", "value": 100000}`,
		Shifts:       `[12,13]`,
	})
}

func TestCode(t *testing.T) {
	for i := 0; i < 10; i++ {
		defer fmt.Println(i, ". defer.... ", time.Now().UnixNano())

		fmt.Println("this is running...")

		if i > 8 {
			continue
		}
		fmt.Println("last looping code...")
	}
}
