package usecase

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"sort"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/domain"
)

func (s *syncUseCase) fetchSalesByVolume(syncDataConfig *domain.SyncDataEntity) []domain.SyncSalesResult {
	filterDetailMap, err := syncDataConfig.FilterDetailToMap()
	log.IfError(err)
	fetchFilter, err := syncDataConfig.GetBasicFilter()
	if log.IfError(err) {
		return []domain.SyncSalesResult{}
	}

	result, err := s.repo.FetchSalesFilter(fetchFilter)
	log.IfError(err)
	log.Info("total sales %v | by filter: %v", len(result), utils.SimplyToJson(fetchFilter))
	if len(result) == 0 {
		return []domain.SyncSalesResult{}
	}

	log.Info("running filter %v", utils.SimplyToJson(filterDetailMap))

	sort.Slice(result, func(i, j int) bool {
		return result[i].TimeCreated < result[j].TimeCreated
	})

	filterCount := cast.ToInt(filterDetailMap["value"])
	if cast.ToString(filterDetailMap["key"]) == "percentage" {
		filterCount = int(float64(len(result)) * cast.ToFloat(filterDetailMap["value"]) / 100)
	}
	log.Info("filter count: %v", filterCount)

	// Ensure filterCount doesn't exceed the number of available results
	if filterCount > len(result) {
		filterCount = len(result)
	}

	// Generate random index numbers
	randomIndexes := make(map[int]bool)
	for len(randomIndexes) < filterCount {
		randomIndex := rand.Intn(len(result))
		if !randomIndexes[randomIndex] {
			randomIndexes[randomIndex] = true
		}
	}

	// Select random data based on the generated indexes
	randomResult := make([]domain.SyncSalesResult, 0, filterCount)
	for index := range randomIndexes {
		randomResult = append(randomResult, result[index])
	}

	log.Info("Randomly selected %v sales out of %v", len(randomResult), len(result))

	return randomResult
}

func (s *syncUseCase) fetchSalesByEvenOdd(syncDataConfig *domain.SyncDataEntity) []domain.SyncSalesResult {
	var filterDetailMap struct {
		Value string `json:"value"`
	}
	err := json.Unmarshal([]byte(syncDataConfig.FilterDetail), &filterDetailMap)
	if log.IfError(err) {
		return []domain.SyncSalesResult{}
	}

	fetchFilter, err := syncDataConfig.GetBasicFilter()
	if log.IfError(err) {
		return []domain.SyncSalesResult{}
	}

	result, err := s.repo.FetchSalesFilter(fetchFilter)
	log.IfError(err)
	log.Info("total sales %v | by filter: %v", len(result), utils.SimplyToJson(fetchFilter))

	modulo := 0
	if filterDetailMap.Value == "odd" {
		modulo = 1
	}
	log.Info("filter value: %v | modulo: %v", filterDetailMap.Value, modulo)

	resultFiltered := make([]domain.SyncSalesResult, 0)
	for i, row := range result {
		if i%2 == modulo {
			resultFiltered = append(resultFiltered, row)
		}
	}
	return resultFiltered
}

func (s *syncUseCase) fetchSalesByThreshold(syncDataConfig *domain.SyncDataEntity) []domain.SyncSalesResult {
	// var filterDetailMap map[string]interface{}
	var filterDetailMap struct {
		Key   string      `json:"key"`
		Value interface{} `json:"value"`
	}
	err := json.Unmarshal([]byte(syncDataConfig.FilterDetail), &filterDetailMap)
	if log.IfError(err) {
		return []domain.SyncSalesResult{}
	}

	log.Info("filter detail (fetchSalesByThreshold): %v", utils.SimplyToJson(filterDetailMap))

	fetchFilter, err := syncDataConfig.GetBasicFilter()
	if log.IfError(err) {
		return []domain.SyncSalesResult{}
	}

	grandTotal := cast.ToInt(filterDetailMap.Value)
	var errSwitch error
	switch filterDetailMap.Key {
	case ">":
		fetchFilter.GrandTotalAbove = grandTotal
		log.Info("use sign >")
	case "<":
		fetchFilter.GrandTotalBelow = grandTotal
		log.Info("use sign <")
	default:
		log.IfError(fmt.Errorf("can not understannd key: '%v'", filterDetailMap.Key))
		errSwitch = fmt.Errorf("can not understand key '%v'", filterDetailMap.Key)
	}

	log.Info("fetchFilter: %v", utils.SimplyToJson(fetchFilter))
	if log.IfError(errSwitch) {
		return []domain.SyncSalesResult{}
	}

	result, err := s.repo.FetchSalesFilter(fetchFilter)
	log.IfError(err)
	log.Info("total sales %v | by filter: %v", len(result), utils.SimplyToJson(fetchFilter))

	return result
}

func (s *syncUseCase) fetchSalesByTag(syncDataConfig *domain.SyncDataEntity) []domain.SyncSalesResult {
	var filterDetailMap struct {
		Value []interface{} `json:"value"`
	}
	err := json.Unmarshal([]byte(syncDataConfig.FilterDetail), &filterDetailMap)
	if log.IfError(err) {
		return []domain.SyncSalesResult{}
	}

	fetchFilter, err := syncDataConfig.GetBasicFilter()
	if log.IfError(err) {
		return []domain.SyncSalesResult{}
	}

	tagIds := make([]int, 0)
	for _, filter := range filterDetailMap.Value {
		tagIds = append(tagIds, cast.ToInt(filter))
	}

	fetchFilter.TagIds = tagIds
	result, err := s.repo.FetchSalesFilter(fetchFilter)
	log.Info("got total %v sales from filter %v", len(result), utils.SimplyToJson(fetchFilter))
	log.IfError(err)
	return result
}
