package usecase

import (
	"fmt"
	"strings"

	v1 "gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	domain "gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

type memberUseCase struct {
	repo domain.MemberRepository
}

func NewMemberUseCase(repository domain.MemberRepository) domain.MemberUseCase {
	return &memberUseCase{repository}
}

func (m *memberUseCase) AddMember(member domain.Member, user domain.UserSession) (map[string]interface{}, error) {
	if strings.HasPrefix(member.Phone, "08") {
		member.Phone = "62" + member.Phone[1:]
	}

	//first check data already registered
	data, err := m.repo.FetchMemberByPhone(member.Phone)
	if err != nil {
		return nil, err
	}

	var memberId int64

	//if member already registered,
	if len(data) > 0 {
		//check if registered to this business or other
		memberDetail, err := m.repo.FetchMemberById(utils.ToInt64(data["member_id"]), user.AdminId)
		if err != nil {
			return nil, err
		}

		//if already registered to this business, return err
		if len(memberDetail) > 0 {
			return nil, utils.ErrWithCode{
				Code:    50,
				Message: "account already registered",
			}
		}

		//if registered in another business, then register to this business
		err = m.repo.AddMemberToOtherBusiness(utils.ToInt64(data["member_id"]), user.AdminId)
		if err != nil {
			return nil, err
		}

		memberId = utils.ToInt64(data["member_id"])
	} else {
		memberId, err = m.repo.AddMember(member, user)
		if err != nil {
			return nil, err
		}
	}

	return m.repo.FetchMemberById(memberId, user.AdminId)
}

// FetchMember implements domain.MemberUseCase.
func (m *memberUseCase) FetchMember(request models.MemberRequest, user domain.UserSession) ([]models.MemberResponse, error) {
	if request.QrCode != "" {
		request.MemberId = v1.ExtractMemberIdFromQr(request.QrCode)
		if request.MemberId == 0 {
			return nil, fmt.Errorf("invalid qr code")
		}
	}

	member, err := m.repo.FetchMember(request, user)
	if log.IfError(err) {
		return nil, err
	}

	//check if secret code expired
	if request.MemberId == 0 && request.SecretId != "" && len(member) > 0 {
		if member[0].SecretIDExpired < utils.CurrentMillis() {
			return nil, fmt.Errorf("secret code already expired")
		}
	}
	return member, err
}
