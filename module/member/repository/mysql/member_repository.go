package mysql

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	db "gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	domain "gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

type memberRepository struct {
	db db.Repository
}

func NewMysqlMemberRepository(conn *sql.DB) domain.MemberRepository {
	return &memberRepository{db.Repository{Conn: conn}}
}

func (m memberRepository) FetchMemberByPhone(phone string) (map[string]interface{}, error) {
	params := make([]interface{}, 0)
	params = append(params, phone)
	if strings.HasPrefix(phone, "62") {
		params = append(params, "0"+phone[2:])
	}
	whereIn := strings.TrimRight(strings.Repeat("?,", len(params)), ",")
	q := "select * from members where phone in (" + whereIn + ")"
	return db.Query(q, params...)
}

func (m memberRepository) AddMember(member domain.Member, user domain.UserSession) (int64, error) {
	memberTypeId, err := m.FetchDefaultMemberTypeId(user.AdminId)
	if err != nil {
		return 0, err
	}

	var memberId int64
	err = db.WithTransaction(func(tx db.Transaction) error {
		resp, _ := tx.Insert("members", map[string]interface{}{
			"name":         member.Name,
			"phone":        member.Phone,
			"data_created": time.Now().Unix() * 1000,
		})

		memberId, _ = resp.LastInsertId()
		_, _ = tx.Insert("members_detail", map[string]interface{}{
			"member_fkid":   memberId,
			"admin_fkid":    user.AdminId,
			"type_fkid":     memberTypeId,
			"register_date": time.Now().Unix() * 1000,
		})
		return nil
	})

	return memberId, err
}

func (m memberRepository) FetchMemberById(memberId int64, adminId int64) (map[string]interface{}, error) {
	q := `SELECT * from members m 
join members_detail md on m.member_id=md.member_fkid 
where m.member_id = ? and md.admin_fkid= ? `
	return db.Query(q, memberId, adminId)
}

func (m memberRepository) AddMemberToOtherBusiness(memberId int64, adminId int64) error {
	memberTypeId, err := m.FetchDefaultMemberTypeId(adminId)
	if err != nil {
		return err
	}
	_, err = db.Insert("members_detail", map[string]interface{}{
		"member_fkid":   memberId,
		"admin_fkid":    adminId,
		"type_fkid":     memberTypeId,
		"register_date": time.Now().Unix() * 1000,
	})
	return err
}

func (m memberRepository) FetchDefaultMemberTypeId(adminId int64) (int64, error) {
	q := "SELECT type_id from members_type where admin_fkid = ? and point_target=0 and spent_target=0"
	memberType, err := db.Query(q, adminId)
	if err != nil {
		return 0, err
	}

	if len(memberType) == 0 {
		_, err = db.GetDb().Exec("call create_default_member_type(?)", adminId)
		if log.IfError(err) {
			return 0, err
		}
	}

	memberType, err = db.Query(q, adminId)
	memberTypeId := utils.ToInt64(memberType["type_id"])
	if memberTypeId == 0 {
		return 0, errors.New("failed to get default member type id")
	}

	return memberTypeId, nil
}

// FetchMember implements domain.MemberRepository.
func (m *memberRepository) FetchMember(request models.MemberRequest, user domain.UserSession) ([]models.MemberResponse, error) {
	query := `SELECT member_id, m.name, phone, type_fkid, p.name as type_name, secret_id_expired
	FROM members m
			 join members_detail md on m.member_id = md.member_fkid
			 join members_type mt on md.type_fkid = mt.type_id 
			 join products p on mt.product_fkid = p.product_id 
	WHERE md.admin_fkid = @adminId `

	if request.MemberId != 0 {
		query += " AND m.member_id = @memberId "
	}
	if request.SecretId != "" {
		query += " AND secret_id = @secretId "
	}

	query, params := db.MapParam(query, map[string]interface{}{
		"adminId":  user.AdminId,
		"memberId": request.MemberId,
		"secretId": request.SecretId,
	})

	var result []models.MemberResponse
	err := m.db.Set(query, params...).Get(&result)
	return result, err
}
