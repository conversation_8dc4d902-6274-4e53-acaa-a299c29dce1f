package http

import (
	"bytes"
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	domain "gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

type memberHandler struct {
	uc domain.MemberUseCase
}

func NewHttpMemberHandler(app *fasthttprouter.Router, useCase domain.MemberUseCase) {
	handler := &memberHandler{useCase}
	app.POST("/v1/member", auth.ValidateToken(handler.AddMember))
	app.GET("/v2/member", auth.ValidateToken(handler.FetchMember))
}

func (h *memberHandler) AddMember(ctx *fasthttp.RequestCtx) {
	var member domain.Member
	log.IfError(json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&member))
	user := domain.UserSessionFastHttp(ctx)
	result, err := h.uc.AddMember(member, user)
	if err != nil {
		log.Info("err: %v", err)
		if errWithCode, ok := err.(utils.ErrWithCode); ok {
			_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Code: errWithCode.Code, Status: false, Message: err.Error()})
			return
		}

		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: result})
}

func (h *memberHandler) FetchMember(ctx *fasthttp.RequestCtx) {
	secretID := string(ctx.QueryArgs().Peek("secret_id"))
	qrCode := string(ctx.QueryArgs().Peek("qr_code"))

	if secretID == "" && qrCode == "" {
		log.Warn("GetMember requires either a secret_id or qr_code")
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "secret_id or qr_code is required"})
		return
	}

	user := domain.UserSessionFastHttp(ctx)
	request := models.MemberRequest{
		SecretId: secretID,
		QrCode:   qrCode,
	}
	result, err := h.uc.FetchMember(request, user)
	if err != nil {
		log.Info("err: %v", err)
		if errWithCode, ok := err.(utils.ErrWithCode); ok {
			ctx.SetStatusCode(fasthttp.StatusBadRequest)
			_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Code: errWithCode.Code, Status: false, Message: err.Error()})
			return
		}

		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "Internal server error"})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: result})
}
