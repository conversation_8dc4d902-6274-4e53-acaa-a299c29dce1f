package http

import (
	"encoding/json"
	"strconv"
	"time"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	outlet "gitlab.com/uniqdev/backend/api-pos/module/outlet"
)

type outletHandler struct {
	uc outlet.UseCase
}

func NewHttpOutletHandler(app *fasthttprouter.Router, useCase outlet.UseCase) {
	handler := &outletHandler{useCase}
	app.GET("/v1/outlet", auth.ValidateToken(handler.FetchOutlet))
	app.GET("/v2/outlet/:id", auth.ValidateToken(handler.FetchOutlet))

	app.POST("/v1/outlet/kitchen-display", auth.ValidateToken(handler.AddKitchenDisplay))
	app.GET("/v2/outlet/:id/kitchen-display", auth.ValidateToken(handler.GetKitchenDisplays))
	app.DELETE("/v2/outlet/:outletId/kitchen-display/:id", auth.ValidateToken(handler.RemoveKitchenDisplays))
	app.PUT("/v2/outlet/:outletId/kitchen-display/:id", auth.ValidateToken(handler.UpdateKitchenDisplay))
}

// sample
func (h *outletHandler) Sample(ctx *fasthttp.RequestCtx) {
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"message": "this is sample of outlet feature"})
}

func (h *outletHandler) FetchOutlet(ctx *fasthttp.RequestCtx) {
	id := ctx.UserValue("id")

	var result interface{}
	var err error
	user := domain.UserSessionFastHttp(ctx)

	if id != nil {
		result, err = h.uc.FetchOutletById(cast.ToInt(id), user)
	} else {
		result, err = h.uc.FetchOutlet(user)
	}

	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: false, Millis: time.Now().Unix() * 1000})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Data: result})
}

func (h *outletHandler) AddKitchenDisplay(ctx *fasthttp.RequestCtx) {
	var kitchenDisplay models.KitchenDisplay
	err := json.Unmarshal(ctx.PostBody(), &kitchenDisplay)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: false, Millis: time.Now().Unix() * 1000, Message: "Invalid request body"})
		return
	}

	user := domain.UserSessionFastHttp(ctx)
	err = h.uc.AddKitchenDisplay(user, kitchenDisplay)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: false, Millis: time.Now().Unix() * 1000, Message: "Failed to add kitchen display"})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Message: "Kitchen display added successfully"})
}

func (h *outletHandler) GetKitchenDisplays(ctx *fasthttp.RequestCtx) {
	outletId, err := strconv.Atoi(ctx.UserValue("id").(string))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: false, Millis: time.Now().Unix() * 1000, Message: "Invalid outlet ID"})
		return
	}

	lastSync, err := strconv.ParseInt(string(ctx.QueryArgs().Peek("last_sync")), 10, 64)
	if err != nil {
		lastSync = 0 // If last_sync is not provided or invalid, fetch all records
	}

	user := domain.UserSessionFastHttp(ctx)
	kitchenDisplays, err := h.uc.GetKitchenDisplays(user, outletId, lastSync)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: false, Millis: time.Now().Unix() * 1000, Message: "Failed to fetch kitchen displays"})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Data: kitchenDisplays})
}

func (h *outletHandler) RemoveKitchenDisplays(ctx *fasthttp.RequestCtx) {
	outletId := cast.ToInt(ctx.UserValue("outletId"))
	kitchenDisplayId := cast.ToInt(ctx.UserValue("id"))
	user := domain.UserSessionFastHttp(ctx)
	err := h.uc.RemoveKitchenDisplays(user, outletId, kitchenDisplayId)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: false, Millis: time.Now().Unix() * 1000, Message: "Failed to remove kitchen displays"})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Message: "Kitchen displays removed successfully"})
}

func (h *outletHandler) UpdateKitchenDisplay(ctx *fasthttp.RequestCtx) {
	outletId := cast.ToInt(ctx.UserValue("outletId"))
	kitchenDisplayId := cast.ToInt(ctx.UserValue("id"))
	var kitchenDisplay models.KitchenDisplay
	err := json.Unmarshal(ctx.PostBody(), &kitchenDisplay)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: false, Millis: time.Now().Unix() * 1000, Message: "Invalid request body"})
		return
	}

	user := domain.UserSessionFastHttp(ctx)
	err = h.uc.UpdateKitchenDisplay(user, outletId, kitchenDisplayId, kitchenDisplay)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: false, Millis: time.Now().Unix() * 1000, Message: "Failed to update kitchen display"})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Message: "Kitchen display updated successfully"})
}
