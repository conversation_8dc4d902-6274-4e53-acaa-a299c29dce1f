package outlet

import (
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

type UseCase interface {
	FetchOutlet(user domain.UserSession) ([]models.OutletEntity, error)
	FetchOutletById(id int, user domain.UserSession) (models.OutletEntity, error)
	AddKitchenDisplay(user domain.UserSession, kitchenDisplay models.KitchenDisplay) error
	// Add new method
	GetKitchenDisplays(user domain.UserSession, outletId int, lastSync int64) ([]models.KitchenDisplay, error)
	RemoveKitchenDisplays(user domain.UserSession, outletId int, kitchenDisplayId int) error
	UpdateKitchenDisplay(user domain.UserSession, outletId int, kitchenDisplayId int, kitchenDisplay models.KitchenDisplay) error
}
