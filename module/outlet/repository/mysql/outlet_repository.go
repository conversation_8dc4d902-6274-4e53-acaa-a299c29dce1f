package mysql

import (
	"database/sql"
	"encoding/json"
	"strings"

	db "gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	outlet "gitlab.com/uniqdev/backend/api-pos/module/outlet"
)

type outletRepository struct {
	db db.Repository
}

func NewMysqlOutletRepository(conn *sql.DB) outlet.Repository {
	return &outletRepository{db.Repository{Conn: conn}}
}

func (o outletRepository) FetchOutlet(user domain.UserSession, filter models.OutletRequestFilter) ([]models.OutletEntity, error) {
	var result []models.OutletEntity
	sql := `SELECT outlets.*, trim(name) as name, trim(receipt_note) as receipt_note 
	FROM outlets 
	WHERE admin_fkid= @adminId AND data_status='on' 
	#WHERE
	ORDER BY name `

	var whereSql strings.Builder
	if filter.OutletID > 0 {
		whereSql.WriteString(" AND outlet_id = @outletId ")
	}

	sql = strings.Replace(sql, "#WHERE", whereSql.String(), 1)
	var params []interface{}
	sql, params = db.MapParam(sql, map[string]interface{}{
		"outletId": filter.OutletID,
		"adminId":  user.AdminId,
	})

	err := o.db.Set(sql, params...).Get(&result)
	return result, err
}

func (o outletRepository) FetchOutletById(outletId int) (models.OutletEntity, error) {
	sql := "select * from outlets where outlet_id = ?"
	var result models.OutletEntity
	err := o.db.Set(sql, outletId).Get(&result)
	return result, err
}

func (o outletRepository) AddKitchenDisplay(user domain.UserSession, kitchenDisplay models.KitchenDisplay) error {
	categoriesJSON, err := json.Marshal(kitchenDisplay.Categories)
	if err != nil {
		return err
	}

	_, err = db.Insert("setting_kitchen_display", map[string]interface{}{
		"name":          kitchenDisplay.Name,
		"address":       kitchenDisplay.Address,
		"categories":    string(categoriesJSON),
		"outlet_fkid":   kitchenDisplay.OutletFKID,
		"data_created":  utils.CurrentMillis(),
		"data_modified": utils.CurrentMillis(),
	})

	return err
}

func (o outletRepository) GetKitchenDisplays(outletId int, lastSync int64) ([]models.KitchenDisplay, error) {
	sql := `SELECT setting_kitchen_display_id, name, address, categories, outlet_fkid, data_created, data_modified 
		FROM setting_kitchen_display 
		WHERE outlet_fkid = ? AND data_modified > ?
		ORDER BY data_modified DESC`

	stmt, err := o.db.Conn.Prepare(sql)
	if log.IfError(err) {
		return nil, err
	}

	rows, err := stmt.Query(outletId, lastSync)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var result []models.KitchenDisplay
	for rows.Next() {
		var kd models.KitchenDisplay
		var categoriesJSON string
		err := rows.Scan(
			&kd.SettingKitchenDisplayID,
			&kd.Name,
			&kd.Address,
			&categoriesJSON,
			&kd.OutletFKID,
			&kd.DataCreated,
			&kd.DataModified,
		)
		if err != nil {
			return nil, err
		}

		err = json.Unmarshal([]byte(categoriesJSON), &kd.Categories)
		if err != nil {
			return nil, err
		}

		result = append(result, kd)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return result, nil
}

func (o outletRepository) RemoveKitchenDisplays(outletId int, kitchenDisplayId int) error {
	sql := "DELETE FROM setting_kitchen_display WHERE setting_kitchen_display_id = ? AND outlet_fkid = ?"
	_, err := o.db.Conn.Exec(sql, kitchenDisplayId, outletId)
	return err
}

func (o outletRepository) UpdateKitchenDisplay(outletId int, kitchenDisplayId int, kitchenDisplay models.KitchenDisplay) error {
	updateFields := make(map[string]interface{})

	if kitchenDisplay.Name != "" {
		updateFields["name"] = kitchenDisplay.Name
	}
	if kitchenDisplay.Address != "" {
		updateFields["address"] = kitchenDisplay.Address
	}
	if len(kitchenDisplay.Categories) > 0 {
		categoriesJSON, err := json.Marshal(kitchenDisplay.Categories)
		if err != nil {
			return err
		}
		updateFields["categories"] = string(categoriesJSON)
	}
	updateFields["data_modified"] = utils.CurrentMillis()
	_, err := db.Update("setting_kitchen_display", updateFields, "setting_kitchen_display_id = ? AND outlet_fkid = ?", kitchenDisplayId, outletId)

	return err
}
