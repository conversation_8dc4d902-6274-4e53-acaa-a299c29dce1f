package outlet

import (
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

type Repository interface {
	FetchOutlet(user domain.UserSession, requestFilter models.OutletRequestFilter) ([]models.OutletEntity, error)
	FetchOutletById(outletId int) (models.OutletEntity, error)
	// Add new method
	AddKitchenDisplay(user domain.UserSession, kitchenDisplay models.KitchenDisplay) error
	// Add new method
	GetKitchenDisplays(outletId int, lastSync int64) ([]models.KitchenDisplay, error)
	RemoveKitchenDisplays(outletId int, kitchenDisplayId int) error
	UpdateKitchenDisplay(outletId int, kitchenDisplayId int, kitchenDisplay models.KitchenDisplay) error
}
