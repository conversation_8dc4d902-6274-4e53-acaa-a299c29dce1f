package usecase

import (
	"fmt"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/array"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	outlet "gitlab.com/uniqdev/backend/api-pos/module/outlet"
)

type outletUseCase struct {
	repo outlet.Repository
}

func NewOutletUseCase(repository outlet.Repository) outlet.UseCase {
	return &outletUseCase{repository}
}

func (o *outletUseCase) FetchOutlet(user domain.UserSession) ([]models.OutletEntity, error) {
	outlets, err := o.repo.FetchOutlet(user, models.OutletRequestFilter{})
	// log.Info("get outlet: %v | total %v | authorized: %v | role: %v", user.AdminId, len(outlets), len(user.AuthorizedOutletIds), user.Role)
	if log.IfError(err) {
		return []models.OutletEntity{}, err
	}

	//validate outlet access
	if user.Role == utils.ROLE_EMPLOYEE {
		outletsAuthorized := make([]models.OutletEntity, 0)
		for _, outlet := range outlets {
			if array.Contain(user.AuthorizedOutletIds, outlet.OutletID) {
				outletsAuthorized = append(outletsAuthorized, outlet)
			}
		}
		outlets = outletsAuthorized
		// log.Info("authorized outlets list %v | result %v", user.AuthorizedOutletIds, len(outletsAuthorized))
	}

	//TODO: validate outlet permission

	// isRequestOutOfPermission := false
	// unAuthOutlet := ""
	// var data []map[string]interface{}
	// for _, outlet := range outlets {
	// 	if found := Any(strings.Split(string(outletIds), ","), utils.ToString(outlet["outlet_id"])); string(role) == utils.ROLE_OWNER || found {
	// 		if os.Getenv("server") == "staging" || os.Getenv("server") == "demo" {
	// 			outlet["receipt_logo"] = ""
	// 		}
	// 		data = append(data, outlet)
	// 	} else {
	// 		isRequestOutOfPermission = true
	// 		unAuthOutlet += utils.ToString(outlet["outlet_id"])
	// 	}
	// }

	// if isRequestOutOfPermission {
	// 	log.Error("adminId %s request out of permission. unAuthOutlet : %s - allowed outlet : %s", adminId, unAuthOutlet, outletIds)
	// }

	return outlets, nil
}

func (o *outletUseCase) FetchOutletById(id int, user domain.UserSession) (models.OutletEntity, error) {
	outlets, err := o.repo.FetchOutlet(user, models.OutletRequestFilter{OutletID: id})
	log.Info("get outlet, id: %v, adminId: %v | total %v", id, user.AdminId, len(outlets))
	if log.IfError(err) {
		return models.OutletEntity{}, err
	}

	if len(outlets) > 0 {
		return outlets[0], nil
	}

	return models.OutletEntity{}, err
}

func (o *outletUseCase) AddKitchenDisplay(user domain.UserSession, kitchenDisplay models.KitchenDisplay) error {
	kitchenDisplay.DataCreated = time.Now().Unix()
	kitchenDisplay.DataModified = time.Now().Unix()

	// user.AuthorizedOutletIds
	// Validate if the user has access to the requested outlet
	if !array.Contain(user.AuthorizedOutletIds, kitchenDisplay.OutletFKID) {
		return fmt.Errorf("user does not have access to the requested outlet")
	}

	err := o.repo.AddKitchenDisplay(user, kitchenDisplay)
	if log.IfError(err) {
		return err
	}

	return nil
}

func (o *outletUseCase) GetKitchenDisplays(user domain.UserSession, outletId int, lastSync int64) ([]models.KitchenDisplay, error) {
	// user.AuthorizedOutletIds
	// Validate if the user has access to the requested outlet
	if !array.Contain(user.AuthorizedOutletIds, outletId) {
		return nil, fmt.Errorf("user does not have access to the requested outlet")
	}

	// Fetch kitchen displays
	kitchenDisplays, err := o.repo.GetKitchenDisplays(outletId, lastSync)
	if err != nil {
		return nil, err
	}

	return kitchenDisplays, nil
}

func (o *outletUseCase) RemoveKitchenDisplays(user domain.UserSession, outletId int, kitchenDisplayId int) error {
	// Validate if the user has access to the requested outlet
	if !array.Contain(user.AuthorizedOutletIds, outletId) {
		return fmt.Errorf("user does not have access to the requested outlet")
	}

	// Remove kitchen displays
	err := o.repo.RemoveKitchenDisplays(outletId, kitchenDisplayId)
	log.IfError(err)
	return err
}

func (o *outletUseCase) UpdateKitchenDisplay(user domain.UserSession, outletId int, kitchenDisplayId int, kitchenDisplay models.KitchenDisplay) error {
	// Validate if the user has access to the requested outlet
	if !array.Contain(user.AuthorizedOutletIds, outletId) {
		return fmt.Errorf("user does not have access to the requested outlet")
	}

	// Update kitchen displays
	err := o.repo.UpdateKitchenDisplay(outletId, kitchenDisplayId, kitchenDisplay)
	log.IfError(err)
	return err
}
