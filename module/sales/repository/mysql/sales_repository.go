package mysql

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	db "gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	sales "gitlab.com/uniqdev/backend/api-pos/module/sales"
)

type salesRepository struct {
	db db.Repository
}

// FetchOutletById implements sales.Repository.
func (s *salesRepository) FetchOutletById(outletId int) (map[string]any, error) {
	panic("unimplemented")
}

func NewMysqlSalesRepository(conn *sql.DB) sales.Repository {
	return &salesRepository{db.Repository{Conn: conn}}
}

// FetchSales implements sales.Repository.
func (s *salesRepository) FetchSales(request domain.SalesRequest, user domain.UserSession) ([]map[string]any, error) {
	sql := `select * from sales where time_modified > @time and outlet_fkid = @outletId LIMIT @limit`
	sql, params := db.MapParam(sql, map[string]any{
		"time":     request.LastSync,
		"outletId": request.OutletId,
		"limit":    200,
	})
	return db.QueryArray(sql, params...)
}

func (s salesRepository) FetchSalesCart(id string, user domain.UserSession) (models.SalesCart, error) {
	sql := `select c.* from tmp_sales as c 
	join outlets o on o.outlet_id = c.outlet_fkid 
	where c.no_nota = @salesId `

	if user.AdminId > 0 {
		sql += " and o.admin_fkid = @adminId "
	}

	sql, params := db.MapParam(sql, map[string]any{
		"salesId": id,
		"adminId": user.AdminId,
	})

	var result models.SalesCart
	err := s.db.Set(sql, params...).Get(&result)
	return result, err
}

func (s salesRepository) FetchSalesCartPayment(id string) ([]models.SalesCartPaymentEntity, error) {
	var result []models.SalesCartPaymentEntity
	err := s.db.Set("select * from tmp_sales_payment where tmp_sales_fkid = ?", id).Get(&result)
	return result, err
}

func (s salesRepository) CreateSalesPayment(payment models.SalesCartPaymentEntity) (int64, error) {
	resp, err := db.Insert(" tmp_sales_payment ", payment.ToMap())
	if err != nil {
		return 0, err
	}
	id, _ := resp.LastInsertId()
	return id, nil
}

func (s salesRepository) FetchOrderSales(param domain.OrderSalesReqParam, user domain.UserSession) ([]models.OrderSalesEntity, error) {
	sql := `SELECT os.* from order_sales os 
	join outlets o on o.outlet_id=os.outlet_fkid
	where o.admin_fkid= @adminId `

	if param.LastSync > 0 {
		sql += " and os.last_sync > @lastSync "
	}
	if param.OrderSalesId != "" {
		sql += " and os.order_sales_id = @orderSalesId"
	}

	sql, params := db.MapParam(sql, map[string]any{
		"adminId":      user.AdminId,
		"lastSync":     param.LastSync,
		"orderSalesId": param.OrderSalesId,
	})

	var result []models.OrderSalesEntity
	err := s.db.Set(sql, params...).Get(&result)
	return result, err
}

func (s salesRepository) UpdateOrderSales(id string, param domain.OrderSalesUpdate, user domain.UserSession) error {
	return db.WithTransaction(func(t db.Transaction) error {
		order, err := db.Query(`SELECT o.admin_fkid, os.id from order_sales os 
	join outlets o on o.outlet_id=os.outlet_fkid
	where os.order_sales_id = ?`, id)
		if err != nil {
			return err
		}

		if len(order) == 0 {
			return fmt.Errorf("order_sales not found, %v", id)
		}

		t.Insert("order_sales_status_log", map[string]any{
			"order_sales_fkid": order["id"],
			"order_sales_id":   id,
			"admin_id":         user.AdminId,
			"status":           param.Status,
			"employee_id":      param.EmployeeId,
			"time_created":     time.Now().UnixNano() / 1000000,
			"message":          param.Message,
		})

		orderSalesUpdate := map[string]any{
			"status": param.Status,
		}

		if param.Status == domain.OrderStatusReject {
			orderSalesUpdate["reject_reason"] = param.Message
		}
		t.Update("order_sales", orderSalesUpdate, "order_sales_id = ?", id)

		return nil
	})
}

func (s salesRepository) UpdateSalesCart(cart models.SalesCart) error {
	dataUpdate := make(map[string]any)
	dataUpdate["time_modified"] = time.Now().UnixNano() / 1000000
	if cart.Status != "" {
		dataUpdate["status"] = cart.Status
	}
	_, err := db.Update("tmp_sales", dataUpdate, "no_nota = ?", cart.NoNota)
	return err
}

func (s salesRepository) FetchBank(filter models.BankFilter) ([]models.Bank, error) {
	sql := "select * from payment_media_bank WHERE bank_id > 0 "
	if filter.Name != "" {
		sql += "  AND name = @name "
	}
	if filter.AdminId > 0 {
		sql += "  AND admin_fkid = @adminId "
	}
	if filter.IsInstantPayment {
		sql += " AND provider is not NULL AND provider != '' "
	}
	if filter.Provider != "" {
		sql += " AND provider = @provider "
	}
	if filter.ProviderPaymentKey != "" {
		sql += " AND provider_payment_key = @providerKey "
	}

	sql, params := db.MapParam(sql, map[string]any{
		"name":        filter.Name,
		"adminId":     filter.AdminId,
		"provider":    filter.Provider,
		"providerKey": filter.ProviderPaymentKey,
	})

	var result []models.Bank
	err := s.db.Set(sql, params...).Get(&result)
	return result, err
}

func (s salesRepository) AddBank(bank models.Bank) (int64, error) {
	resp, err := db.Insert("payment_media_bank", bank.ToMap())
	if err != nil {
		return 0, err
	}
	id, _ := resp.LastInsertId()
	return id, nil
}

func (s salesRepository) AddBankDetail(details ...models.BankDetailEntity) error {
	return db.WithTransaction(func(t db.Transaction) error {
		for _, row := range details {
			t.Insert("payment_media_bank_detail", row.ToMap())
		}
		return nil
	})
}

func (s salesRepository) FetchCurrentShift(outletId int) (models.OpenShift, error) {
	var shift models.OpenShift
	err := s.db.Set("SELECT * FROM open_shift WHERE time_close IS NULL AND outlet_fkid = ?", outletId).Get(&shift)
	return shift, err
}

func (s salesRepository) CountSales(openShiftID int) (int, error) {
	// SQL query
	query := "SELECT COUNT(*) FROM sales WHERE open_shift_fkid = ?"

	// Execute the query and get the count
	var count int
	err := s.db.Conn.QueryRow(query, openShiftID).Scan(&count)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s salesRepository) FetchDevices(outletID int) ([]models.DeviceEntity, error) {
	// SQL query
	query := "SELECT * FROM devices WHERE outlet_fkid = ?"

	var devices []models.DeviceEntity
	err := s.db.Set(query, outletID).Get(&devices)
	if err != nil {
		return nil, err
	}

	return devices, nil
}

func (p salesRepository) FetchPaymentNotification(transactionID string) (*[]models.PaymentNotificationEntity, error) {
	var notifications []models.PaymentNotificationEntity

	query := "SELECT * FROM payment_notification WHERE transaction_id = ?"
	err := p.db.Set(query, transactionID).Get(&notifications)
	if err != nil {
		return nil, err
	}

	return &notifications, nil
}

func (s salesRepository) FetchAdmin(adminId int) (models.AdminEntity, error) {
	sql := `select * from admin where admin_id = ?`
	var result models.AdminEntity
	err := s.db.Set(sql, adminId).Get(&result)
	return result, err
}

func (s salesRepository) FetchSalesTag(param *domain.SalesTagRequest, user *domain.UserSession) (*[]models.SalesTagEntity, error) {
	sql := `select * from sales_tag where admin_fkid = @adminId and data_status='on' `
	if param.OutletId > 0 {
		sql += " AND sales_tag_id in (select sales_tag_fkid from sales_tag_outlet where outlet_fkid = @outletId) "
	}
	sql, params := db.MapParam(sql, map[string]any{
		"adminId":  user.AdminId,
		"outletId": param.OutletId,
	})
	var result []models.SalesTagEntity
	err := s.db.Set(sql, params...).Get(&result)
	return &result, err
}

// FetchSalesDetail implements sales.Repository.
func (s *salesRepository) FetchSalesDetail(salesIds ...string) ([]map[string]any, error) {
	sql := `SELECT sd.*,
       (sd.price * sd.qty) - coalesce(sdp.promotion_value, 0) as subtotal_after_promo
		FROM sales_detail sd       
        left join sales_detail_promotion sdp on sdp.sales_detail_fkid = sd.sales_detail_id
		WHERE sd.sales_fkid IN @salesIds`
	sql, params := db.MapParam(sql, map[string]any{
		"salesIds": salesIds,
	})
	return db.QueryArray(sql, params...)
}

// FetchSalesDetailPromotion implements sales.Repository.
func (s *salesRepository) FetchSalesDetailPromotion(salesIds ...string) ([]map[string]any, error) {
	sql := `select sdp.sales_detail_fkid,
		sdp.sales_void_fkid  as sales_void_id,
		sdp.promotion_value,
		p.promotion_id,
		p.name,
		p.promotion_type_id,
		p.promotion_type_id  as promotion_type_fkid,
		sdp.sales_fkid 
 from sales_detail_promotion sdp
		  left join promotions p on p.promotion_id = sdp.promotion_fkid		  
		  where sdp.sales_fkid in @salesIds `
	sql, params := db.MapParam(sql, map[string]any{
		"salesIds": salesIds,
	})
	return db.QueryArray(sql, params...)
}

// FetchSalesPayment implements sales.Repository.
func (s *salesRepository) FetchSalesPayment(salesIds ...string) ([]map[string]any, error) {
	sql := `SELECT info, method, pay, payment_id, total,sales_fkid, pmb.name, spb.account_number
FROM sales_payment sp
left join sales_payment_bank spb on spb.sales_payment_fkid=sp.payment_id
left JOIN payment_media_bank pmb ON spb.bank_fkid=pmb.bank_id
	where sales_fkid in @salesIds `
	sql, params := db.MapParam(sql, map[string]any{
		"salesIds": salesIds,
	})
	return db.QueryArray(sql, params...)
}

// FetchSalesPromotion implements sales.Repository.
func (s *salesRepository) FetchSalesPromotion(salesIds ...string) ([]map[string]any, error) {
	sql := `select s.sales_id as sales_fkid, sp.promotion_value, sp.promotion_value as value, p.name, sp.voucher_code, 
	p.promotion_type_id, p.promotion_type_id as type_id, p.promotion_id
from sales s
         join sales_promotion sp on s.sales_id = sp.sales_fkid
         join promotions p on sp.promotion_fkid = p.promotion_id
		 where s.sales_id in @salesIds `
	sql, params := db.MapParam(sql, map[string]any{
		"salesIds": salesIds,
	})
	return db.QueryArray(sql, params...)
}

// FetchSalesRefund implements sales.Repository.
func (s *salesRepository) FetchSalesRefund(salesIds ...string) ([]map[string]any, error) {
	sql := `select sales_fkid, reason from sales_refund where sales_fkid in @salesIds`
	sql, params := db.MapParam(sql, map[string]any{
		"salesIds": salesIds,
	})
	return db.QueryArray(sql, params...)
}

// FetchSalesTax implements sales.Repository.
func (s *salesRepository) FetchSalesTax(salesIds ...string) ([]map[string]any, error) {
	sql := `SELECT st.total, g.name, sales_fkid, tax_category FROM sales_tax st
	JOIN gratuity g ON st.tax_fkid=g.gratuity_id
	WHERE sales_fkid IN @salesIds `

	sql, params := db.MapParam(sql, map[string]any{
		"salesIds": salesIds,
	})
	return db.QueryArray(sql, params...)
}

// FetchSalesVoid implements sales.Repository.
func (s *salesRepository) FetchSalesVoid(salesIds ...string) ([]map[string]any, error) {
	sql := `SELECT sv.*,     
       s.child_type
FROM sales_void sv
         join sales_detail s on sv.sales_detail_fkid = s.sales_detail_id        
		 WHERE sv.sales_fkid IN @salesIds `
	sql, params := db.MapParam(sql, map[string]any{
		"salesIds": salesIds,
	})
	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSalesById(salesId string) (models.SalesEntity, error) {
	var sale models.SalesEntity
	err := s.db.Set("SELECT * FROM sales WHERE sales_id = ?", salesId).Get(&sale)
	return sale, err
}

func (s *salesRepository) FetchSalesDetailWithProduct(salesId string) ([]map[string]any, error) {
	sql := `
		SELECT sd.*, p.name as product_name
		FROM sales_detail sd
		JOIN products p ON p.product_id = sd.product_fkid
		WHERE sd.sales_fkid = ?
	`
	return db.QueryArray(sql, salesId)
}

func (s *salesRepository) FetchPointEarned(salesId string) (int, error) {
	data, err := db.Query("SELECT point_earned FROM sales WHERE sales_id = ?", salesId)
	if err != nil {
		return 0, err
	}
	return cast.ToInt(data["point_earned"]), nil
}

func (s *salesRepository) FetchAppCrmConfig(adminId int) (models.CrmAppConfig, error) {
	var config models.CrmAppConfig
	data, err := db.Query("SELECT app_config FROM crm_app WHERE admin_fkid = ?", adminId)
	if err != nil {
		return config, err
	}

	if err := json.Unmarshal([]byte(cast.ToString(data["app_config"])), &config); err != nil {
		return config, err
	}
	return config, nil
}

// FetchTransactionConfig implements sales.Repository.
func (s *salesRepository) FetchTransactionConfig(adminId int64) ([]map[string]any, error) {
	sql := `select * from order_configuration where admin_id = ?	`
	return db.QueryArray(sql, adminId)
}

// FetchOrderSalesV2 implements sales.Repository.
func (s *salesRepository) FetchOrderSalesV2(param domain.OrderSalesV2Request, user domain.UserSession) ([]domain.OrderSalesV2Response, error) {
	sql := `SELECT os.*, m.name as member_name, m.phone, m.email
		FROM order_sales os
		left join members m on m.member_id = os.member_fkid
		WHERE outlet_fkid = @outletId AND time_modified > @lastSync`

	sql, params := db.MapParam(sql, map[string]any{
		"outletId": param.OutletId,
		"lastSync": param.LastSync,
	})

	data, err := db.QueryArray(sql, params...)
	if err != nil {
		return nil, err
	}

	var result []domain.OrderSalesV2Response
	for _, row := range data {
		orderSales := domain.OrderSalesV2Response{
			ID:               cast.ToInt(row["id"]),
			OrderSalesID:     cast.ToString(row["order_sales_id"]),
			SalesFkid:        row["sales_fkid"],
			MemberFkid:       cast.ToInt(row["member_fkid"]),
			OutletFkid:       cast.ToInt(row["outlet_fkid"]),
			OrderType:        cast.ToString(row["order_type"]),
			OrderNote:        cast.ToString(row["order_note"]),
			PickupTime:       row["pickup_time"],
			Status:           cast.ToString(row["status"]),
			TimeOrder:        cast.ToInt64(row["time_order"]),
			TimeModified:     cast.ToInt64(row["time_modified"]),
			TimeAcceptReject: cast.ToInt64(row["time_accept_reject"]),
			TimeReady:        cast.ToInt(row["time_ready"]),
			TimeTaken:        cast.ToInt(row["time_taken"]),
			Items:            cast.ToString(row["items"]),
			RejectReason:     row["reject_reason"],
			GrandTotal:       cast.ToInt(row["grand_total"]),
			PaymentInfo:      cast.ToString(row["payment_info"]),
		}

		// Handle member data if available
		if row["member_name"] != nil {
			orderSales.Member = &domain.OrderSalesMemberData{
				MemberID: cast.ToInt64(row["member_fkid"]),
				Name:     cast.ToString(row["member_name"]),
				Phone:    cast.ToString(row["phone"]),
				Email:    cast.ToString(row["email"]),
			}
		} else {
			// Handle meta_data parsing when member_fkid is null
			if metaDataJson := cast.ToString(row["meta_data"]); metaDataJson != "" {
				var metaData map[string]interface{}
				if err := json.Unmarshal([]byte(metaDataJson), &metaData); err == nil {
					orderSales.MetaData = metaData
				}
			}
		}

		result = append(result, orderSales)
	}

	return result, nil
}

// FetchMembersByIds implements sales.Repository.
func (s *salesRepository) FetchMembersByIds(memberIds []int, adminId int64) ([]models.MemberResponse, error) {
	if len(memberIds) == 0 {
		return []models.MemberResponse{}, nil
	}

	sql := `SELECT m.member_id, m.name, m.phone, m.email, md.type_fkid, p.name as type_name, md.secret_id_expired
		FROM members m
		JOIN members_detail md on m.member_id = md.member_fkid
		JOIN members_type mt on md.type_fkid = mt.type_id
		JOIN products p on mt.product_fkid = p.product_id
		WHERE md.admin_fkid = @adminId AND m.member_id IN @memberIds`

	sql, params := db.MapParam(sql, map[string]any{
		"adminId":   adminId,
		"memberIds": memberIds,
	})

	var result []models.MemberResponse
	err := s.db.Set(sql, params...).Get(&result)
	return result, err
}
