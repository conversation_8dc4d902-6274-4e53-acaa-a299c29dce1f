package service

import (
	"encoding/json"

	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"gitlab.com/uniqdev/backend/api-pos/module/sales"
)

type billingRepository struct {
	baseUrl string
}

func NewBillingRepository(baseUrl string) sales.BillingRepository {
	return &billingRepository{baseUrl: baseUrl}
}

func (b *billingRepository) CreatePayment(billing models.CreateBillingRequest) (models.CreateBillingResponse, error) {
	req := utils.HttpRequest{
		Method: "POST",
		Url:    b.baseUrl + "/v1/payment",
		Header: map[string]interface{}{
			"business_id": billing.AdminId,
		},
		PostRequest: utils.PostRequest{
			Body: billing,
		},
	}

	resp, err := req.Execute()
	if log.IfError(err) {
		return models.CreateBillingResponse{}, err
	}

	log.Info("createPayment result -> %s", resp)
	var result models.CreateBillingResponse
	err = json.Unmarshal(resp, &result)
	log.IfError(err)
	return result, err
}
