package usecase

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	v1 "gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/format"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/array"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/generate"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"gitlab.com/uniqdev/backend/api-pos/module/outlet"
	"gitlab.com/uniqdev/backend/api-pos/module/product"
	sales "gitlab.com/uniqdev/backend/api-pos/module/sales"
	billing "gitlab.com/uniqdev/backend/api-pos/module/sales/repository/service"
)

type salesUseCase struct {
	repo        sales.Repository
	repoBilling sales.BillingRepository
	repoOutlet  outlet.Repository
	repoProduct product.Repository
}

func NewSalesUseCase(repository sales.Repository,
	repoProduct product.Repository,
	repoOutlet outlet.Repository) sales.UseCase {
	repoBilling := billing.NewBillingRepository(os.Getenv("API_BILLING"))
	uc := &salesUseCase{repository, repoBilling, repoOutlet, repoProduct}
	go uc.runSubscription()
	return uc
}

// FetchSales implements sales.UseCase.
func (s *salesUseCase) FetchSales(param domain.SalesRequest, user domain.UserSession) (models.ResponseArray, error) {
	// timeStart := time.Now()
	// max := 200
	minAllowToSync := time.Now().AddDate(0, -3, 0).Unix() * 1000
	response := models.ResponseArray{Status: true, Data: make([]map[string]interface{}, 0), Millis: (time.Now().Unix() * 1000) - 30000} //minus 30 seconds

	//validation
	if param.OutletId == 0 {
		return response, utils.ErrWithCode{Code: 402, Message: "invalid outlet id"}
	}

	//TODO: validate outlet

	if param.LastSync < minAllowToSync {
		log.Debug("outlet %s request sales longer tan minAllow. minReq : %v | req : %v", param.OutletId, minAllowToSync, param.LastSync)
		param.LastSync = minAllowToSync
	}

	sales, err := s.repo.FetchSales(param, user)
	if log.IfError(err) || len(sales) == 0 {
		return response, err
	}

	salesIds := make([]string, 0)
	for _, sale := range sales {
		salesIds = append(salesIds, cast.ToString(sale["sales_id"]))
	}
	log.Info("get sales, since %v (%v), got %v data", param.LastSync, time.Unix(param.LastSync/1000, 0), len(sales))

	salesTagChan := make(chan []models.SalesTagEntity)
	salesRefundChan := make(chan []map[string]interface{})
	salesDetailChan := make(chan []map[string]interface{})
	salesVoidChan := make(chan []map[string]interface{})
	salesTaxChan := make(chan []map[string]interface{})
	salesPaymentChan := make(chan []map[string]interface{})
	salesPromotionChan := make(chan []map[string]interface{})
	salesDetailPromotionChan := make(chan []map[string]interface{})

	//get refund
	go func(salesIds []string, dataChan chan []map[string]interface{}) {
		result, err := s.repo.FetchSalesRefund(salesIds...)
		log.IfError(err)
		dataChan <- result
	}(salesIds, salesRefundChan)

	//get sales tag
	go func(param domain.SalesRequest, dataChan chan []models.SalesTagEntity) {
		result, err := s.repo.FetchSalesTag(&domain.SalesTagRequest{OutletId: param.OutletId}, &user)
		log.IfError(err)
		dataChan <- *result
	}(param, salesTagChan)

	//get sales detail
	go func(salesIds []string, dataChan chan []map[string]interface{}) {
		result, err := s.repo.FetchSalesDetail(salesIds...)
		log.IfError(err)
		dataChan <- result
	}(salesIds, salesDetailChan)

	//get salesVoid
	go func(salesIds []string, dataChan chan []map[string]interface{}) {
		result, err := s.repo.FetchSalesVoid(salesIds...)
		log.IfError(err)
		dataChan <- result
	}(salesIds, salesVoidChan)

	//get salesTax
	go func(salesIds []string, dataChan chan []map[string]interface{}) {
		result, err := s.repo.FetchSalesTax(salesIds...)
		log.IfError(err)
		dataChan <- result
	}(salesIds, salesTaxChan)

	//get salesPayment
	go func(salesIds []string, dataChan chan []map[string]interface{}) {
		result, err := s.repo.FetchSalesPayment(salesIds...)
		log.IfError(err)
		dataChan <- result
	}(salesIds, salesPaymentChan)

	//get salesPromotion
	go func(salesIds []string, dataChan chan []map[string]interface{}) {
		result, err := s.repo.FetchSalesPromotion(salesIds...)
		log.IfError(err)
		dataChan <- result
	}(salesIds, salesPromotionChan)

	//get salesDetailPromotion
	go func(salesIds []string, dataChan chan []map[string]interface{}) {
		result, err := s.repo.FetchSalesDetailPromotion(salesIds...)
		log.IfError(err)
		dataChan <- result
	}(salesIds, salesDetailPromotionChan)

	//wait all channel to finish, and capture the value
	salesRefund := <-salesRefundChan
	salesTag := <-salesTagChan
	salesDetail := <-salesDetailChan
	salesVoid := <-salesVoidChan
	salesTax := <-salesTaxChan
	salesPayment := <-salesPaymentChan
	salesPromotion := <-salesPromotionChan
	salesDetailPromotion := <-salesDetailPromotionChan

	//append sales void
	for _, void := range salesVoid {
		void["is_item_void"] = true
		salesDetail = append(salesDetail, salesVoid...)
	}

	//modify sales payment
	for i, payment := range salesPayment {
		if payment["name"] != nil {
			fields := []string{"name", "account_number"}
			salesPayment[i]["bank"] = array.Take(payment, fields...)
			array.Remove(payment, fields...)
		}
	}

	//modify sales detail
	salesDetailPromotionMap := array.GroupBy(salesDetailPromotion, "sales_detail_fkid")
	salesDetailVoidPromotionMap := array.GroupBy(salesDetailPromotion, "sales_void_id")
	for i, detail := range salesDetail {
		promotion := salesDetailVoidPromotionMap[cast.ToString(detail["sales_void_id"])]
		if detail["sales_void_id"] == nil {
			promotion = salesDetailPromotionMap[cast.ToString(detail["sales_detail_id"])]
		}

		promotionValueTotal := 0
		for _, promo := range promotion {
			promotionValueTotal += cast.ToInt(promo["promotion_value"])
		}
		subTotalAfterPromo := (cast.ToInt(detail["qty"]) * utils.ToInt(detail["price"])) - promotionValueTotal
		qty := cast.ToInt(detail["qty"])
		if qty <= 0 {
			qty = 1
		}
		salesDetail[i]["sub_total"] = subTotalAfterPromo
		salesDetail[i]["price"] = subTotalAfterPromo / qty
		salesDetail[i]["promotions"] = promotion
	}

	salesPaymentMap := array.GroupBy(salesPayment, "sales_fkid")
	salesTaxMap := array.GroupBy(salesTax, "sales_fkid")
	salesDetailMap := array.GroupBy(salesDetail, "sales_fkid")
	// salesVoidMap := array.FlatMapArray(salesVoid, "sales_fkid")
	salesPromotionMap := array.FlatMapArray(salesPromotion, "sales_fkid")
	productDetailIds := array.GetUnique(salesDetail, "product_detail_fkid")
	salesRefundMap := array.FlatMapArray(salesRefund, "sales_fkid")
	salesTagMap := make(map[int]interface{})

	for _, tag := range salesTag {
		salesTagMap[tag.SalesTagID] = tag
	}

	//fetch product
	products, err := s.repoProduct.FetchProductByDetailIdSimple(productDetailIds)
	log.IfError(err)
	productMap := array.FlatMapArray(products, "product_detail_id")

	millis := int64(0)
	for i, sale := range sales {
		salesId := cast.ToString(sale["sales_id"])
		sales[i]["payments"] = salesPaymentMap[salesId]
		sales[i]["tax"] = salesTaxMap[salesId]
		sales[i]["reason"] = salesRefundMap[salesId]["reason"]
		sales[i]["details"] = formatSalesDetailWithExtra(salesDetailMap[salesId], productMap)
		sales[i]["promotion"] = salesPromotionMap[salesId]
		sales[i]["sales_tag"] = salesTagMap[cast.ToInt(sale["sales_tag_fkid"])]

		if timeModified := cast.ToInt64(sale["time_modified"]); timeModified > millis {
			millis = timeModified
		}
	}

	response.Data = sales
	response.Millis = millis
	return response, nil
}

func (s *salesUseCase) CreateSalesPayment(salesId string, user domain.UserSession) (map[string]interface{}, error) {
	//check if id is valid
	cart, err := s.repo.FetchSalesCart(salesId, user)
	if log.IfError(err) {
		return nil, err
	}

	if cart.NoNota == "" {
		return nil, fmt.Errorf("cart %v not found", salesId)
	}

	if cart.Status == models.CartStatusPaid {
		return nil, fmt.Errorf("%v already paid", salesId)
	}

	//fetch sales-cart payment log
	salesPayments, err := s.repo.FetchSalesCartPayment(salesId)
	log.IfError(err)

	log.Info("total salesPayments '%v' : %v", salesId, len(salesPayments))
	isPaymentExpired := false
	for _, payment := range salesPayments {
		log.Info("%v: %v", payment.ID, payment.Status)
		if payment.Status == models.CartStatusPaid {
			log.Info("cart '%v' already paid, based on paymentId: %v at: %v", salesId, payment.ID, payment.TimeCreated)
			return nil, fmt.Errorf("%v already paid", salesId)
		} else if payment.Status == models.CartStatusExpire {
			isPaymentExpired = true
		}
	}

	if !isPaymentExpired && len(salesPayments) > 0 && salesPayments[0].Status == models.CartStatusPending {
		var paymentRecord models.CreateBillingResponse
		err = json.Unmarshal([]byte(salesPayments[0].Payload), &paymentRecord)
		timeElapsed := time.Since(time.Unix(salesPayments[0].TimeCreated/1000, 0))
		log.Info("using salesPayment before: %v - since %v", salesPayments[0].ID, timeElapsed)
		if !log.IfError(err) && paymentRecord.TransactionID != "" {
			return map[string]interface{}{
				"payment_method": "qris",
				"transaction_id": paymentRecord.TransactionID,
				"qris": map[string]interface{}{
					"url": paymentRecord.Qris.URL,
				},
			}, nil
		}
	}

	var salesDetail struct {
		Customer     string `json:"customer,omitempty"`
		MemberDetail struct {
			Phone string `json:"phone,omitempty"`
		} `json:"memberDetail,omitempty"`
	}

	var customer models.BillingCustomer
	if !log.IfError(json.Unmarshal([]byte(cart.Sales), &salesDetail)) {
		customer.Name = salesDetail.Customer
		customer.Phone = salesDetail.MemberDetail.Phone
	}

	payment, err := s.repoBilling.CreatePayment(models.CreateBillingRequest{
		Id:            sales.PaymentIdPrefix + salesId,
		PaymentMethod: models.PaymentMethodQris,
		Amount:        cart.GrandTotal,
		AdminId:       user.AdminId,
		Customer:      customer,
		OutletId:      cart.OutletFkid,
	})
	if err != nil {
		return nil, err
	}

	log.Info("payment of '%v' : %v", salesId, utils.SimplyToJson(payment))

	//save payment info to database
	payId, err := s.repo.CreateSalesPayment(models.SalesCartPaymentEntity{
		TmpSalesFkid:  salesId,
		TransactionID: payment.TransactionID,
		PaymentType:   models.PaymentMethodQris,
		Status:        "pending",
		Payload:       utils.SimplyToJson(payment),
		TimeCreated:   time.Now().Unix() * 1000,
	})
	log.IfError(err)
	log.Info("salesPayment '%v' created, id: %v", salesId, payId)

	return map[string]interface{}{
		"payment_method": "qris",
		"transaction_id": payment.TransactionID,
		"qris": map[string]interface{}{
			"url": payment.Qris.URL,
		},
	}, nil
}

func (s salesUseCase) FetchSalesPayment(salesId string, user domain.UserSession) (models.SalesCartPaymentEntity, error) {
	salesPayments, err := s.repo.FetchSalesCartPayment(salesId)
	if log.IfError(err) {
		return models.SalesCartPaymentEntity{}, err
	}

	if len(salesPayments) == 1 {
		return salesPayments[0], nil
	}

	for _, payment := range salesPayments {
		if payment.Status == models.CartStatusPaid {
			return payment, nil
		}
	}

	return models.SalesCartPaymentEntity{}, nil
}

func (s salesUseCase) ReceivePaymentUpdate(payment models.PaymentWebhookPayload) {
	paymentMethod := "qris"

	//do validation (just in case), check cart status
	paymentStatus := "expire"
	if array.Contain([]string{"settlement", "success", "succeeded"}, strings.ToLower(payment.Status)) {
		paymentStatus = "paid"
	}

	// paymentPayload, _ := json.Marshal(payment)

	//fetch payment notification
	notif, err := s.repo.FetchPaymentNotification(payment.TransactionId)
	log.IfError(err)

	if len(*notif) == 0 {
		log.IfError(fmt.Errorf("no payment notification found for '%v'", payment.TransactionId))
	}

	paymentGateway := ""
	if len(*notif) > 0 {
		paymentGateway = (*notif)[0].PaymentGateway
	}

	salesId := strings.Replace(payment.OrderId, sales.PaymentIdPrefix, "", 1)
	payId, err := s.repo.CreateSalesPayment(models.SalesCartPaymentEntity{
		TmpSalesFkid:  salesId,
		TransactionID: payment.TransactionId,
		PaymentType:   models.PaymentMethodQris,
		Status:        paymentStatus,
		Payload:       utils.SimplyToJson(payment),
		TimeCreated:   time.Now().Unix() * 1000,
	})
	log.IfError(err)
	log.Info("salesPayment '%v' created, id: %v, status: %v", salesId, payId, paymentStatus)

	//if not paid, return ...
	if paymentStatus != "paid" {
		return
	}

	cart, err := s.repo.FetchSalesCart(salesId, domain.UserSession{})
	log.IfError(err)
	fmt.Println(cart.Status)

	//update cart status
	err = s.repo.UpdateSalesCart(models.SalesCart{
		NoNota: salesId,
		Status: "paid",
	})
	log.IfError(err)

	//adjust data before saving to sales
	var sales models.Sale
	err = json.Unmarshal([]byte(cart.Sales), &sales)
	log.IfError(err)

	sales.GrandTotal = cart.GrandTotal
	sales.OutletID = cart.OutletFkid
	sales.Payment = "CARD"

	outlet, err := s.repoOutlet.FetchOutletById(cart.OutletFkid)
	log.IfError(err)

	admin, err := s.repo.FetchAdmin(outlet.AdminFkid)
	log.IfError(err)

	//get bank
	banks, err := s.repo.FetchBank(models.BankFilter{ProviderPaymentKey: strings.ToLower(paymentMethod), AdminId: outlet.AdminFkid, Provider: paymentGateway})
	log.IfError(err)

	if len(banks) == 0 {
		newBank := models.Bank{
			AdminFkid:    outlet.AdminFkid,
			DataCreated:  time.Now().Format("2006-01-02 15:04:05"),
			DataModified: time.Now().Format("2006-01-02 15:04:05"),
			DataStatus:   "on",
			Name:         strings.ToUpper(strings.TrimSpace(fmt.Sprintf("%s %s", paymentMethod, strings.ToUpper(paymentGateway)))),
			Provider:     paymentGateway,
			ProviderKey:  strings.ToLower(paymentMethod),
			Owner:        admin.Name,
		}
		id, err := s.repo.AddBank(newBank)
		if !log.IfError(err) {
			newBank.BankID = int(id)
			banks = append(banks, newBank)
		}

		//TODO: activate the new bank to all outlet
		//fetch all outletIds
		outelts, err := s.repoOutlet.FetchOutlet(domain.UserSession{AdminId: int64(outlet.AdminFkid)}, models.OutletRequestFilter{})
		log.IfError(err)

		bankDetail := make([]models.BankDetailEntity, 0)
		for _, outlet := range outelts {
			bankDetail = append(bankDetail, models.BankDetailEntity{
				BankFkid:     int(id),
				OutletFkid:   outlet.OutletID,
				ActiveOnPos:  1,
				ActiveOnCrm:  1,
				DataModified: time.Now().Format("2006-01-02 15:04:05"),
				DataStatus:   "on",
			})
		}

		err = s.repo.AddBankDetail(bankDetail...)
		log.IfError(err)
	}
	sales.Payments = []models.Payment{
		{
			Method: "CARD",
			Total:  cart.GrandTotal,
			Pay:    cart.GrandTotal,
			Bank:   banks[0],
		},
	}

	if sales.OpenShiftID == 0 {
		//get current shift
		openShift, err := s.repo.FetchCurrentShift(cart.OutletFkid)
		log.IfError(err)
		sales.OpenShiftID = int(openShift.OpenShiftId)
	}

	cnt, err := s.repo.CountSales(sales.OpenShiftID)
	log.IfError(err)

	sales.DisplayNota = generate.DisplayNota(outlet.AdminFkid, cart.OutletFkid, cnt)

	salesResp, err := v1.SaveSales(sales, cast.ToString(outlet.AdminFkid), "")
	log.IfError(err)
	fmt.Println(salesResp)

	//push notif
	devices, err := s.repo.FetchDevices(cart.OutletFkid)
	log.IfError(err)

	fmt.Println("total device: ", len(devices))
	if len(devices) == 0 {
		return
	}

	ids := make([]string, 0)
	for _, device := range devices {
		ids = append(ids, device.FirebaseToken)

		err = google.SendNotification(google.NotificationData{
			Token: cast.ToString(device.FirebaseToken),
			Data: map[string]string{
				"type":    "payment_receive",
				"message": "pembayaran telah berhasil di verifikasi",
				"title":   fmt.Sprintf("%s Berhasil Dibayarkan", salesId),
				"id":      salesId,
			},
		})
		log.IfError(err)
	}

	fmt.Println("sending notification about new payment: ", len(ids), "users")
}

// order sales
func (s *salesUseCase) FetchOrderSales(param domain.OrderSalesReqParam, user domain.UserSession) ([]models.OrderSalesEntity, error) {
	orderSales, err := s.repo.FetchOrderSales(param, user)
	if log.IfError(err) {
		return orderSales, err
	}

	//TODO: fetch member data
	return orderSales, nil
}

func (s *salesUseCase) UpdateOrderSales(orderId string, param domain.OrderSalesUpdate, user domain.UserSession) error {
	///validation
	if !array.Contain(domain.OrderStatusList, param.Status) {
		log.Info("invalid status: '%v', orderId: %v", param.Status, orderId)
		return utils.ErrWithCode{Code: 402, Message: fmt.Sprintf("invalid status: '%v'", param.Status)}
	}

	orderSalesList, err := s.repo.FetchOrderSales(domain.OrderSalesReqParam{OrderSalesId: orderId}, user)
	if log.IfError(err) {
		return err
	}

	if len(orderSalesList) == 0 {
		return utils.ErrWithCode{Code: 404, Message: "data not found"}
	}

	if param.Status == orderSalesList[0].Status {
		return utils.ErrWithCode{Code: 402, Message: fmt.Sprintf("status is same as previous: %v", param.Status)}
	}

	// validate status flow: new status must be after current status in domain.OrderStatusList
	var currentIndex, newIndex = -1, -1
	for i, sStatus := range domain.OrderStatusList {
		if sStatus == orderSalesList[0].Status {
			currentIndex = i
		}
		if sStatus == param.Status {
			newIndex = i
		}
	}

	log.Info("update status order %v currentIndex: %v, newIndex: %v | %v -> %v", orderId, currentIndex, newIndex, orderSalesList[0].Status, param.Status)

	// if either status not found, reject
	if currentIndex == -1 || newIndex == -1 {
		log.Info("invalid status flow: current='%v' new='%v'", orderSalesList[0].Status, param.Status)
		return utils.ErrWithCode{Code: 402, Message: fmt.Sprintf("invalid status flow: %v -> %v", orderSalesList[0].Status, param.Status)}
	}

	if newIndex <= currentIndex {
		log.Info("status transition not allowed: currentIndex=%d newIndex=%d", currentIndex, newIndex)
		return utils.ErrWithCode{Code: 402, Message: fmt.Sprintf("status transition not allowed: %v -> %v", orderSalesList[0].Status, param.Status)}
	}

	//update
	err = s.repo.UpdateOrderSales(orderId, param, user)
	if log.IfError(err) {
		return err
	}

	log.Info("orderId: %v updated to status: %v", orderId, param.Status)
	google.PublishMessage(map[string]interface{}{
		"status":         param.Status,
		"order_sales_id": orderId,
		"creator":        fmt.Sprintf("admin:%v", user.AdminId),
	}, fmt.Sprintf("crm-order-status-%s", os.Getenv("ENV")))

	return nil
}

func (s *salesUseCase) FetchSalesTag(param *domain.SalesTagRequest, user *domain.UserSession) (*[]models.SalesTagEntity, error) {
	return s.repo.FetchSalesTag(param, user)
}

func (s *salesUseCase) SendReceipt(salesId string) error {
	// Fetch sales data
	sales, err := s.repo.FetchSalesById(salesId)
	if err != nil {
		return err
	}

	// Fetch outlet info
	outlet, err := s.repo.FetchOutletById(sales.OutletFkid)
	if err != nil {
		return err
	}

	// Fetch sales details with products
	details, err := s.repo.FetchSalesDetailWithProduct(salesId)
	if err != nil {
		return err
	}

	// Process items
	items := make([]models.Items, 0)
	qtyTotal := 0

	for _, order := range details {
		subItems := make([]models.Items, 0)

		// Handle discounts
		if cast.ToInt(order["discount"]) > 0 {
			percentage := ""
			if order["discount_type"] == "percentage" {
				percentage = fmt.Sprintf("(%d%s)", order["discount"], "%")
			}
			subItems = append(subItems, models.Items{
				Product:  "#Discount " + percentage,
				SubTotal: "-" + utils.CurrencyFormat(cast.ToInt(order["discount_nominal"])),
			})
		}

		// Handle vouchers
		if cast.ToInt(order["voucher"]) > 0 {
			percentage := ""
			if order["voucher_type"] == "percentage" {
				percentage = fmt.Sprintf("(%d%s)", order["voucher"], "%")
			}
			subItems = append(subItems, models.Items{
				Product:  "#Voucher " + percentage,
				SubTotal: "-" + utils.CurrencyFormat(cast.ToInt(order["voucher_nominal"])),
			})
		}

		prefix := ""
		if cast.ToBool(order["is_item_void"]) {
			prefix = "VOID: "
			qtyTotal -= cast.ToInt(order["qty"])
		} else {
			qtyTotal += cast.ToInt(order["qty"])
		}

		items = append(items, models.Items{
			Product:  prefix + cast.ToString(order["product_name"]),
			Qty:      utils.CurrencyFormat(cast.ToInt(order["qty"])),
			SubTotal: utils.CurrencyFormat(cast.ToInt(order["sub_total"])),
			Price:    format.Currency(cast.ToInt(order["price"])),
			SubItems: subItems,
		})
	}

	if qtyTotal <= 0 {
		return fmt.Errorf("receipt transaction with id '%s' / '%s' will not send to customer. Because qty is %d",
			sales.SalesID, sales.DisplayNota, qtyTotal)
	}

	// Get point earned if member exists
	var pointEarned int
	if sales.MemberFkid > 0 {
		pointEarned, _ = s.repo.FetchPointEarned(salesId)
	}

	// Get CRM app config
	appConfig, _ := s.repo.FetchAppCrmConfig(cast.ToInt(outlet["admin_fkid"]))
	timeOffset := int64(25200)

	// Build receipt data
	data := models.Receipt{
		ReceiptNo:     sales.DisplayNota,
		Date:          time.Unix((sales.TimeCreated/1000)+timeOffset, 0).Format("02-01-2006 03:04"),
		Outlet:        cast.ToString(outlet["name"]),
		OutletAddress: cast.ToString(outlet["address"]),
		OutletPhone:   cast.ToString(outlet["phone"]),
		OutletLogo:    cast.ToString(outlet["receipt_logo"]),
		Items:         items,
		GrandTotal:    utils.CurrencyFormat(sales.GrandTotal),
		ReceiptSosMed: cast.ToString(outlet["receipt_socialmedia"]),
		ReceiptNote:   strings.TrimSpace(cast.ToString(outlet["receipt_note"])),
		Customer:      sales.CustomerName,
		Payment:       sales.Payment,
		Table:         sales.DiningTable,
	}

	if pointEarned > 0 {
		data.PointName = cast.ToString(utils.OneOfThese(appConfig.Language.Point, "Point"))
		data.PointTotal = pointEarned
	}

	// Send receipt based on receiver type
	if utils.IsNumber(sales.ReceiptReceiver) {
		return s.sendWhatsAppReceipt(data, sales.ReceiptReceiver)
	}

	return s.sendEmailReceipt(data, sales.ReceiptReceiver)
}

func (s *salesUseCase) sendWhatsAppReceipt(receipt models.Receipt, phoneNumber string) error {
	// Implementation for WhatsApp receipt
	return nil
}

func (s *salesUseCase) sendEmailReceipt(receipt models.Receipt, email string) error {
	// Implementation for email receipt
	return nil
}

// FetchTransactionConfig implements sales.UseCase.
func (s *salesUseCase) FetchTransactionConfig(user domain.UserSession) (map[string]interface{}, error) {
	configs, err := s.repo.FetchTransactionConfig(user.AdminId)
	if log.IfError(err) {
		return nil, err
	}

	result := make(map[string]interface{})
	for _, conf := range configs {
		configValueStr := strings.TrimSpace(cast.ToString(conf["value"]))
		var configValueConverted interface{} = configValueStr
		if strings.HasPrefix(configValueStr, "{") {
			configValueConverted = cast.ToMap(configValueStr)
		} else if strings.HasPrefix(configValueStr, "[{") {
			configValueConverted = cast.ToArrayMap(configValueStr)
		}
		result[cast.ToString(conf["config"])] = configValueConverted
	}
	return result, nil
}

// FetchOrderSalesV2 implements sales.UseCase.
func (s *salesUseCase) FetchOrderSalesV2(param domain.OrderSalesV2Request, user domain.UserSession) (models.ResponseArray, error) {
	response := models.ResponseArray{
		Status: true,
		Data:   make([]map[string]interface{}, 0),
		Millis: (time.Now().Unix() * 1000) - 5000,
	}

	// Validation
	if param.OutletId == 0 {
		return response, utils.ErrWithCode{Code: 402, Message: "invalid outlet id"}
	}

	// Fetch order sales data using the new repository method
	orderSalesData, err := s.repo.FetchOrderSalesV2(param, user)
	if err != nil {
		log.IfError(err)
		return response, err
	}

	// Convert to map[string]interface{} slice for response
	result := make([]map[string]interface{}, len(orderSalesData))
	for i, orderSales := range orderSalesData {
		// Convert struct to map using JSON marshal/unmarshal
		jsonData, err := json.Marshal(orderSales)
		if err != nil {
			log.IfError(err)
			continue
		}

		var orderSalesMap map[string]interface{}
		if err := json.Unmarshal(jsonData, &orderSalesMap); err != nil {
			log.IfError(err)
			continue
		}

		result[i] = orderSalesMap
	}

	response.Data = result
	return response, nil
}
