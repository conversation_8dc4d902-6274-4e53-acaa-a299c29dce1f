package usecase

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/models"
	sales "gitlab.com/uniqdev/backend/api-pos/module/sales"
)

func (s *salesUseCase) runSubscription() {
	env := os.Getenv("ENV")
	subsId := fmt.Sprintf("billing_webhook_%s_pos", env)
	google.Subscribe(subsId, func(data []byte) bool {
		return s.receiveWebhook(data)
	})
}

func (s *salesUseCase) receiveWebhook(body []byte) bool {
	var pubsub struct {
		Message struct {
			Attributes struct {
				Key string `json:"key"`
			} `json:"attributes"`
			Data      string `json:"data"`
			MessageID string `json:"messageId"`
		} `json:"message"`
		Subscription string `json:"subscription"`
	}

	log.Info("pubsub received data original: %s", body)
	err := json.NewDecoder(bytes.NewReader(body)).Decode(&pubsub)
	if log.IfError(err) {
		return false
	}

	data, err := base64.StdEncoding.DecodeString(pubsub.Message.Data)
	if log.IfError(err) {
		return false
	}

	if data == nil || pubsub.Message.Data == "" {
		log.Info("data is nill or pubsub.Message.Data is empty, using body instead...")
		data = body
	}

	log.Info("pubsub receive data (payment): %s", string(data))
	var payload map[string]interface{}
	if log.IfError(json.Unmarshal(data, &payload)) {
		return false
	}

	paymentData := getTransactionDetail(payload)
	log.Info("order id: '%s' | transId: %v | status: %v", paymentData.OrderId, paymentData.TransactionId, paymentData.Status)

	if strings.HasPrefix(paymentData.OrderId, sales.PaymentIdPrefix) {
		log.Info("payment handled by api pos...")
		go s.ReceivePaymentUpdate(paymentData)
	} else {
		log.Info("api pos not handle this payment")
	}

	return true
}

func getTransactionDetail(payload map[string]interface{}) models.PaymentWebhookPayload {
	if cast.ToString(payload["payment_gateway"]) == "xendit" {
		var paymentXendit models.PaymentWebhookXendit
		payloadData, err := json.Marshal(payload["payload"])
		log.IfError(err)
		fmt.Println("payload data --> ", string(payloadData))
		log.IfError(json.Unmarshal([]byte(payloadData), &paymentXendit))
		return paymentXendit.ToPayload()
	}

	if payloadData, ok := payload["payload"].(map[string]interface{}); ok {
		return models.PaymentWebhookPayload{
			TransactionId: strings.TrimSpace(cast.ToString(payloadData["transaction_id"])),
			OrderId:       strings.TrimSpace(cast.ToString(payloadData["order_id"])),
			Status:        cast.ToString(payloadData["transaction_status"]),
		}
	}
	return models.PaymentWebhookPayload{}
}
