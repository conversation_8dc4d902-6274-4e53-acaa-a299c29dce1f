package usecase

import (
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/array"
)

func formatSalesDetailWithExtra(salesDetail []map[string]interface{}, productMap map[string]map[string]interface{}) []map[string]interface{} {
	salesDetailWithExtra := make([]map[string]interface{}, 0)
	extraMap := make(map[string][]map[string]interface{})
	for _, detail := range salesDetail {
		product := productMap[cast.ToString(detail["product_detail_fkid"])]
		// detail["name"] = product["name"]
		// detail["product_subcategory_fkid"] = product["product_subcategory_fkid"]

		array.Merge(detail, product)

		if detail["parent"] != nil {
			parentId := cast.ToString(detail["parent"])
			extraMap[parentId] = append(extraMap[parentId], detail)
		} else {
			salesDetailWithExtra = append(salesDetailWithExtra, detail)
		}
	}

	for i, detail := range salesDetailWithExtra {
		parentId := cast.ToString(detail["sales_detail_id"])
		if _, ok := extraMap[parentId]; ok {
			salesDetailWithExtra[i]["extra"] = extraMap[parentId]
		}
	}
	return salesDetailWithExtra
}
