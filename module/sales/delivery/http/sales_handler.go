package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	sales "gitlab.com/uniqdev/backend/api-pos/module/sales"
)

type salesHandler struct {
	uc sales.UseCase
}

func NewHttpSalesHandler(app *fasthttprouter.Router, useCase sales.UseCase) {
	handler := &salesHandler{useCase}
	app.POST("/v2/sales", auth.ValidateToken(handler.AddSales))
	app.POST("/v1/sales-payment", auth.ValidateToken(handler.CreateSalesPayment))
	app.GET("/v1/sales-payment/:id", auth.ValidateToken(handler.FetchSalesPayment))

	app.GET("/v2/order-sales", auth.ValidateToken(handler.FetchOrderSalesV2))
	// app.GET("/v2/order-sales-v2", auth.ValidateToken(handler.FetchOrderSalesV2))
	app.PATCH("/v2/order-sales/:id", auth.ValidateToken(handler.UpdateOrderSales))

	app.GET("/v1/sales-tag", auth.ValidateToken(handler.FetchSalesTag))
	app.GET("/v2/sales", auth.ValidateToken(handler.FetchSales))

	//transaction config
	app.GET("/v1/transaction-config", auth.ValidateToken(handler.FetchTransactionConfig))
}

func (h *salesHandler) AddSales(ctx *fasthttp.RequestCtx) {
	// Parse the request body
	// var sales models.Sales
	// err := json.Unmarshal(ctx.PostBody(), &sales)
	// if err != nil {
	// 	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
	// 	return
	// }

	// // Call the use case to add the sales
	// resp, err := h.uc.AddSales(sales, domain.UserSessionFastHttp(ctx))
	// if err != nil {
	// 	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
	// 	return
	// }

	// // Encode the response
	// _ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Data: resp})
}

func (h *salesHandler) CreateSalesPayment(ctx *fasthttp.RequestCtx) {
	salesId := string(ctx.PostArgs().Peek("id"))
	if salesId == "" {
		salesId = string(ctx.FormValue("id"))
	}

	if salesId == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "id is required"})
		return
	}
	resp, err := h.uc.CreateSalesPayment(salesId, domain.UserSessionFastHttp(ctx))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Data: resp})
}

func (h *salesHandler) FetchSalesPayment(ctx *fasthttp.RequestCtx) {
	salesId := cast.ToString(ctx.UserValue("id"))
	result, err := h.uc.FetchSalesPayment(salesId, domain.UserSessionFastHttp(ctx))
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Data: result})
}

func (h *salesHandler) FetchOrderSales(ctx *fasthttp.RequestCtx) {
	param := domain.OrderSalesReqParam{
		LastSync: cast.ToInt64(ctx.QueryArgs().Peek("last-sync")),
	}
	h.uc.FetchOrderSales(param, domain.UserSessionFastHttp(ctx))
}

func (h *salesHandler) UpdateOrderSales(ctx *fasthttp.RequestCtx) {
	param := domain.OrderSalesUpdate{
		Status:     string(ctx.PostArgs().Peek("status")),
		EmployeeId: cast.ToInt(string(ctx.PostArgs().Peek("employee_id"))),
		Message:    string(ctx.PostArgs().Peek("message")),
	}
	err := h.uc.UpdateOrderSales(cast.ToString(ctx.UserValue("id")), param, domain.UserSessionFastHttp(ctx))
	if err != nil {
		response := models.ResponseAny{Status: false, Message: err.Error()}
		if errWithCode := err.(utils.ErrWithCode); errWithCode.Code != 0 {
			ctx.SetStatusCode(errWithCode.Code)
			response.Code = errWithCode.Code
		} else {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		}
		_ = json.NewEncoder(ctx).Encode(response)
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success"})
}

func (h *salesHandler) FetchSalesTag(ctx *fasthttp.RequestCtx) {
	param := domain.SalesTagRequest{
		OutletId: cast.ToInt(string(ctx.QueryArgs().Peek("outlet_id"))),
	}
	user := domain.UserSessionFastHttp(ctx)
	result, err := h.uc.FetchSalesTag(&param, &user)
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Data: result})
}

func (h *salesHandler) FetchSales(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	param := domain.SalesRequest{
		OutletId: cast.ToInt(string(ctx.QueryArgs().Peek("outlet-id"))),
		LastSync: cast.ToInt64(string(ctx.QueryArgs().Peek("last-sync"))),
	}
	result, err := h.uc.FetchSales(param, user)
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h *salesHandler) FetchTransactionConfig(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	result, err := h.uc.FetchTransactionConfig(user)
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Data: result})
}

func (h *salesHandler) FetchOrderSalesV2(ctx *fasthttp.RequestCtx) {
	user := domain.UserSessionFastHttp(ctx)
	param := domain.OrderSalesV2Request{
		OutletId: cast.ToInt(string(ctx.QueryArgs().Peek("outlet_id"))),
		LastSync: cast.ToInt64(string(ctx.QueryArgs().Peek("last_sync"))),
	}

	result, err := h.uc.FetchOrderSalesV2(param, user)
	if err != nil {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}
