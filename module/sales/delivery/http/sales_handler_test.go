package http

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

// MockSalesUseCase is a mock implementation of sales.UseCase
type MockSalesUseCase struct {
	mock.Mock
}

func (m *MockSalesUseCase) FetchSales(param domain.SalesRequest, user domain.UserSession) (models.ResponseArray, error) {
	args := m.Called(param, user)
	return args.Get(0).(models.ResponseArray), args.Error(1)
}

func (m *MockSalesUseCase) CreateSalesPayment(salesId string, user domain.UserSession) (map[string]interface{}, error) {
	args := m.Called(salesId, user)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockSalesUseCase) FetchSalesPayment(salesId string, user domain.UserSession) (models.SalesCartPaymentEntity, error) {
	args := m.Called(salesId, user)
	return args.Get(0).(models.SalesCartPaymentEntity), args.Error(1)
}

func (m *MockSalesUseCase) FetchOrderSales(param domain.OrderSalesReqParam, user domain.UserSession) ([]models.OrderSalesEntity, error) {
	args := m.Called(param, user)
	return args.Get(0).([]models.OrderSalesEntity), args.Error(1)
}

func (m *MockSalesUseCase) UpdateOrderSales(orderId string, param domain.OrderSalesUpdate, user domain.UserSession) error {
	args := m.Called(orderId, param, user)
	return args.Error(0)
}

func (m *MockSalesUseCase) FetchSalesTag(param *domain.SalesTagRequest, user *domain.UserSession) (*[]models.SalesTagEntity, error) {
	args := m.Called(param, user)
	return args.Get(0).(*[]models.SalesTagEntity), args.Error(1)
}

func (m *MockSalesUseCase) SendReceipt(salesId string) error {
	args := m.Called(salesId)
	return args.Error(0)
}

func (m *MockSalesUseCase) FetchTransactionConfig(user domain.UserSession) (map[string]interface{}, error) {
	args := m.Called(user)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockSalesUseCase) FetchOrderSalesV2(param domain.OrderSalesV2Request, user domain.UserSession) (models.ResponseArray, error) {
	args := m.Called(param, user)
	return args.Get(0).(models.ResponseArray), args.Error(1)
}

func TestFetchOrderSalesV2(t *testing.T) {
	// Create mock usecase
	mockUC := new(MockSalesUseCase)
	handler := &salesHandler{uc: mockUC}

	// Test data
	expectedResponse := models.ResponseArray{
		Status: true,
		Data: []map[string]interface{}{
			{
				"id":              1,
				"order_sales_id":  "ORDER123",
				"member_fkid":     123,
				"outlet_fkid":     1,
				"order_type":      "dine_in",
				"status":          "pending",
				"time_order":      1640995200000,
				"time_modified":   1640995200000,
				"grand_total":     50000,
				"member": map[string]interface{}{
					"member_id": 123,
					"name":      "John Doe",
					"phone":     "081234567890",
					"email":     "<EMAIL>",
				},
			},
		},
		Millis: 1640995200000,
	}

	// Setup mock expectations
	mockUC.On("FetchOrderSalesV2", mock.AnythingOfType("domain.OrderSalesV2Request"), mock.AnythingOfType("domain.UserSession")).Return(expectedResponse, nil)

	// Create test request context
	ctx := &fasthttp.RequestCtx{}
	ctx.Request.SetRequestURI("/v2/order-sales-v2?outlet_id=1&last_sync=1640995200000")
	ctx.Request.Header.Set("admin_id", "1")
	ctx.Request.Header.Set("outlet_id", "1")

	// Call the handler
	handler.FetchOrderSalesV2(ctx)

	// Verify the response
	var response models.ResponseArray
	err := json.Unmarshal(ctx.Response.Body(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Status)
	assert.Len(t, response.Data, 1)

	// Verify mock was called
	mockUC.AssertExpectations(t)
}

func TestFetchOrderSalesV2_WithMetaData(t *testing.T) {
	// Create mock usecase
	mockUC := new(MockSalesUseCase)
	handler := &salesHandler{uc: mockUC}

	// Test data with meta_data instead of member
	expectedResponse := models.ResponseArray{
		Status: true,
		Data: []map[string]interface{}{
			{
				"id":              1,
				"order_sales_id":  "ORDER124",
				"member_fkid":     0, // No member
				"outlet_fkid":     1,
				"order_type":      "delivery",
				"status":          "pending",
				"time_order":      1640995200000,
				"time_modified":   1640995200000,
				"grand_total":     75000,
				"meta_data": map[string]interface{}{
					"name":  "Jane Doe",
					"phone": "081234567891",
					"table": "12",
				},
			},
		},
		Millis: 1640995200000,
	}

	// Setup mock expectations
	mockUC.On("FetchOrderSalesV2", mock.AnythingOfType("domain.OrderSalesV2Request"), mock.AnythingOfType("domain.UserSession")).Return(expectedResponse, nil)

	// Create test request context
	ctx := &fasthttp.RequestCtx{}
	ctx.Request.SetRequestURI("/v2/order-sales-v2?outlet_id=1&last_sync=1640995200000")
	ctx.Request.Header.Set("admin_id", "1")
	ctx.Request.Header.Set("outlet_id", "1")

	// Call the handler
	handler.FetchOrderSalesV2(ctx)

	// Verify the response
	var response models.ResponseArray
	err := json.Unmarshal(ctx.Response.Body(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Status)
	assert.Len(t, response.Data, 1)

	// Check that meta_data is present
	orderSales := response.Data[0]
	assert.Contains(t, orderSales, "meta_data")
	metaData := orderSales["meta_data"].(map[string]interface{})
	assert.Equal(t, "Jane Doe", metaData["name"])
	assert.Equal(t, "081234567891", metaData["phone"])
	assert.Equal(t, "12", metaData["table"])

	// Verify mock was called
	mockUC.AssertExpectations(t)
}
