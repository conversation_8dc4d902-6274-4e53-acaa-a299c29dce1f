package usecase

import (
	"strings"

	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	kitchen "gitlab.com/uniqdev/backend/api-pos/module/kitchen"
)

type kitchenUseCase struct {
	repo kitchen.Repository
}

func NewKitchenUseCase(repository kitchen.Repository) kitchen.UseCase {
	return &kitchenUseCase{repository}
}

func (k *kitchenUseCase) GetKdsToken(userSession domain.UserSession) (*models.AuthToken, error) {
	outletIdStr := make([]string, 0)
	for _, outletId := range userSession.AuthorizedOutletIds {
		outletIdStr = append(outletIdStr, cast.ToString(outletId))
	}

	// Generate Token
	jwtAuth := auth.InitJWTAuth()
	authToken := jwtAuth.GenerateTokenWithOptions(auth.GenerateTokenOptions{
		Id:            utils.Encrypt(cast.ToString(userSession.AdminId)),
		AccessAllowed: strings.Join(outletIdStr, ","),
		Role:          userSession.Role,
		AccessLimitUrls: []string{
			"kitchen-display",
			"kds",
		},
	})
	return authToken, nil
}
