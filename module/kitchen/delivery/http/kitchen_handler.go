package http

import (
	"encoding/json"
	"time"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	kitchen "gitlab.com/uniqdev/backend/api-pos/module/kitchen"
)

type kitchenHandler struct {
	uc kitchen.UseCase
}

func NewHttpKitchenHandler(app *fasthttprouter.Router, useCase kitchen.UseCase) {
	handler := &kitchenHandler{useCase}

	app.GET("/v1/kitchen-display-token", auth.ValidateToken(handler.GetKdsToken))
}

func (h *kitchenHandler) GetKdsToken(ctx *fasthttp.RequestCtx) {
	userSession := domain.UserSessionFastHttp(ctx)

	token, err := h.uc.GetKdsToken(userSession)
	if err != nil {
		log.Info("err: %v", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Millis: time.Now().Unix() * 1000, Message: "Failed to get kds token"})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Data: token})
}
