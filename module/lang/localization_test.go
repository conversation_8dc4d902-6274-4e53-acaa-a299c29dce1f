package lang

import "testing"

func Test_selectedLanguage_String(t *testing.T) {
	type fields struct {
		vocab map[lang<PERSON>ey]string
	}
	type args struct {
		key  langKey
		args []interface{}
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{"no-arg", fields{vocab: en}, args{key: (PleaseGiveFeedback)}, en[(PleaseGiveFeedback)]},
		{"with-arg", fields{vocab: en}, args{key: Welcome, args: []interface{}{"Anna<PERSON>"}}, "welcome <PERSON><PERSON>"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &selectedLanguage{
				vocab: tt.fields.vocab,
			}
			if got := s.String(tt.args.key, tt.args.args...); got != tt.want {
				t.Errorf("selectedLanguage.String() = %v, want %v", got, tt.want)
			}
		})
	}
}
