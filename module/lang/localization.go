package lang

import "fmt"

type langId int64

const (
	En langId = iota
	Id
)

type selectedLanguage struct {
	vocab map[langKey]string
}

func New(languageId langId) *selectedLanguage {
	langMap := map[langId]map[langKey]string{En: en, Id: id}
	return &selectedLanguage{
		vocab: langMap[languageId],
	}
}

func IdFromString(lng string) langId {
	if lng == "id" {
		return Id
	}
	return En
}

func (s *selectedLanguage) String(key langKey, args ...interface{}) string {
	fmt.Printf("key: %c", key)
	return fmt.Sprintf(s.vocab[key], args...)
}
