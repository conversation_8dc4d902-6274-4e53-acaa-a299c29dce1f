package mysql

import (
	"database/sql"

	db "gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/module/setting"
)

type settingRepository struct {
	db db.Repository
}

func NewMysqlSettingRepository(conn *sql.DB) setting.Repository {
	return &settingRepository{db.Repository{Conn: conn}}
}

func (r *settingRepository) FetchTransactionSettings(adminID int, filter domain.SettingTransactionFilter) ([]domain.SettingTransaction, error) {
	sql := `SELECT st.* from setting_transaction st
		JOIN outlets ON st.outlet_id = outlets.outlet_id
		WHERE outlets.admin_fkid = @adminId`

	params := map[string]any{
		"adminId": adminID,
	}

	if filter.CreatedAfter > 0 {
		sql += ` AND created_at > @createdAfter`
		params["createdAfter"] = filter.CreatedAfter
	}

	if len(filter.OutletIDs) > 0 {
		sql += ` AND st.outlet_id IN @outletIds`
		params["outletIds"] = filter.OutletIDs
	}

	sql += ` ORDER BY st.display_order`

	sql, sqlParams := db.MapParam(sql, params)
	var results []domain.SettingTransaction
	err := r.db.Set(sql, sqlParams...).Get(&results)
	return results, err
}
