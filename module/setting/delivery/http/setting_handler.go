package http

import (
	"encoding/json"
	"strings"
	"time"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"gitlab.com/uniqdev/backend/api-pos/module/setting"
)

type settingHandler struct {
	uc setting.UseCase
}

func NewHttpSettingHandler(app *fasthttprouter.Router, useCase setting.UseCase) {
	handler := &settingHandler{useCase}
	app.GET("/v1/settings/transaction", auth.ValidateToken(handler.GetTransactionSettings))
}

func (h *settingHandler) GetTransactionSettings(ctx *fasthttp.RequestCtx) {
	filter := domain.SettingTransactionFilter{
		CreatedAfter: cast.ToInt64(ctx.QueryArgs().Peek("created_after")),
	}

	// Parse outlet_ids from query parameter (comma-separated)
	if outletIdsStr := string(ctx.QueryArgs().Peek("outlet_ids")); outletIdsStr != "" {
		var outletIDs []int64
		for _, idStr := range strings.Split(outletIdsStr, ",") {
			if id := cast.ToInt64(idStr); id > 0 {
				outletIDs = append(outletIDs, id)
			}
		}
		filter.OutletIDs = outletIDs
	}

	result, err := h.uc.GetTransactionSettings(domain.UserSessionFastHttp(ctx), filter)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{
		Status: true,
		Millis: time.Now().UnixMilli(),
		Data:   result,
	})
}
