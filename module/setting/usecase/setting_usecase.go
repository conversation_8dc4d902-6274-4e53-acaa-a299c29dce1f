package usecase

import (
	"errors"

	"gitlab.com/uniqdev/backend/api-pos/domain"
	"gitlab.com/uniqdev/backend/api-pos/module/setting"
)

type settingUseCase struct {
	repo setting.Repository
}

func NewSettingUseCase(repository setting.Repository) setting.UseCase {
	return &settingUseCase{repository}
}

func (uc *settingUseCase) GetTransactionSettings(user domain.UserSession, filter domain.SettingTransactionFilter) ([]domain.SettingTransaction, error) {
	if user.AdminId == 0 {
		return nil, errors.New("unauthorized")
	}

	return uc.repo.FetchTransactionSettings(int(user.AdminId), filter)
}
