UNIQ POS - CLOSE SHIFT REPORT
_#Outlet_  : {{.OutletName}}
_#Kasir_   : {{.EmployeeName}}
_#Shift_   : {{.ShiftName}}
_#Tanggal_ : {{.Date}}

========== SALES ==========
*QTY Item Sales*     : {{.ItemSalesQty}}
*Nominal Item Sales* : {{Currency (Add .ItemSalesTotal .DiscTotal)}}
*Discount Sales*     : {{Currency .DiscTotal}}
========================== +
*NET SALES*            {{Currency .ItemSalesTotal}}

========== MEDIA ==========
*Cash*     : {{Currency .TotalCash}}
*Card*     : {{Currency .TotalCard}}
{{range .BankDetail -}}
- {{.BankName}}   {{Currency .Total}}
{{end -}}
*Piutang*  : {{Currency .TotalPiutang}}
========================== +
*TOTAL*      {{Currency (Add .TotalCash .TotalCard .TotalPiutang)}}

*Total Refund* : {{Currency .TotalRefund}}
*Total Void*   : {{Currency .TotalVoid}}
{{if ne .TotalPax .BillCount -}}
*Total Pax*    : {{Currency .TotalPax}}
*AVG Pax*      : {{Currency .AvgPax}}
{{end -}}
*Total Bill*   : {{Currency .BillCount}}
*AVG Bill*     : {{Currency .AvgBill}}
{{- if ne .TotalOperationalCost 0}}
*Operational*  : {{Currency .TotalOperationalCost}}
{{- end}}

========== AKTUAL =========
*CASH* : {{Currency .ActualCash}}
*CARD* : {{Currency .ActualCard}}
========================== +
*TOTAL*  {{Currency (Add .ActualCash .ActualCard)}}
_selisih {{Currency (Minus (Add .ActualCash .ActualCard) .ItemSalesTotal)}}_

{{- if ne .CommissionTotal 0}}

===== SHARING COMMISSION ====
*({{.CommissionPercentage}})*          {{Currency .CommissionTotal}}
{{- end}}

detail: y.uniq.id/r-closing

