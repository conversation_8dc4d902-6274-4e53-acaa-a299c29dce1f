<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<HTML>

<HEAD>
  <META http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <META http-equiv="X-UA-Compatible" content="IE=8">
  <title>Invoice</title>
  <META name="generator" content="BCL easyConverter SDK 5.0.140">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.0/css/bootstrap.min.css">
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.0/js/bootstrap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment.min.js"></script>
  <STYLE type="text/css">
  body {
    margin-top: 0px;
    margin-left: 0px;
  }


  #page_1 {
    position: relative;
    margin: 100px auto;
    padding: 0px;
    border: none;
    width: 816px;
    height: 895px;
  }

  #page_1 #p1dimg1 {
    position: absolute;
    top: 0px;
    left: 53px;
    z-index: -1;
    width: 710px;
    height: 895px;
  }

  #page_1 #p1dimg1 #p1img1 {
    width: 710px;
    height: 895px;
  }




  .dclr {
    clear: both;
    float: none;
    height: 1px;
    margin: 0px;
    padding: 0px;
    overflow: hidden;
  }

  .ft0 {
    font: bold 16px 'Arial';
    line-height: 19px;
  }

  .ft1 {
    font: 15px 'Arial';
    line-height: 17px;
  }

  .ft2 {
    font: bold 15px 'Arial';
    line-height: 18px;
  }

  .ft3 {
    font: 1px 'Arial';
    line-height: 2px;
  }

  .ft4 {
    font: bold 16px 'Arial';
    color: #ffffff;
    line-height: 19px;
  }

  .ft5 {
    font: bold 16px 'Arial';
    color: #ffffff;
    background-color: #454647;
    line-height: 19px;
  }

  .ft6 {
    font: 1px 'Arial';
    line-height: 1px;
  }

  .ft7 {
    font: 1px 'Arial';
    line-height: 5px;
  }

  .ft8 {
    font: 15px 'Arial';
    color: #404040;
    line-height: 17px;
  }

  .ft9 {
    font: 1px 'Arial';
    line-height: 7px;
  }

  .ft10 {
    font: bold 15px 'Arial';
    color: #404040;
    line-height: 18px;
  }

  .ft11 {
    font: bold 15px 'Arial';
    color: #ffffff;
    line-height: 18px;
  }

  .ft12 {
    font: 1px 'Arial';
    line-height: 6px;
  }

  .ft13 {
    font: bold 15px 'Arial';
    color: #262626;
    line-height: 18px;
  }

  .ft14 {
    font: 15px 'Arial';
    color: #404040;
    line-height: 16px;
  }

  .ft15 {
    font: bold 15px 'Arial';
    color: #ff0000;
    line-height: 17px;
  }

  .ft16 {
    font: 15px 'Arial';
    color: #404040;
    margin-left: 19px;
    line-height: 17px;
  }

  .ft17 {
    font: bold 15px 'Arial';
    color: #404040;
    line-height: 17px;
  }

  .ft18 {
    font: italic 11px 'Arial';
    line-height: 14px;
  }

  .ft19 {
    font: bold 15px 'Calibri';
    line-height: 18px;
  }

  .ft20 {
    font: 13px 'Calibri';
    line-height: 15px;
  }

  .ft21 {
    font: 13px 'Calibri';
    text-decoration: underline;
    color: #0000ff;
    line-height: 15px;
  }

  .ft22 {
    font: 13px 'Calibri';
    color: #0000ff;
    line-height: 15px;
  }

  .p0 {
    text-align: right;
    padding-right: 72px;
    margin-top: 1px;
    margin-bottom: 0px;
  }

  .p1 {
    text-align: right;
    padding-right: 72px;
    margin-top: 12px;
    margin-bottom: 0px;
  }

  .p2 {
    text-align: left;
    padding-left: 72px;
    padding-right: 104px;
    margin-top: 25px;
    margin-bottom: 0px;
  }

  .p3 {
    text-align: left;
    margin-top: 0px;
    margin-bottom: 0px;
    white-space: nowrap;
  }

  .p4 {
    text-align: left;
    padding-left: 14px;
    margin-top: 0px;
    margin-bottom: 0px;
    white-space: nowrap;
  }

  .p5 {
    text-align: left;
    padding-left: 2px;
    margin-top: 0px;
    margin-bottom: 0px;
    white-space: nowrap;
  }

  .p6 {
    text-align: left;
    padding-left: 48px;
    margin-top: 0px;
    margin-bottom: 0px;
    white-space: nowrap;
  }

  .p7 {
    text-align: left;
    padding-left: 54px;
    margin-top: 0px;
    margin-bottom: 0px;
    white-space: nowrap;
  }

  .p8 {
    text-align: right;
    padding-right: 22px;
    margin-top: 0px;
    margin-bottom: 0px;
    white-space: nowrap;
  }

  .p9 {
    text-align: right;
    padding-right: 31px;
    margin-top: 0px;
    margin-bottom: 0px;
    white-space: nowrap;
  }

  .p10 {
    text-align: center;
    padding-right: 8px;
    margin-top: 0px;
    margin-bottom: 0px;
    white-space: nowrap;
  }

  .p11 {
    text-align: right;
    padding-right: 7px;
    margin-top: 0px;
    margin-bottom: 0px;
    white-space: nowrap;
  }

  .p12 {
    text-align: left;
    padding-left: 121px;
    margin-top: 0px;
    margin-bottom: 0px;
    white-space: nowrap;
  }

  .p13 {
    text-align: right;
    margin-top: 0px;
    margin-bottom: 0px;
    white-space: nowrap;
  }

  .p14 {
    text-align: left;
    padding-left: 72px;
    margin-top: 30px;
    margin-bottom: 0px;
  }

  .p15 {
    text-align: left;
    padding-left: 72px;
    margin-top: 4px;
    margin-bottom: 0px;
  }

  .p16 {
    text-align: left;
    padding-left: 30px;
    margin-top: 0px;
    margin-bottom: 0px;
    white-space: nowrap;
  }

  .p17 {
    text-align: left;
    padding-left: 72px;
    margin-top: 33px;
    margin-bottom: 0px;
  }

  .p18 {
    text-align: left;
    padding-left: 96px;
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .p19 {
    text-align: left;
    padding-left: 120px;
    padding-right: 116px;
    margin-top: 1px;
    margin-bottom: 0px;
    text-indent: -24px;
  }

  .p20 {
    text-align: left;
    padding-left: 72px;
    margin-top: 32px;
    margin-bottom: 0px;
  }

  .p21 {
    text-align: left;
    padding-left: 72px;
    padding-right: 115px;
    margin-top: 5px;
    margin-bottom: 0px;
  }

  .p22 {
    text-align: left;
    padding-left: 535px;
    margin-top: 40px;
    margin-bottom: 0px;
  }

  .p23 {
    text-align: left;
    padding-left: 72px;
    margin-top: 10px;
    margin-bottom: 0px;
  }

  .p24 {
    text-align: left;
    padding-left: 72px;
    margin-top: 18px;
    margin-bottom: 0px;
  }

  .p25 {
    text-align: left;
    padding-left: 72px;
    margin-top: 1px;
    margin-bottom: 0px;
  }

  .td0 {
    border-left: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 7px;
    vertical-align: bottom;
    background: #454647;
  }

  .td1 {
    padding: 0px;
    margin: 0px;
    width: 53px;
    vertical-align: bottom;
    background: #454647;
  }

  .td2 {
    border-right: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 7px;
    vertical-align: bottom;
    background: #454647;
  }

  .td3 {
    padding: 0px;
    margin: 0px;
    width: 6px;
    vertical-align: bottom;
    background: #454647;
  }

  .td4 {
    padding: 0px;
    margin: 0px;
    width: 228px;
    vertical-align: bottom;
    background: #454647;
  }

  .td5 {
    padding: 0px;
    margin: 0px;
    width: 7px;
    vertical-align: bottom;
    background: #454647;
  }

  .td6 {
    padding: 0px;
    margin: 0px;
    width: 70px;
    vertical-align: bottom;
    background: #454647;
  }

  .td7 {
    padding: 0px;
    margin: 0px;
    width: 156px;
    vertical-align: bottom;
    background: #454647;
  }

  .td8 {
    padding: 0px;
    margin: 0px;
    width: 107px;
    vertical-align: bottom;
    background: #454647;
  }

  .td9 {
    border-left: #454647 1px solid;
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 7px;
    vertical-align: bottom;
    background: #454647;
  }

  .td10 {
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 53px;
    vertical-align: bottom;
    background: #454647;
  }

  .td11 {
    border-right: #454647 1px solid;
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 7px;
    vertical-align: bottom;
    background: #454647;
  }

  .td12 {
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 6px;
    vertical-align: bottom;
    background: #454647;
  }

  .td13 {
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 228px;
    vertical-align: bottom;
    background: #454647;
  }

  .td14 {
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 7px;
    vertical-align: bottom;
    background: #454647;
  }

  .td15 {
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 70px;
    vertical-align: bottom;
    background: #454647;
  }

  .td16 {
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 156px;
    vertical-align: bottom;
    background: #454647;
  }

  .td17 {
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 107px;
    vertical-align: bottom;
    background: #454647;
  }

  .td18 {
    border-left: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 60px;
    vertical-align: bottom;
  }

  .td19 {
    border-right: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 7px;
    vertical-align: bottom;
  }

  .td20 {
    padding: 0px;
    margin: 0px;
    width: 6px;
    vertical-align: bottom;
  }

  .td21 {
    border-right: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 235px;
    vertical-align: bottom;
  }

  .td22 {
    padding: 0px;
    margin: 0px;
    width: 77px;
    vertical-align: bottom;
  }

  .td23 {
    padding: 0px;
    margin: 0px;
    width: 7px;
    vertical-align: bottom;
  }

  .td24 {
    border-right: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 163px;
    vertical-align: bottom;
  }

  .td25 {
    border-right: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 114px;
    vertical-align: bottom;
  }

  .td26 {
    border-left: #454647 1px solid;
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 7px;
    vertical-align: bottom;
  }

  .td27 {
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 53px;
    vertical-align: bottom;
  }

  .td28 {
    border-right: #454647 1px solid;
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 7px;
    vertical-align: bottom;
  }

  .td29 {
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 6px;
    vertical-align: bottom;
  }

  .td30 {
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 228px;
    vertical-align: bottom;
  }

  .td31 {
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 7px;
    vertical-align: bottom;
  }

  .td32 {
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 70px;
    vertical-align: bottom;
  }

  .td33 {
    border-right: #454647 1px solid;
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 163px;
    vertical-align: bottom;
  }

  .td34 {
    border-right: #454647 1px solid;
    border-bottom: #454647 1px solid;
    padding: 0px;
    margin: 0px;
    width: 114px;
    vertical-align: bottom;
  }

  .td35 {
    padding: 0px;
    margin: 0px;
    width: 8px;
    vertical-align: bottom;
  }

  .td36 {
    padding: 0px;
    margin: 0px;
    width: 53px;
    vertical-align: bottom;
  }

  .td37 {
    padding: 0px;
    margin: 0px;
    width: 228px;
    vertical-align: bottom;
  }

  .td38 {
    padding: 0px;
    margin: 0px;
    width: 70px;
    vertical-align: bottom;
  }

  .td39 {
    padding: 0px;
    margin: 0px;
    width: 156px;
    vertical-align: bottom;
  }

  .td40 {
    padding: 0px;
    margin: 0px;
    width: 102px;
    vertical-align: bottom;
  }

  .td41 {
    padding: 0px;
    margin: 0px;
    width: 229px;
    vertical-align: bottom;
  }

  .tr0 {
    height: 2px;
  }

  .tr1 {
    height: 24px;
  }

  .tr2 {
    height: 22px;
  }

  .tr3 {
    height: 5px;
  }

  .tr4 {
    height: 23px;
  }

  .tr5 {
    height: 7px;
  }

  .tr6 {
    height: 6px;
  }

  .tr7 {
    height: 16px;
  }

  .tr8 {
    height: 17px;
  }

  .t0 {
    width: 687px;
    margin-left: 64px;
    margin-top: 27px;
    font: bold 16px 'Arial';
    color: #ffffff;
  }

  .t1 {
    width: 331px;
    margin-left: 72px;
    font: 15px 'Arial';
    color: #404040;
  }

  .brightness {
    filter: brightness(0.25);
  }

  #print {
    cursor: pointer;
  }

  .table-services {
    color: black;
    padding: 20;
    border-collapse: collapse;
  }

  .table-services td {
    border: 2px solid black;
  }

  .table-services th {
    text-align: center;
    background-color: gray;
    border: 2px solid black;
  }

  .paid-stamp {
    position: absolute;
    top: 43%;
    left: 67%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    opacity: 0.3;
  }
  </STYLE>
</HEAD>

<BODY>
  
  <DIV id="page_1">
    <span id="print" onClick="window.print()">Print <span class="glyphicon glyphicon-print"></span></span>
    <DIV id="p1dimg1">
      <img
        src='data:image/jpeg;base64,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' />
    </DIV>

      {{if eq (toLower .BillingStatus) "pending"}}
          <!-- ... -->
      {{else if eq (toLower .BillingStatus) "success"}}
          <img src="https://storage.googleapis.com/uniq-187911.appspot.com/public/assets/paid-stamp.png" class="paid-stamp" alt="Paid Stamp"/>
      {{end}}               

    <DIV class="dclr"></DIV>
    <P class="p0 ft0">{{uppercase .BillingStatus }} PAYMENT</P>
    <P class="p1 ft2">INVOICE: <SPAN class="ft1">#<b>{{ .Invoice }}</b></SPAN></P>
    <P class="p2 ft1">Kepada {{.Admin.Name}}, terima kasih telah melakukan order.</P>
    <P class="p14 ft13">Deskripsi</P>
    <TABLE cellpadding=0 cellspacing=0 class="t1">
      <TR>
        <TD class="tr7 td40">
          <P class="p3 ft14">Nama</P>
        </TD>
        <TD class="tr7 td41">
          <P class="p16 ft14">: {{.Admin.Name}}</P>
        </TD>
      </TR>
      <TR>
        <TD class="tr8 td40">
          <P class="p3 ft8">Business Name</P>
        </TD>
        <TD class="tr8 td41">
          <P class="p16 ft8">: {{.Admin.BusinessName}}</P>
        </TD>
      </TR>
      <TR>
        <TD class="tr8 td40">
          <P class="p3 ft8">Invoice</P>
        </TD>
        <TD class="tr8 td41">
          <P class="p16 ft8">: {{ .Invoice }}</P>
        </TD>
      </TR>
      <TR>
        <TD class="tr8 td40">
          <P class="p3 ft8">Kode Unik</P>
        </TD>
        <TD class="tr8 td41">
          <P class="p16 ft8">: {{ .UniqueCode }}</P>
        </TD>
      </TR>
      <TR>
        <TD class="tr8 td40">
          <P class="p3 ft8">Diskon</P>
        </TD>
        <TD class="tr8 td41">
          <P class="p16 ft8">:
            {{currency .Discount}}
          </P>
        </TD>
      </TR>
      <TR>
        <TD class="tr8 td40">
          <P class="p3 ft8">Biaya yang harus ditransfer</P>
        </TD>
        <TD class="tr8 td41">
          <P class="p16 ft8">:
            {{currency .DetailTotal}}
          </P>
        </TD>
      </TR>
      <TR>
        <TD class="tr8 td40">
          {{if eq .BillingStatus "pending"}}
            <P class="p3 ft8">Lakukan pembayaran sebelum</P>
          {{else if eq .BillingStatus "success"}}
            <P class="p3 ft8"><b>Dibayarkan Pada</b></P>
          {{end}}
        </TD>
        <TD class="tr8 td41">
          <P class="p16 ft8"><b>:
            <script>          
            {{if eq .BillingStatus "pending"}}
              document.write(moment({{.TimeOrderExpired}}).format('DD/MM/YYYY HH:mm:ss'));
            {{else if eq .BillingStatus "success"}}
              document.write(moment({{.TimeConfirm}}).format('DD/MM/YYYY HH:mm:ss'));
            {{end}}
            </script>
            </b>
          </P>
        </TD>
      </TR>
    </TABLE>
    {{if .PaymentCode}}
    <p class="p2 ft1" style="text-align: center;">
      <b style="font-size: 18pt">Payment Code: {{.PaymentCode}}</b>
    </p>
    {{end}}

    <p class="p2 ft1">Berikut ini adalah rincian order yang telah Anda lakukan:</p>
    
    <TABLE cellpadding=0 cellspacing=0 class="t0 table-services">
      <tr>
        <th colspan="2" style="padding:5px">NO</th>
        <th colspan="4">DESKRIPSI</th>
        <th colspan="2">JUMLAH</th>
        <th colspan="3">HARGA</th>
        <th colspan="4">TOTAL</th>
      </tr>   

    {{range $index, $row := .Detail}} 
		<TR>
			<TD colspan=2 style=padding:5px><P class='p10 ft8'>{{add $index 1}}</P></TD>
			<TD colspan=4><P class='p10 ft8'>{{.SystemService.Name}} ({{.ServicePeriod}} {{periodConversion .ServiceLengthDay}})</P></TD>
			<TD colspan=2><P class='p9 ft8'>{{.Qty}}</P></TD>
			<TD colspan=3><P class='p10 ft8'>
			{{currency .Price}}
			</P></TD>
			<TD colspan=4><P class='p11 ft8'>
			{{currency (multiply .ServicePeriod (multiply .Price .Qty))}}
			</P></TD>
		</TR>

    {{end}}

    </TABLE>

    <table class="t0">
      <tr style="padding-buttom : 20px;">
        <td colspan="14" style="align: center !important;color: #404040;">Kode Unik</td>
        <td colspan="1" style="align: center !important;color: #404040; text-align: right;">
          {{ .UniqueCode }}
        </td>
      </tr>
      <tr>
        <td colspan="14" style="align: center !important;color: #404040;">TOTAL YANG HARUS
          DITRANSFER</td>
        <td colspan="1" style="align: center !important;color: #404040; text-align: right;">
          {{currency .DetailTotal}}
        </td>
      </tr>
    </table>


    <div style="font-size:16pt;">
      <P class="p17 ft15">PENTING!</P>
      <!-- <P class="p18 ft8">
				<SPAN class="ft8">-</SPAN>
				<SPAN class="ft16">Invoice ini berlaku sampai tanggal
					<?= date('d M Y', ($result->data->billing->time_order_expired / 1000)) ?>.</SPAN>
			</P>
			-->
      <P class="p19 ft8">
        <SPAN class="ft8">-</SPAN>
        <SPAN class="ft16">Tulis invoice id #<b>{{ .Invoice }}</b> sebagai berita transfer
          (jika ada) ketika Anda melakukan transfer untuk mempermudah pengecekan.
        </SPAN>
      </P>
      <P class="p19 ft8">
        <SPAN class="ft8">-</SPAN>
        <SPAN class="ft16">Pembayaran harus tepat sampai 3 digit terakhir. perbedaan nominal akan menghambat
          proses verifikasi.
        </SPAN>
      </P>
    </div>

    <!--  <P class="p20 ft10">KONFIRMASI PEMBAYARAN</P>
		<P class="p21 ft1">Setelah transfer, silakan konfirmasi pembayaran Anda dengan mengirim bukti transfer ke
			melalui WhatsApp ke nomor kami di +62 <NOBR>817-1717-2171.</NOBR>
		</P>-->
    <P class="p22 ft18">*Invoice ini dibuat pada tanggal {{dateNow}}</P>
    <P class="p23 ft19">UNIQ – Solusi Terbaik Bisnis Anda</P>
    <P class="p24 ft22"><SPAN class="ft20">Kontak Email: </SPAN><A href="mailto: <EMAIL>"><SPAN
          class="ft21"><EMAIL></SPAN></A></P>
    <P class="p25 ft20">WhatsApp: +62 <NOBR>817-1717-2171</NOBR>
    </P>
    <P class="p25 ft22"><SPAN class="ft20">Website: </SPAN><A href="http://www.uniq.id/"><SPAN
          class="ft21">www.uniq.id</SPAN></A></P>
  </DIV>
</BODY>

</HTML>