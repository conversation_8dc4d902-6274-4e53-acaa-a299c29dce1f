package google

import (
	"context"
	"fmt"
	"runtime"
	"strings"

	"firebase.google.com/go/messaging"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
)

type NotificationData struct {
	Data    map[string]string
	Token   string
	Title   string
	Message string
}

func SendNotification(data NotificationData) error {
	return SendPushNotif(data, false)
}

// isCRM: true for CRM, false for POS. Used for initialize firebase client
func SendPushNotif(data NotificationData, isCrm bool) error {
	if data.Token == "" {
		log.Info("SendPushNotif: token is empty")
		return nil
	}
	ctx := context.Background()
	var client *messaging.Client
	var err error

	if isCrm {
		client, err = GetFirebaseClientCrm().Messaging(ctx)
	} else {
		client, err = GetFirebaseClient().Messaging(ctx)
	}

	if err != nil {
		fmt.Println("Error init firebase client: ", err)
	}

	notifMessage := messaging.Message{
		Data:  data.Data,
		Token: data.Token,
	}

	if data.Title != "" && data.Message != "" {
		notifMessage.Notification = &messaging.Notification{
			Title: data.Title,
			Body:  data.Message,
		}
	} else if len(data.Data) == 0 {
		fmt.Println("sending notification might be failed, data is empty and title or message is not set")
	}

	msg, err := client.Send(ctx, &notifMessage)

	target := "POS"
	if isCrm {
		target = "CRM"
	}
	if err != nil {
		pc, file, line, _ := runtime.Caller(1)
		fn := runtime.FuncForPC(pc).Name()

		fmt.Printf("%s:%d %s: send notification to %s failed, err: %v | msg: %v\n", file, line, fn, target, err, msg)
		fmt.Println("Notif Message: ", cast.ToJson(notifMessage))
		//types of error:
		/// http error status: 404; reason: app instance has been unregistered; code: registration-token-not-registered; details: Requested entity was not found.
		if strings.Contains(err.Error(), "app instance has been unregistered") {
			return nil
		}
	} else {
		fmt.Printf("send notification to %s success, msg: %s\n", target, msg)
	}
	return err
}
