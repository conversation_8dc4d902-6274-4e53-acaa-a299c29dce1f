package google

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"strings"

	"cloud.google.com/go/profiler"
	"cloud.google.com/go/pubsub"
	"cloud.google.com/go/storage"
	firebase "firebase.google.com/go"
	"firebase.google.com/go/db"
	"github.com/joho/godotenv"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"golang.org/x/oauth2"
	"google.golang.org/api/firebaseremoteconfig/v1"
	"google.golang.org/api/option"
)

var storageClient *storage.Client
var pubsubClient *pubsub.Client

var firebaseClient *firebase.App
var firebaseClientCrm *firebase.App
var firebaseDb *db.Client
var firebaseRemoteConf *firebaseremoteconfig.Service
var firebaseToken *oauth2.Token

func init() {
	if args := os.Args; len(args) > 1 {
		fmt.Println("args---", args[1])
		if strings.HasPrefix(args[1], "-test") {
			fmt.Println("db init skipped on testing.....")
			return
		}
	}

	errl := godotenv.Load()
	if errl != nil {
		godotenv.Load("/Users/<USER>/Documents/WORK/api-pos/.env")
	}

	opt := option.WithCredentialsFile(utils.MustHaveFile("config/credentials/google_credential.json"))
	ctx := context.Background()

	var err error
	storageClient, err = storage.NewClient(ctx, opt)
	if err != nil && os.Getenv("ENV") != "localhost" {
		log.Fatalf("init storage client error - %v", err)
	}

	projectId := os.Getenv("PROJECT_ID")
	pubsubCredPath := utils.MustHaveFile("config/credentials/pubsub_credential.json")
	serviceAccount, err := ReadServiceAccount(pubsubCredPath)
	if err == nil {
		projectId = serviceAccount.ProjectID
	} else {
		log.Printf("failed to read pubsub credential - %v\n", err)
	}

	optCred := option.WithCredentialsFile(pubsubCredPath)
	pubsubClient, err = pubsub.NewClient(ctx, projectId, optCred)
	if err != nil {
		fmt.Println("pubsub.NewClient: ", err)
	}

	config := &firebase.Config{
		DatabaseURL: "https://uniq-pos.firebaseio.com",
	}
	optCred = option.WithCredentialsFile("config/credentials/uniq-pos-firebase-adminsdk.json")
	firebaseClient, err = firebase.NewApp(ctx, config, optCred)
	if err == nil {
		firebaseDb, err = firebaseClient.Database(ctx)
		if err != nil {
			fmt.Println("init firebase database error - ", err)
		}
	} else {
		fmt.Println("init firebase client error - ", err)
	}

	//init firebase client for CRM
	config = &firebase.Config{
		DatabaseURL: "https://uniq-crm.firebaseio.com",
	}
	optCred = option.WithCredentialsFile("config/credentials/uniq-crm-firebase-adminsdk.json")
	firebaseClientCrm, err = firebase.NewApp(ctx, config, optCred)
	if err != nil {
		fmt.Println("init firebase client for CRM error - ", err)
	}

	firebaseToken, err = readServiceAccount("config/credentials/uniq-pos-firebase-adminsdk.json")
	if err != nil {
		fmt.Println(">>> Error acquiring token:", err)
	} else {
		writeEtag(getRemoteConfig(firebaseToken))
		// cfg, _ := GetFirebaseRemoteConfig()
		// fmt.Println(cfg.Get("api_receipt_sender"))
	}
}

func Profiler() profiler.Config {
	serviceAccountPath := "config/credentials/gcloud_service_account.json"
	os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", serviceAccountPath)
	serviceAccount, err := ReadServiceAccount(serviceAccountPath)
	if err != nil {
		fmt.Println("read service account for profile err: ", err)
	}

	cfg := profiler.Config{
		Service:        strings.TrimSpace(fmt.Sprintf("api-pos-%v", os.Getenv("ENV"))),
		ServiceVersion: "1.0.0",
		ProjectID:      serviceAccount.ProjectID,
	}
	return cfg
}

// read service account (json format) from file
func ReadServiceAccount(keyPath string) (models.ServiceAccount, error) {
	file, err := os.Open(keyPath)
	if err != nil {
		return models.ServiceAccount{}, err
	}
	defer file.Close()
	result, err := io.ReadAll(file)
	if err == nil {
		var serviceAccount models.ServiceAccount
		err = json.Unmarshal(result, &serviceAccount)
		return serviceAccount, err
	}
	return models.ServiceAccount{}, err

}

func GetStorageClient() *storage.Client {
	return storageClient
}

func GetPubSubClient() *pubsub.Client {
	return pubsubClient
}

func GetFirebaseClient() *firebase.App {
	return firebaseClient
}

func GetFirebaseClientCrm() *firebase.App {
	return firebaseClientCrm
}

func GetFirebaseDb() *db.Client {
	return firebaseDb
}

func GetFirebaseToken() *oauth2.Token {
	return firebaseToken
}
