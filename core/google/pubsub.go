package google

import (
	"context"
	"fmt"
	"sync"

	"cloud.google.com/go/pubsub"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
)

func PublishMessage(data interface{}, topicId string) error {
	ctx := context.Background()
	client := GetPubSubClient()

	if client == nil {
		return fmt.Errorf("client has not been initialized")
	}

	t := client.Topic(topicId)
	result := t.Publish(ctx, &pubsub.Message{
		Data: []byte(utils.SimplyToJson(data)),
	})

	id, err := result.Get(ctx)
	if log.IfError(err) {
		fmt.Printf("failed to publish to topic '%s' : %v", topicId, err)
	}

	fmt.Printf("pubsub published to topic: '%s', msg ID: %v\n", topicId, id)
	return err
}

func CreatePubSubTopic(topicId string) error {
	ctx := context.Background()
	client := GetPubSubClient()

	// Creates the new topic.
	topic, err := client.CreateTopic(ctx, topicId)
	if err != nil {
		return err
	}

	fmt.Printf("Topic %v created.\n", topic)
	return nil
}

func CreatePubSubSubcription(topicId, subscriptionName string) error {
	ctx := context.Background()
	client := GetPubSubClient()

	t := client.Topic(topicId)
	_, err := client.CreateSubscription(ctx, subscriptionName, pubsub.SubscriptionConfig{Topic: t})
	return err
}

func Subscribe(subsId string, action func(data []byte) bool) error {
	client := pubsubClient
	if client == nil {
		fmt.Println("pubsub client has not been initialized")
		return fmt.Errorf("pubsub client has not been initialized")
	}

	var mu sync.Mutex
	ctx := context.Background()
	sub := client.Subscription(subsId)
	cctx, _ := context.WithCancel(ctx)
	err := sub.Receive(cctx, func(ctx context.Context, msg *pubsub.Message) {
		mu.Lock()
		defer mu.Unlock()
		fmt.Printf("pubsub receive message, subs id: %s | id: %s | %s \n", subsId, msg.ID, string(msg.Data))
		if action(msg.Data) {
			fmt.Printf("pubsub ack? : %v | id: %s \n", true, msg.ID)
			msg.Ack()
		}
	})

	if !log.IfError(err) {
		fmt.Println("pubsub subscribed to id: ", subsId)
	} else {
		fmt.Printf("pubsub subscribed to id '%s' error: %v \n", subsId, err)
	}

	return err
}
