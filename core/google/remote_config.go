package google

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/models"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"golang.org/x/oauth2/jwt"
)

const PROJECT_ID = "uniq-pos"
const BASE_URL = "https://firebaseremoteconfig.googleapis.com"
const REMOTE_CONFIG_ENDPOINT = "v1/projects/" + PROJECT_ID + "/remoteConfig"
const REMOTE_CONFIG_URL = BASE_URL + "/" + REMOTE_CONFIG_ENDPOINT

var client = &http.Client{}
var configExpiredAt = time.Now().Unix()

func GetFirebaseRemoteConfig() (models.RemoteConfig, error) {
	var result models.RemoteConfig
	if firebaseToken == nil {
		return result, fmt.Errorf("firebase token not initialized...")
	}

	c, err := os.ReadFile("config.json")
	if err != nil {
		fmt.Println("readfile err: ", err)
		return result, err
	}
	err = json.Unmarshal(c, &result)
	if err != nil {
		fmt.Println("json unmarshal: ", err)
		return result, err
	}

	//if expired get new one
	if configExpiredAt <= time.Now().Unix() {
		go writeEtag(getRemoteConfig(firebaseToken))
	}

	return result, nil
}

func readServiceAccount(credentialFile string) (*oauth2.Token, error) {
	b, err := os.ReadFile(credentialFile)
	if err != nil {
		return nil, err
	}
	var c = struct {
		Email      string `json:"client_email"`
		PrivateKey string `json:"private_key"`
	}{}
	json.Unmarshal(b, &c)
	config := &jwt.Config{
		Email:      c.Email,
		PrivateKey: []byte(c.PrivateKey),
		Scopes: []string{
			"https://www.googleapis.com/auth/firebase.remoteconfig",
		},
		TokenURL: google.JWTTokenURL,
	}
	token, err := config.TokenSource(context.Background()).Token()
	if err != nil {
		return nil, err
	}
	return token, nil
}

func publish(token *oauth2.Token, Etag string) string {

	// read config.json
	readFile, err := os.ReadFile("config.json")
	if err != nil {
		fmt.Println(err)
	}

	request, err := http.NewRequest("PUT", REMOTE_CONFIG_URL, bytes.NewReader(readFile))
	if err != nil {
		log.Fatal("Error : %v\n", err)
	}

	request.Header.Set("Authorization", "Bearer "+token.AccessToken)
	request.Header.Add("Content-Type", "application/json; UTF-8")
	request.Header.Add("If-Match", Etag)

	response, err := client.Do(request)
	if err != nil {
		log.Fatalf("Error: %v\n", err)
	}

	// if resp.Status is 200
	if response.StatusCode == http.StatusOK {

		// Read response body
		bodyBytes, err := io.ReadAll(response.Body)
		if err != nil {
			log.Fatal(err)
		}

		// Convert Response Body to String
		bodyString := string(bodyBytes)

		// Print it out
		fmt.Println(bodyString)

		// Get ETag
		fmt.Printf("Found ETag: %+v\n", response.Header["Etag"][0])

		return string(response.Header["Etag"][0])
	}
	return ""
}

func getRemoteConfig(token *oauth2.Token) string {
	req, err := http.NewRequest("GET", REMOTE_CONFIG_URL, nil)
	if err != nil {
		log.Fatalf("Error: %v\n", err)
	}

	// Set Authorization Header
	req.Header.Set("Authorization", "Bearer "+token.AccessToken)
	resp, err := client.Do(req)
	if err != nil {
		log.Fatalf("Error: %v\n", err)
	}

	// if resp.Status is 200
	if resp.StatusCode == http.StatusOK {

		// Read response body
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Fatal(err)
		}
		// Convert Response Body to String
		// bodyString := string(bodyBytes)
		// Print it out
		// fmt.Println(bodyString)
		// Get ETag
		fmt.Printf("Found ETag: %+v\n", resp.Header["Etag"][0])

		// Write the response body into config.json
		_ = os.WriteFile("config.json", bodyBytes, 0644)
		configExpiredAt = time.Now().Add(30 * time.Minute).Unix()
		fmt.Println("firebase remote config expired at: ", configExpiredAt)

		return string(resp.Header["Etag"][0])
	}
	return ""
}

func writeEtag(etag string) {
	f, err := os.Create("etag.txt")
	if err != nil {
		fmt.Println(err)
		return
	}

	f.WriteString(etag)
	f.Close()
}
