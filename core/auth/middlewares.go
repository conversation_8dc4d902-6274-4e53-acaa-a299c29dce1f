package auth

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/dgrijalva/jwt-go/request"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
)

func ValidateToken(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return func(ctx *fasthttp.RequestCtx) {
		timeStart := time.Now()
		//fmt.Printf("%s -- %s \n", ctx.Method(), ctx.URI().Path())

		auth := InitJWTAuth()
		req := new(http.Request)
		req.Header = http.Header{}
		req.Header.Set("Authorization", string(ctx.Request.Header.Peek("Authorization")))
		token, err := request.ParseFromRequest(req, request.OAuth2Extractor, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			} else {
				return auth.PublicKey, nil
			}
		})

		if err == nil && token.Valid {
			claims := token.Claims.(jwt.MapClaims)
			authorization := claims["authorization"]
			outlet := claims["access_allowed"]
			if outlet == nil {
				outlet = claims["outlet"]
			}
			adminIdEnc := claims["sub"]
			if authorization != nil && outlet != nil && adminIdEnc != nil {
				adminId := adminIdEnc.(string)
				if len(adminId) > 3 {
					adminId, err = utils.DecryptText(adminIdEnc.(string))
					if log.IfError(err) {
						log.Warn("decrypt adminId error. adminId : %s", adminId)
						ctx.SetStatusCode(fasthttp.StatusUnauthorized)
						return
					}
				}

				if authorization.(string) == os.Getenv("token") {
					for k, v := range claims {
						ctx.Request.Header.Set(k, utils.ToString(v))
						// fmt.Println("header ---> k: ", k, "v: ", v)
					}

					//validate allowed access url (if set in token claim)
					accessLimitUrls := claims["access_limit_urls"]
					if accessLimitUrls != nil {
						accessLimitUrls := accessLimitUrls.([]interface{})
						hasAccess := validateAccessLimitUrls(accessLimitUrls, string(ctx.URI().Path()))

						if !hasAccess {
							fmt.Printf("user %s has no access to this url: %s, access limit urls: %v", adminId, string(ctx.URI().Path()), accessLimitUrls)
							ctx.SetStatusCode(fasthttp.StatusForbidden)
							return
						}
					}

					ctx.Request.Header.Set("outlet", outlet.(string))
					ctx.Request.Header.Set("admin_id", adminId)
					ctx.Request.Header.Set("role", claims["role"].(string))
					ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
					ctx.Response.Header.Set("X-Frame-Options", "DENY")
					// ctx.Request.Header.Set("insurer", claims["insurer"].(string))
					if insurer, ok := claims["insurer"]; ok {
						ctx.Request.Header.Set("insurer", insurer.(string))
					}

					ctx.SetContentType("application/json")
					//date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
					//fmt.Println("[request]  ", date, "  ", string(ctx.Method()), "  ", string(ctx.URI().Path()))

					//go utils.LogRequest(map[string]interface{}{
					//	"ip" : fmt.Sprintf("remote : %s | local : %s", ctx.RemoteIP().String(), ctx.LocalIP().String()),
					//	"token" : authorization,
					//	"env" : os.Getenv("server"),
					//	"device" : "-",
					//	"method" : string(ctx.Method()),
					//	"end_point" :  string(ctx.URI().Path()),
					//})

					//if ctx.Request.Header.Peek("Device") != nil {
					//	go func() {
					//		_, err := db.Update("devices",
					//			map[string]interface{}{"last_sync": time.Now().Unix() * 1000},
					//			map[string]interface{}{"imei": string(ctx.Request.Header.Peek("Device"))})
					//		utils.CheckErr(err)
					//	}()
					//}

					next(ctx)
					//fmt.Println(ctx.Response.String())
				} else {
					log.Warn("Validate token error. authorization bearer is not valid")
					ctx.SetStatusCode(fasthttp.StatusUnauthorized)
				}
			} else {
				log.Warn("Validate token error. Request : Authorization : %v | outlet : %v | adminIdEnc : %v", authorization, outlet, adminIdEnc)
				ctx.SetStatusCode(fasthttp.StatusUnauthorized)
			}
		} else {
			log.Warn("invalid auhtorization")
			ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		}

		bodySize := len(ctx.Response.Body())
		bodyUnit := "B"
		if bodySize/1000 < 1000 {
			bodyUnit = "KB"
			bodySize = bodySize / 1000
		} else if bodySize/1000 >= 1000 {
			bodyUnit = "MB"
			bodySize = bodySize / 1000000
		}

		if ctx.Response.StatusCode() != 200 {
			fmt.Printf("%d | %v | %s | %d %s | %s \n", ctx.Response.StatusCode(), time.Since(timeStart), string(ctx.Method()), bodySize, bodyUnit, string(ctx.URI().Path()))
		}
	}
}

func validateAccessLimitUrls(accessLimitUrls []interface{}, currentUrl string) bool {
	hasAccess := false
	for _, url := range accessLimitUrls {
		//check if currentUrl is match with url (contains)
		if strings.Contains(currentUrl, url.(string)) {
			hasAccess = true
			break
		}
	}
	return hasAccess
}

func ValidateRefreshToken(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return fasthttp.RequestHandler(func(ctx *fasthttp.RequestCtx) {
		authBearer := ctx.Request.Header.Peek("Authorization")
		userToken := string(ctx.FormValue("refresh_token"))
		if string(authBearer) != os.Getenv("token") {
			log.Error("some one try to request new token with invalid auth bearer")
			fmt.Println("authBearer: ", string(authBearer))
			ctx.SetStatusCode(fasthttp.StatusUnauthorized)
			return
		}
		auth := InitJWTAuth()
		req := new(http.Request)
		req.Header = http.Header{}
		req.Header.Set("Authorization", userToken)
		token, err := request.ParseFromRequest(req, request.OAuth2Extractor, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			} else {
				return auth.PublicKey, nil
			}
		})

		if err == nil && token.Valid {
			claims := token.Claims.(jwt.MapClaims)
			tokenId := claims["sub"]
			insurer := claims["insurer"]
			role := claims["role"]

			if isRefreshTokenExist(tokenId.(string), userToken) {
				if tokenId != nil && role != nil {
					userId := utils.Decrypt(tokenId.(string))
					if userId == "" {
						log.IfError(fmt.Errorf("refresh token invalid, userId is: %v, tokenId: %v", userId, tokenId))
						ctx.SetStatusCode(fasthttp.StatusUnauthorized)
						return
					}

					for k, v := range claims {
						ctx.Request.Header.Set(k, utils.ToString(v))
					}

					if accessLimitUrls, ok := claims["access_limit_urls"]; ok && accessLimitUrls != nil {
						accessLimitUrls := accessLimitUrls.([]interface{})
						accessLimitUrlsStr := make([]string, 0)
						for _, url := range accessLimitUrls {
							accessLimitUrlsStr = append(accessLimitUrlsStr, utils.ToString(url))
						}
						ctx.Request.Header.Set("access_limit_urls", strings.Join(accessLimitUrlsStr, ","))
					}
					insurerId := ""
					if utils.ToString(role) != utils.ROLE_OWNER {
						insurerId = utils.Decrypt(utils.ToString(insurer))
					}
					ctx.Request.Header.Set("token_id", tokenId.(string))
					ctx.Request.Header.Set("user_id", userId)
					ctx.Request.Header.Set("insurer_id", insurerId)
					ctx.Request.Header.Set("role", role.(string))
					ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
					ctx.Response.Header.Set("X-Frame-Options", "DENY")
					ctx.SetContentType("application/json")
					next(ctx)
				} else {
					log.Error("Login fail, some of variable is null. tokenId : %v | role : %v ", tokenId, role)
					ctx.SetStatusCode(fasthttp.StatusUnauthorized)
					updateDeviceStatus(ctx.Request.Header.Peek("Device"))
				}
			} else {
				log.Error("[AUTH FAILED] token is no longer valid, something must be happen")
				log.Warn("id token : %s | user token : %s", tokenId, userToken)
				ctx.SetStatusCode(fasthttp.StatusUnauthorized)

				//update device status
				updateDeviceStatus(ctx.Request.Header.Peek("Device"))
			}
		} else {
			log.Info("user request with invalid token refresh, token : %s", string(ctx.FormValue("refresh_token")))
			ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		}
	})
}

func ValidateKey(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return fasthttp.RequestHandler(func(ctx *fasthttp.RequestCtx) {
		authBearer := ctx.Request.Header.Peek("Authorization")
		if string(authBearer) != os.Getenv("token") {
			log.Info("invalid key: %s", authBearer)
			ctx.SetStatusCode(fasthttp.StatusUnauthorized)
			return
		}

		ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
		ctx.Response.Header.Set("X-Frame-Options", "DENY")
		ctx.SetContentType("application/json")
		next(ctx)
	})
}

func updateDeviceStatus(imei interface{}) {
	if imei != nil {
		_, err := db.Update("devices", map[string]interface{}{
			"device_status": "off",
		}, "imei = ?", imei)
		log.IfError(err)
	} else {
		log.Warn("imei is empty")
	}
}

// check if refresh token is match with our data in db
func isRefreshTokenExist(tokenId, userToken string) bool {
	data, err := db.Query("select token, expired_at from users_session where id = ? and expired_at > UNIX_TIMESTAMP()", tokenId)
	log.IfError(err)

	log.Debug("token from db : %s", data["token"])
	isTokenMatch := utils.CheckPasswordHash(tokenId, utils.ToString(data["token"]))
	if os.Getenv("server") == "development" {
		return isTokenMatch
	}

	if !isTokenMatch {
		log.IfError(fmt.Errorf("refreshToken Not Found! token id: %s, token from db: %s, user token: %s", tokenId, data["token"], userToken))
	}

	return true
	//if os.Getenv("server") == "production" {
	//	return true
	//}else{
	//	localDb := db.GetDbJson()
	//	refreshToken := new(models.RefreshToken)
	//	err := localDb.Read("refresh_token", tokenId, &refreshToken)
	//	log.IfError(err)
	//
	//	return utils.CheckPasswordHash(userToken, refreshToken.RefreshToken)
	//}
}

func ValidateOutlet(ctx *fasthttp.RequestCtx) bool {
	outletId := ctx.UserValue("outletId")
	outlet := ctx.Request.Header.Peek("outlet")
	if found := Any(strings.Split(string(outlet), ","), outletId.(string)); !found {
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return false
	}

	return true
}

func ValidateOutletId(ctx *fasthttp.RequestCtx, outletId int) bool {
	outlet := ctx.Request.Header.Peek("outlet")

	if found := Any(strings.Split(string(outlet), ","), strconv.Itoa(outletId)); !found {
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return false
	}
	return true
}

func ValidateOutletIds(outletIds []int, outletId int) bool {
	for _, id := range outletIds {
		if id == outletId {
			return true
		}
	}
	return false
}

//func ValidateOutlet(next fasthttp.RequestHandler) fasthttp.RequestHandler {
//	return fasthttp.RequestHandler(func(ctx *fasthttp.RequestCtx){
//		outletId := ctx.UserValue("outletId")
//		outlet := ctx.Response.Header.Peek("outlet")
//
//		if found := Any(strings.Split(string(outlet), ","), outletId.(string)); !found {
//			ctx.SetStatusCode(fasthttp.StatusForbidden)
//		}else{
//			next(ctx)
//		}
//	})
//}

func Any(vs []string, t string) bool {
	for _, v := range vs {
		if v == t {
			return true
		}
	}
	return false
}

var (
	corsAllowHeaders     = "Authorization, Public-Key, Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, browsername, platform, user-agent, UserAgent, isPhysicalDevice, Version, Brand, Manufacture, Model"
	corsAllowMethods     = "HEAD,GET,POST,PUT,DELETE,OPTIONS"
	corsAllowOrigin      = "*"
	corsAllowCredentials = "true"
)

func CORS(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return func(ctx *fasthttp.RequestCtx) {
		// Only enable CORS in development environment
		if strings.ToLower(os.Getenv("ENV")) != "development" {
			next(ctx)
			return
		}

		// fmt.Println("cors: ", string(ctx.Request.URI().FullURI()))
		if string(ctx.Method()) == fasthttp.MethodOptions {
			ctx.Response.Header.Set("Access-Control-Allow-Credentials", corsAllowCredentials)
			ctx.Response.Header.Set("Access-Control-Allow-Headers", corsAllowHeaders)
			ctx.Response.Header.Set("Access-Control-Allow-Methods", corsAllowMethods)
			ctx.Response.Header.Set("Access-Control-Allow-Origin", corsAllowOrigin)
			ctx.SetStatusCode(fasthttp.StatusNoContent)
			return
		}

		ctx.Response.Header.Set("Access-Control-Allow-Credentials", corsAllowCredentials)
		ctx.Response.Header.Set("Access-Control-Allow-Origin", corsAllowOrigin)
		// fmt.Println("allowing cors...")
		// fmt.Println("CORS - headers resp : ", ctx.Response.Header.String(), "headers req : ", ctx.Request.Header.String())
		next(ctx)
	}
}
