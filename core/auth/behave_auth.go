package auth

import (
	"gitlab.com/uniqdev/backend/api-pos/models"
	"bytes"
	"encoding/base64"
	"fmt"
	"github.com/dgrijalva/jwt-go"
	"github.com/dgrijalva/jwt-go/request"
	"github.com/valyala/fasthttp"
	"net/http"
	"os"
	"time"
)


var basicAuthPrefix = []byte("Basic ")

func ValidateBehaveToken(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return fasthttp.RequestHandler(func(ctx *fasthttp.RequestCtx){
		auth := InitJWTAuth()
		req := new(http.Request)
		req.Header = http.Header{}
		req.Header.Set("Authorization", string(ctx.Request.Header.Peek("Authorization")))
		token, err := request.ParseFromRequest(req, request.OAuth2Extractor, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			} else {
				return auth.PublicKey, nil
			}
		})

		if err == nil && token.Valid {
			ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
			ctx.Response.Header.Set("X-Frame-Options", "DENY")
			ctx.SetContentType("application/json")
			next(ctx)
			return
		}

		ctx.Response.Header.Set("WWW-Authenticate", "Basic realm=Restricted")
		ctx.Error(fasthttp.StatusMessage(fasthttp.StatusUnauthorized), fasthttp.StatusUnauthorized)
	})
}

func ValidateBehaveBasicAuth(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return fasthttp.RequestHandler(func(ctx *fasthttp.RequestCtx){
		auth := ctx.Request.Header.Peek("Authorization")
		if bytes.HasPrefix(auth, basicAuthPrefix){
			payload, err := base64.StdEncoding.DecodeString(string(auth[len(basicAuthPrefix):]))
			if err == nil {
				user := []byte(os.Getenv("behave_user"))
				pass := []byte(os.Getenv("behave_password"))
				pair := bytes.SplitN(payload, []byte(":"), 2)
				if len(pair) == 2 &&
					bytes.Equal(pair[0], user) &&
					bytes.Equal(pair[1], pass){

					next(ctx)
					return
				}
			}
		}

		ctx.Response.Header.Set("WWW-Authenticate", "Basic realm=Restricted")
		ctx.Error(fasthttp.StatusMessage(fasthttp.StatusUnauthorized), fasthttp.StatusUnauthorized)
	})
}

func (auth *JWTAuthBackend) GenerateBehaveToken() *models.AuthToken {
	expired := time.Now().Add(time.Minute * 30).Unix()
	token := jwt.New(jwt.SigningMethodRS512)
	token.Claims = jwt.MapClaims{
		"exp": expired,
		"iat": time.Now().Unix(),
		"scope": "behave",
	}
	authToken := new(models.AuthToken)
	tokenString, err := token.SignedString(auth.privateKey)
	if err != nil{
		panic(err)
		return authToken
	}
	authToken.Token = tokenString
	authToken.Type = "Bearer"
	authToken.Expired = expired
	return authToken
}
