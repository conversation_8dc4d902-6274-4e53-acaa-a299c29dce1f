package auth

import (
	"bufio"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/dgrijalva/jwt-go"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

type JWTAuthBackend struct {
	privateKey *rsa.PrivateKey
	PublicKey  *rsa.PublicKey
}

type GenerateTokenOptions struct {
	Expired         time.Duration
	AccessAllowed   string
	InsurerId       string
	Role            string
	TokenType       string
	Id              string
	AccessLimitUrls []string
}

var jwtAuthInstance *JWTAuthBackend = nil

func InitJWTAuth() *JWTAuthBackend {
	if jwtAuthInstance == nil {
		checkOrCreateKeys()
		jwtAuthInstance = &JWTAuthBackend{
			privateKey: getPrivateKey("config/rsa/app.rsa.key"),
			PublicKey:  getPublicKey("config/rsa/app.rsa.pub.key"),
		}
	}
	return jwtAuthInstance
}

func (auth *JWTAuthBackend) GenerateTokenWithOptions(options GenerateTokenOptions) *models.AuthToken {
	//if expired is not set, use default 1 hour
	if options.Expired == 0 {
		options.Expired = time.Minute * 60
	}
	expired := time.Now().Add(options.Expired).Unix()
	token := jwt.New(jwt.SigningMethodRS512)
	token.Claims = jwt.MapClaims{
		"exp":               expired,
		"iat":               time.Now().Unix(),
		"sub":               options.Id,
		"access_allowed":    options.AccessAllowed,
		"context":           "pos-" + os.Getenv("server"),
		"authorization":     os.Getenv("token"),
		"role":              options.Role,
		"insurer":           options.InsurerId,
		"access_limit_urls": options.AccessLimitUrls,
	}

	authToken := new(models.AuthToken)
	tokenString, err := token.SignedString(auth.privateKey)
	if log.IfError(err) {
		return authToken
	}
	authToken.Token = tokenString
	authToken.Type = "Bearer"
	authToken.Expired = expired

	//create refresh token
	token = jwt.New(jwt.SigningMethodRS512)
	refreshTokenExpired := time.Now().Add(time.Hour * 24 * 30).Unix() //expired in 30 days

	token.Claims = jwt.MapClaims{
		"exp":     refreshTokenExpired,
		"iat":     time.Now().Unix(),
		"sub":     options.Id,
		"context": "pos-" + os.Getenv("server"),
		"insurer": options.InsurerId,
		"role":    options.Role,
		"type":    "refresh_token",
		"access_limit_urls": options.AccessLimitUrls,
	}

	refreshToken, err := token.SignedString(auth.privateKey)
	if log.IfError(err) {
		return authToken
	}
	authToken.RefreshToken = refreshToken

	go saveUserSession(options.Id, refreshToken, refreshTokenExpired)

	return authToken
}

func (auth *JWTAuthBackend) GenerateToken(id, insurerId, role, data string) *models.AuthToken {
	expired := time.Now().Add(time.Minute * 60).Unix() //expires in 1 hour
	token := jwt.New(jwt.SigningMethodRS512)
	token.Claims = jwt.MapClaims{
		"exp":            expired,
		"iat":            time.Now().Unix(),
		"sub":            id,
		"access_allowed": data,
		"context":        "pos-" + os.Getenv("server"),
		"authorization":  os.Getenv("token"),
		"role":           role,
		"insurer":        insurerId,
	}
	authToken := new(models.AuthToken)
	tokenString, err := token.SignedString(auth.privateKey)
	if log.IfError(err) {
		return authToken
	}
	authToken.Token = tokenString
	authToken.Type = "Bearer"
	authToken.Expired = expired

	//create refresh token
	token = jwt.New(jwt.SigningMethodRS512)
	refreshTokenExpired := time.Now().Add(time.Hour * 24 * 30).Unix() //expired in 30 days

	token.Claims = jwt.MapClaims{
		"exp":     refreshTokenExpired,
		"iat":     time.Now().Unix(),
		"sub":     id,
		"context": "pos-" + os.Getenv("server"),
		"insurer": insurerId,
		"role":    role,
		"type":    "refresh_token",
	}

	refreshToken, err := token.SignedString(auth.privateKey)
	if log.IfError(err) {
		return authToken
	}
	authToken.RefreshToken = refreshToken

	go saveUserSession(id, refreshToken, refreshTokenExpired)

	return authToken
}

/*
save refresh token in db, will be used for validate request new token
*/
func saveUserSession(id, refreshToken string, tokenExpired int64) {
	tokenHash, err := utils.HashPassword(id)
	if log.IfError(err) {
		log.Info("failed to hash: %v", refreshToken)
		return
	}

	//insert refresh token to db,
	//for new logged in user, the refresh token is not exist, so we insert it, otherwise we just have to update the data
	_, err = db.GetDb().Exec("insert into users_session (id, timestamp, token, expired_at) values (?,?,?,?) on duplicate key update timestamp=?, token=?, expired_at=?",
		id, tokenExpired, tokenHash, tokenExpired, tokenExpired, tokenHash, tokenExpired)
	log.IfError(err)

	log.Debug("token saved with id : %s", id)

	//tokenHash, err := utils.HashPassword(refreshToken)
	//log.IfError(err)
	//localDb := db.GetDbJson()
	//err = localDb.Write("refresh_token", id, models.RefreshToken{RefreshToken: tokenHash, Expired: refreshTokenExpired})
	//log.IfError(err)
}

func getPrivateKey(filePath string) *rsa.PrivateKey {
	privateKeyFile, err := os.Open(filePath)
	if err != nil {
		panic(err)
	}

	pemFileInfo, _ := privateKeyFile.Stat()
	var size = pemFileInfo.Size()
	pemBytes := make([]byte, size)

	buffer := bufio.NewReader(privateKeyFile)
	_, err = buffer.Read(pemBytes)

	data, _ := pem.Decode([]byte(pemBytes))

	privateKeyFile.Close()

	privateKeyImported, err := x509.ParsePKCS1PrivateKey(data.Bytes)

	if err != nil {
		panic(err)
	}

	return privateKeyImported
}

func getPublicKey(filePath string) *rsa.PublicKey {
	publicKeyFile, err := os.Open(filePath)
	if err != nil {
		panic(err)
	}

	pemFileInfo, _ := publicKeyFile.Stat()
	var size = pemFileInfo.Size()
	pemBytes := make([]byte, size)

	buffer := bufio.NewReader(publicKeyFile)
	_, err = buffer.Read(pemBytes)

	data, _ := pem.Decode([]byte(pemBytes))

	publicKeyFile.Close()

	publicKeyImported, err := x509.ParsePKIXPublicKey(data.Bytes)

	if err != nil {
		panic(err)
	}

	rsaPub, ok := publicKeyImported.(*rsa.PublicKey)

	if !ok {
		panic(err)
	}

	return rsaPub
}

func checkOrCreateKeys() {
	if os.Getenv("ENV") != "localhost" {
		return
	}
	privKeyPath := "config/rsa/app.rsa.key"
	pubKeyPath := "config/rsa/app.rsa.pub.key"

	// Check if the file exists
	if _, err := os.Stat(privKeyPath); os.IsNotExist(err) {
		fmt.Println("File does not exist:", privKeyPath)
	} else {
		fmt.Println("File exists:", privKeyPath)
		return
	}

	// Generate a new RSA key pair
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		fmt.Println("Error generating key:", err)
		return
	}

	// Encode the private key in PEM format
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	privateKeyBlock := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	}
	privateKeyFile, err := os.Create(privKeyPath)
	if err != nil {
		fmt.Println("Error creating private key file:", err)
		return
	}
	defer privateKeyFile.Close()
	if err := pem.Encode(privateKeyFile, privateKeyBlock); err != nil {
		fmt.Println("Error encoding private key:", err)
		return
	}

	// Encode the public key in PEM format
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	if err != nil {
		fmt.Println("Error marshalling public key:", err)
		return
	}
	publicKeyBlock := &pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyBytes,
	}
	publicKeyFile, err := os.Create(pubKeyPath)
	if err != nil {
		fmt.Println("Error creating public key file:", err)
		return
	}
	defer publicKeyFile.Close()
	if err := pem.Encode(publicKeyFile, publicKeyBlock); err != nil {
		fmt.Println("Error encoding public key:", err)
		return
	}

	fmt.Println("RSA key pair generated successfully!")
}
