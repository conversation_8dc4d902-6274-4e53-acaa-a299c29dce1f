package api

import (
	"encoding/json"
	"fmt"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
)

func GetJSONArray(sqlQuery string) ([]map[string]interface{}, error) {
	db := db.GetDb()
	tableData := make([]map[string]interface{}, 0)

	stmt, err := db.Prepare(sqlQuery)
	if err != nil{
		return tableData, err
	}

	rows, err := stmt.Query()
	if err != nil{
		return tableData, err
	}

	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil{
		return tableData, nil
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values{
		scanArgs[i] = &values[i]
	}

	for rows.Next(){
		err := rows.Scan(scanArgs...)
		if err != nil{
			return tableData, nil
		}

		entry := make(map[string]interface{})
		for i, col := range columns{
			v := values[i]

			b, ok := v.([]byte)
			if ok{
				entry[col] = string(b)
			}else{
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	return tableData, nil
}

func GetJSON(sqlQuery string) (map[string]interface{}, error) {
	db :=  db.GetDb()
	entryData := make(map[string]interface{}, 0)

	stmt, err := db.Prepare(sqlQuery)
	if err != nil{
		return entryData, err
	}

	rows, err := stmt.Query()
	if err != nil{
		return entryData, err
	}

	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil{
		return entryData, nil
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values{
		scanArgs[i] = &values[i]
	}

	if rows.Next(){
		err := rows.Scan(scanArgs...)
		if err != nil{
			return entryData, nil
		}

		for i, col := range columns{
			v := values[i]

			b, ok := v.([]byte)
			if ok{
				entryData[col] = string(b)
			}else{
				entryData[col] = v
			}
		}
		fmt.Println("Data Found")
	}else{
		fmt.Println("No Data Found!")
	}

	return entryData, nil
}

func GetJSONString(sqlQuery string, args... interface{}) (string, error) {
	db :=  db.GetDb()
	stmt, err := db.Prepare(sqlQuery)
	if err != nil{
		return "", err
	}

	rows, err := stmt.Query(args)
	if err != nil{
		return "", err
	}

	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil{
		return "", nil
	}

	tableData := make([]map[string]interface{}, 0)

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values{
		scanArgs[i] = &values[i]
	}

	for rows.Next(){
		err := rows.Scan(scanArgs...)
		if err != nil{
			return "", nil
		}

		entry := make(map[string]interface{})
		for i, col := range columns{
			v := values[i]

			b, ok := v.([]byte)
			if ok{
				entry[col] = string(b)
			}else{
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	jsonData, err := json.Marshal(tableData)
	if err != nil{
		return "", nil
	}

	return string(jsonData), nil
}

