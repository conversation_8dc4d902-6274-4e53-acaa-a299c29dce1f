package log

import (
	"context"
	"fmt"
	"os"
	"regexp"
	"runtime"
	"strings"
	"time"

	"cloud.google.com/go/logging"
	"github.com/joho/godotenv"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"google.golang.org/api/option"
)

var loggerClient *logging.Client

const (
	PREFIX_DEBUG = "\033[1;37m[DEBUG]\033[0m"
	//PREFIX_INFO  = "\033[1;32m[INFO]\033[0m"
	PREFIX_WARN  = "\033[1;33m[WARN]\033[0m"
	PREFIX_ERROR = "\033[1;31m[ERROR]\033[0m"

	LOG_APP_ID = "api-pos"
)

func init() {

	_ = godotenv.Load()
	ctx := context.Background()
	var err error
	optLogger := option.WithCredentialsFile("config/credentials/google_credential_logger.json")
	loggerClient, err = logging.NewClient(ctx, os.Getenv("PROJECT_ID"), optLogger)
	if err != nil {
		//log.Printf("init logger client error - %v", err)
	}
}

func LoggerLabel() map[string]string {
	return map[string]string{
		"environment": os.Getenv("server"),
		"project":     "api-pos",
	}
}

func WriteLog(log string, severity logging.Severity) {
	if loggerClient != nil {
		loggerClient.Logger(LOG_APP_ID).Log(logging.Entry{Payload: log, Severity: severity, Labels: LoggerLabel()})
	}
}

func Debug(msg string, v ...interface{}) {
	_, fn, line, _ := runtime.Caller(1)
	prefix := fmt.Sprintf(" %s:%d  >> ", utils.GetFileName(fn, true), line)
	if os.Getenv("server") == "localhost" {
		fmt.Printf(PREFIX_DEBUG+" "+getDate()+prefix+msg+"\n", v...)
	} else {
		fmt.Printf(prefix+msg+"\n", v...)
	}
	WriteLog(fmt.Sprintf(prefix+msg+"\n", v...), logging.Debug)
}

func Info(msg string, v ...interface{}) {
	stackSlice := make([]byte, 512)
	s := runtime.Stack(stackSlice, false)
	stacks := string(stackSlice[0:s])
	p := "[/a-zA-Z\\d\\-_/]+\\.go:\\d+\\s"
	rex, _ := regexp.Compile(p)

	p = "[a-z\\d]+\\.[a-zA-Z]+(\\(|\\s)"
	rex, _ = regexp.Compile(p)
	functions := rex.FindAllString(stacks, -1)

	funcName := ""
	if len(functions) > 0 {
		funcName = fmt.Sprintf("@%s", strings.Replace(functions[len(functions)-1], "(", "", -1))
	}

	funcName = strings.Replace(funcName, "\n", " ", -1)
	_, fn, line, _ := runtime.Caller(1)
	prefix := fmt.Sprintf("%s%s:%d  >> ", utils.GetFileName(fn, true), funcName, line)

	fmt.Printf(prefix+msg+"\n ", v...)
	//if os.Getenv("server") == "production" || os.Getenv("server") == "demo" {
	//	fmt.Printf(prefix+msg+"\n", v...)
	//} else {
	//	fmt.Printf(PREFIX_INFO+" "+getDate()+" "+prefix+msg+"\n", v...)
	//}
	//GetLogger().StandardLogger(logging.Info).Printf(prefix+msg+"\n", v...)
	WriteLog(fmt.Sprintf(prefix+msg+"\n", v...), logging.Info)
}

func Warn(msg string, v ...interface{}) {
	_, fn, line, _ := runtime.Caller(1)
	prefix := fmt.Sprintf("[WARN]  %s:%d  >> ", utils.GetFileName(fn, true), line)

	env := os.Getenv("server")
	if env == "localhost" {
		fmt.Printf(PREFIX_WARN+" "+getDate()+prefix+msg+"\n", v...)
	} else {
		fmt.Printf(prefix+msg+"\n", v...)
	}
	//GetLogger().StandardLogger(logging.Warning).Printf(prefix+msg+"\n", v...)
	WriteLog(fmt.Sprintf(prefix+msg+"\n", v...), logging.Warning)
}

func Error(msg string, v ...interface{}) {
	msg = fmt.Sprintf(msg, v...)
	_, fn, line, _ := runtime.Caller(1)
	prefix := fmt.Sprintf("  %s:%d  >> ", utils.GetFileName(fn, true), line)

	if os.Getenv("server") == "production" || os.Getenv("server") == "demo" {
		fmt.Println(prefix + msg)
	} else {
		fmt.Println(PREFIX_ERROR + " " + getDate() + prefix + msg)
	}

	//GetLogger().StandardLogger(logging.Error).Println(prefix + msg)
	WriteLog(prefix+msg, logging.Error)

	go func() {
		utils.SendMessageToSlack(fmt.Sprintf("[ERROR] %s  :: %s", prefix, msg))
	}()
}

func IfError(err error) (res bool) {
	if err != nil {
		//_, filePath, line, _ := runtime.Caller(1)
		//GetLogger().StandardLogger(logging.Error).Println(msg)
		// stackSlice := make([]byte, 512)
		// s := runtime.Stack(stackSlice, false)
		// stacks := string(stackSlice[0:s])
		// p := "[/a-zA-Z\\d\\-_/]+\\.go:\\d+\\s"
		// rex, _ := regexp.Compile(p)
		// callers := rex.FindAllString(stacks, -1)

		// p = "[a-z\\d]+\\.[a-zA-Z]+(\\(|\\s)"
		// rex, _ = regexp.Compile(p)
		// functions := rex.FindAllString(stacks, -1)

		// defer func() {
		// 	if r := recover(); r != nil {
		// 		fmt.Println("------ recover", r)
		// 		// fmt.Println("regex err, stacks ", stacks)
		// 		// fmt.Println("expression : ", p)
		// 		// fmt.Println("functions : ", functions)
		// 		// utils.SendMessageToSlack("[REGEX ERR] stacks : " + stacks)
		// 		res = true
		// 	}
		// }()

		if sdt != nil && len(sdt.hook) > 0 {
			for _, h := range sdt.hook {
				errHook := h.send(logOutput{
					Message: err.Error(),
					Stacks:  "",
					Level:   "Error",
					Err:     err,
					Env:     os.Getenv("ENV"),
				})
				if errHook != nil {
					fmt.Println("------ errHook", errHook)
				} else {
					fmt.Println("------ send to hook success", h)
					break
				}
			}
		} else {
			fmt.Println("------ no hook")
		}
		//  else {
		// 	if os.Getenv("server") != "localhosts" {
		// 		go func() {
		// 			utils.SendMessageToSlack(fmt.Sprintf("[ERROR] >> %s %s %v", callers[len(callers)-1], functions[len(functions)-1], err))
		// 		}()
		// 	}
		// }

		//msg := fmt.Sprintf("%s %s %s %s >> %v ", PREFIX_ERROR, getDate(), callers[len(callers)-1], functions[len(functions)-1], err)
		// msg := fmt.Sprintf("%s %s >> %v ", callers[len(callers)-1], functions[len(functions)-1], err)
		// fmt.Println(msg)
		// fmt.Println("<stack> ", regexp.MustCompile(`\r?\n`).ReplaceAllString(stacks, " "))
		// //fmt.Fprintln(os.Stderr, msg)

		// WriteLog(msg, logging.Error)

		// for i := len(callers) - 2; i > 0; i-- {
		// 	fmt.Println("       #", callers[i], strings.Replace(functions[i], "(", "", 1))
		// }

		return true
	}
	return false
}

func IfErrorSetStatus(ctx *fasthttp.RequestCtx, err error) bool {
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		msg := fmt.Sprintf("%s %s %s:%d  >> %v ", PREFIX_ERROR, getDate(), utils.GetFileName(fn, true), line, err)
		fmt.Println(msg)
		//GetLogger().StandardLogger(logging.Error).Println(msg)
		WriteLog(msg, logging.Error)

		go func() {
			utils.SendMessageToSlack(fmt.Sprintf("[ERROR] >> %s", strings.Replace(msg, PREFIX_ERROR, "", 1)))
		}()

		ctx.SetContentType("text/plain; charset=utf-8")
		ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return true
	}
	return false
}

func getDate() string {
	_, offset := time.Now().Zone()
	diff := int64(25200 - offset) //25200 is developer offset (WIB)
	return time.Unix(time.Now().Unix()+diff, 0).Format("02/01/2006 15:04:05")
}
