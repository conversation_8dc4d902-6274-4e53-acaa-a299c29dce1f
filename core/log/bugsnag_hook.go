package log

import (
	"fmt"
	"os"
	"time"

	"github.com/bugsnag/bugsnag-go/v2"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
)

type BugsnagHook struct {
	<PERSON>Key string
}

func (b BugsnagHook) initHook() {
	bugsnag.Configure(bugsnag.Configuration{
		APIKey:          b.<PERSON>ey,
		ReleaseStage:    os.Getenv("ENV"),
		ProjectPackages: []string{"main", "gitlab.com/uniqdev/backend/api-pos"},
		AppVersion:      cast.ToString(time.Now().UnixMilli()),
	})
}

func (b BugsnagHook) send(output logOutput) error {
	if b.APIKey == "" {
		fmt.Println("bugsnag api key is empty")
		return fmt.Errorf("bugsnag api key is empty")
	}

	err := bugsnag.Notify(output.Err)
	fmt.Println("------ BUGSNAG: ", err)
	if err != nil {
		fmt.Println("send error to bugsnag", err)
	}
	return err
}
