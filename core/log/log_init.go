package log

type logger struct {
	Config LoggerConfig
	hook   []hook
}

type LoggerConfig struct {
	DisableColor bool
	HideTime     bool
	TimeOffset   int64
}

type logOutput struct {
	Message string
	Stacks  string
	Level   string
	Err     error
	Env     string
}

type hook interface {
	initHook()
	send(logOutput) error
}

var (
	sdt = New()
	//prefixError = "\u001B[1;31m[ERROR]\u001B[0m"
)

func New() *logger {
	return &logger{
		hook: []hook{},
	}
}

//func SetLogger(config LoggerConfig) {
//	sdt.setLogConfig(config)
//}

func AddHook(h hook) {
	if sdt.hook == nil {
		sdt.hook = []hook{}
	}

	sdt.hook = append(sdt.hook, h)
	sdt.hook[len(sdt.hook)-1].initHook()
}

func (l *logger) setLogConfig(config LoggerConfig) {
	l.Config = config
}
