package log

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"net/http"
	"text/template"
)

type SlackHook struct {
	HookUrl string
	Channel string
}

var slackTmpl *template.Template

func (slack SlackHook) initHook() {
	var err error
	slackTmpl, err = template.ParseFiles("config/template/log/slack_template.tmpl")
	if err != nil {
		fmt.Println("------ slackTmpl err", err)
		return
	}
}

func (slack SlackHook) send(out logOutput) error {
	if slack.HookUrl == "" {
		return nil
	}

	var reqBody bytes.Buffer
	err := slackTmpl.Execute(&reqBody, out)
	if err != nil {
		return err
	}

	resp, err := http.Post(slack.HookUrl, "application/json", bytes.NewBuffer(reqBody.Bytes()))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("slack hook failed with status code %d", resp.StatusCode)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("read body err", err)
	}
	fmt.Println("slack:", resp.StatusCode, string(body))
	return nil
}
