package db

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/joho/godotenv"
	scribble "github.com/nanobox-io/golang-scribble"
	"github.com/rapidloop/skv"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var db *sql.DB
var mongoDb *mongo.Database
var mongoDbClient *mongo.Client

func init() {
	e := godotenv.Load()
	if e != nil {
		fmt.Println("Error loading .env file", e)
	}

	if args := os.Args; len(args) > 1 && os.Getenv("db_user") == "" {
		fmt.Println("args---", args[1])
		if strings.HasPrefix(args[1], "-test") {
			fmt.Println("db init skipped on testing.....")
			return
		}
	}

	InitDatabase()
}

func InitDatabase() {
	fmt.Println("------ init databse ------")
	fmt.Println("Environment -> '", os.Getenv("server"), "'")

	username := utils.MustGetEnv("db_user")
	password := utils.MustGetEnv("db_password")
	dbName := utils.MustGetEnv("db_name")
	dbHost := utils.MustGetEnv("db_host")
	dbPort := os.Getenv("db_port")

	fmt.Println("using database: ", dbName)

	if dbPort == "" {
		dbPort = "3306"
	}

	var err error
	source := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", username, password, dbHost, dbPort, dbName)
	if os.Getenv("db_communication") == "unix" {
		source = fmt.Sprintf("%s:%s@unix(/cloudsql/%s)/%s", username, password, dbHost, dbName)
		fmt.Println("using unix communication...")
	}

	db, err = sql.Open("mysql", source)
	if err != nil {
		log.Fatalf("Could not open Db: %v", err)
	}

	err = db.Ping()
	if err != nil {
		fmt.Println("ping db error....", err)
	}

	//db.SetMaxIdleConns(0)

	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(10)
	db.SetConnMaxLifetime(5 * time.Minute)

	//go sendUpdateToGroup()
	setupLocalDb()
	setupMongoDb()

	fmt.Println("#### database initialized ####")
}

func setupMongoDb() {
	// if os.Getenv("ENV") == "localhost" {
	// 	return
	// }

	uname := os.Getenv("mongo_db_user")
	pass := os.Getenv("mongo_db_password")
	dbName := os.Getenv("mongo_db_name")
	cluster := os.Getenv("mongo_db_host")

	if uname == "" || pass == "" {
		fmt.Println("mongo db init skip, no env provided")
		return
	}

	var err error
	connUri := fmt.Sprintf("mongodb+srv://%s:%s@%s/%s?retryWrites=true&w=majority", uname, pass, cluster, dbName)
	mongoDbClient, err = mongo.NewClient(options.Client().ApplyURI(connUri))
	if err != nil {
		panic(err)
	}
	ctx, _ := context.WithTimeout(context.Background(), 10*time.Second)
	err = mongoDbClient.Connect(ctx)
	if err != nil {
		panic(err)
	}

	mongoDb = mongoDbClient.Database(dbName)
}

var localDb *skv.KVStore

func setupLocalDb() {
	//if directory is not created, create it first
	baseDir := "temp"
	if _, err := os.Stat("temp"); os.IsNotExist(err) {
		err = os.Mkdir(baseDir, os.ModePerm)
		if err != nil {
			fmt.Println("creating tmp dir error - ", err)
		}
	}

	var err error
	localDb, err = skv.Open(baseDir + "/behave.db")
	if err != nil {
		fmt.Println("Open local db Error ", err)
	}
}

func GetLocalDb() *skv.KVStore {
	return localDb
}

func GetDb() *sql.DB {
	return db
}

func Mongo() *mongo.Database {
	return mongoDb
}

func MongoDbClient() *mongo.Client {
	return mongoDbClient
}

func GetDbJson() *scribble.Driver {
	dbJson, err := scribble.New("temp/db", nil)
	if err != nil {
		fmt.Println("Error", err)
		utils.SendMessageToSlack(fmt.Sprintf("[[ %s ]] - %s ", os.Getenv("server"), err.Error()))
	}
	return dbJson
}

//func sendUpdateToGroup() {
//	env := strings.TrimSpace(os.Getenv("server"))
//	if env == "development" || env == "localhost" {
//		return
//	}
//
//	lastCommit, err := utils.ReadFile(".lastcommit")
//	if utils.CheckErr(err) {
//		return
//	}
//
//	var commitCount string
//
//	//git rev-list --all --count
//	commitCount, err = utils.ExeCommand("git", "rev-list", "--all", "--count")
//
//	fmt.Println("Commit count : ", commitCount)
//	if lastCommit == commitCount {
//		return
//	}
//
//	releaseNote, err := utils.ReadFile("release_note.txt")
//	fmt.Println("Release Note : ", releaseNote)
//
//	//6287838362747-1495704774
//	err = utils.SendWhatsAppMessage("Server *"+strings.TrimSpace(os.Getenv("server"))+"* baru saja di update. Pesan UpdateDb : "+releaseNote, "group", "tech", "6285742257881-1495197570")
//	if err == nil {
//		err = ioutil.WriteFile(".lastcommit", []byte(commitCount), 0644)
//	}
//}
