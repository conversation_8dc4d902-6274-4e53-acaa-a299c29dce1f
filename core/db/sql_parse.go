package db

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"os"
	"runtime"
	"strings"
	"sync"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
)

// Repository struct
type Repository struct {
	Conn *sql.DB
	sql  string
	args []interface{}
}

func QueryArrayFun(sql string, params map[string]interface{}) ([]map[string]interface{}, error) {
	query, args := FormatArgs(sql, params)
	return QueryArray(query, args...)
}

func QueryFun(sql string, params map[string]interface{}) (map[string]interface{}, error) {
	query, args := FormatArgs(sql, params)
	return Query(query, args...)
}

func QueryArrayGo(wg *sync.WaitGroup, result chan []map[string]interface{}, sqlQuery string, args ...interface{}) {
	defer wg.Done()
	data, err := QueryArray(sqlQuery, args...)
	log.IfError(err)
	result <- data
}

func QueryChan(result chan map[string]interface{}, sqlQuery string, args ...interface{}) {
	data, err := Query(sqlQuery, args...)
	log.IfError(err)
	result <- data
}

//func QueryArrayChan(result chan []map[string]interface{}, sqlQuery string, args ...interface{}) {
//	data, err := QueryArray(sqlQuery, args...)
//	log.IfError(err)
//	result <- data
//}

func QueryArray(sqlQuery string, args ...interface{}) ([]map[string]interface{}, error) {
	db := GetDb()
	tableData := make([]map[string]interface{}, 0)

	//LOG QUERY SQL
	if os.Getenv("server") == "localhost" {
		fmt.Println(getSQLRaw(sqlQuery, args...))
	}

	//stmt, err := db.Prepare(sqlQuery)
	//if err != nil {
	//	return tableData, err
	//}
	//defer stmt.Close()
	//
	//rows, err := stmt.Query(args...)
	//start := time.Now()
	ctx, cancel := context.WithTimeout(context.Background(), 90*time.Second)
	defer cancel()
	rows, err := db.QueryContext(ctx, strings.Replace(sqlQuery, "\n", " ", -1), args...)
	if err != nil {
		fmt.Println("sql error: ", getSQLRaw(sqlQuery, args...))
		return tableData, err
	}

	//elapsed := time.Since(start)
	//fmt.Println("--- Query (array) took : ", elapsed)

	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		return tableData, nil
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return tableData, nil
		}

		entry := make(map[string]interface{})
		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entry[col] = string(b)
			} else {
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	return tableData, nil
}

func Query(sqlQuery string, args ...interface{}) (map[string]interface{}, error) {
	db := GetDb()
	entryData := make(map[string]interface{}, 0)

	//stmt, err := db.Prepare(sqlQuery)
	//if err != nil {
	//	return entryData, err
	//}
	//defer stmt.Close()
	//
	//rows, err := stmt.Query(args...)

	//start := time.Now()
	rows, err := db.Query(sqlQuery, args...)
	if err != nil {
		_, file, no, _ := runtime.Caller(1)
		fmt.Printf("%v:%v sql err: %v\n", file, no, getSQLRaw(sqlQuery, args...))
		return entryData, err
	}
	//elapsed := time.Since(start)
	//fmt.Println("-- Query took : ", elapsed)

	defer rows.Close()

	//LOG QUERY SQL
	if os.Getenv("server") == "localhost" {
		//fmt.Println(getSQLRaw(sqlQuery, args...))
	}

	columns, err := rows.Columns()
	if err != nil {
		fmt.Println("sql error: ", getSQLRaw(sqlQuery, args...))
		return entryData, nil
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	if rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return entryData, nil
		}

		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entryData[col] = string(b)
			} else {
				entryData[col] = v
			}
		}
	}

	return entryData, nil
}

func Insert(table string, data map[string]interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "INSERT INTO " + table + " ("
	for col, val := range data {
		query += col + ","
		values = append(values, val)
	}
	query = query[:len(query)-1] + ") VALUES (" + strings.Repeat("?, ", len(data)-1) + "?)"
	res, err := GetDb().Exec(query, values...)
	if err != nil {
		fmt.Printf("Query Error. Reason %v \nQuery : `%s` \n %s\n", err, getSQLRaw(query, values...), utils.SimplyToJsonPretty(data))
	}

	return res, err
}

func UpdateDb(table string, data map[string]interface{}, where map[string]interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		query += col + " = ?,"
		values = append(values, val)
	}
	query = query[:len(query)-1] + " WHERE "

	for col, val := range where {
		query += col + "=? AND "
		values = append(values, val)
	}
	query = query[:len(query)-4] // -4 : to remove word 'AND' at the end

	res, err := GetDb().Exec(query, values...)
	if err != nil {
		fmt.Printf("Query Error >> `%s`\n", getSQLRaw(query, values...))
	}

	return res, err
}

func Update(table string, data map[string]interface{}, whereCond string, whereParams ...interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		query += col + " = ?,"
		values = append(values, val)
	}
	query = query[:len(query)-1] + " WHERE " + whereCond

	for _, param := range whereParams {
		values = append(values, param)
	}

	//query = query[:len(query)-4]
	//logSQLRaw(query, values...)

	res, err := GetDb().Exec(query, values...)
	if err != nil {
		fmt.Println("update err: ", getSQLRaw(query, values...), err)
	}

	return res, err
}

func Delete(table string, whereCond string, whereParams ...interface{}) (sql.Result, error) {
	if whereCond == "" || len(whereParams) == 0 {
		return nil, errors.New("can not delete without where condition")
	}
	query := "DELETE FROM " + table + " WHERE " + whereCond

	res, err := GetDb().Exec(query, whereParams...)
	if err != nil {
		fmt.Println("Executing Query Error : ", getSQLRaw(query, whereParams...))
	}

	return res, err
}

//func ResultArray(rows *sql.Rows) ([]map[string]interface{}, error) {
//	tableData := make([]map[string]interface{}, 0)
//	defer rows.Close()
//
//	columns, err := rows.Columns()
//	if err != nil {
//		return tableData, nil
//	}
//
//	count := len(columns)
//	values := make([]interface{}, count)
//	scanArgs := make([]interface{}, count)
//
//	for i := range values {
//		scanArgs[i] = &values[i]
//	}
//
//	for rows.Next() {
//		err := rows.Scan(scanArgs...)
//		if err != nil {
//			return tableData, nil
//		}
//
//		entry := make(map[string]interface{})
//		for i, col := range columns {
//			v := values[i]
//
//			b, ok := v.([]byte)
//			if ok {
//				entry[col] = string(b)
//			} else {
//				entry[col] = v
//			}
//		}
//		tableData = append(tableData, entry)
//	}
//
//	return tableData, nil
//}

//func Result(rows *sql.Rows) (map[string]interface{}, error) {
//	entryData := make(map[string]interface{}, 0)
//
//	columns, err := rows.Columns()
//	if err != nil {
//		return entryData, nil
//	}
//
//	defer rows.Close()
//
//	count := len(columns)
//	values := make([]interface{}, count)
//	scanArgs := make([]interface{}, count)
//
//	for i := range values {
//		scanArgs[i] = &values[i]
//	}
//
//	if rows.Next() {
//		err := rows.Scan(scanArgs...)
//		if err != nil {
//			return entryData, nil
//		}
//
//		for i, col := range columns {
//			v := values[i]
//
//			b, ok := v.([]byte)
//			if ok {
//				entryData[col] = string(b)
//			} else {
//				entryData[col] = v
//			}
//		}
//	}
//
//	return entryData, nil
//}

func getSQLRaw(sql string, params ...interface{}) string {
	for i := 0; i < len(params); i++ {
		index := strings.Index(sql, "?")
		sql = sql[:index] + fmt.Sprintf("%v", params[i]) + sql[index+1:]
	}
	return sql
}
