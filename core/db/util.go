package db

import (
	"database/sql"
	"fmt"
	"reflect"
	"regexp"
	"strings"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
)

func FormatArgs(sql string, data map[string]interface{}) (string, []interface{}) {
	result := make([]interface{}, 0)
	for true {
		indexStart := strings.Index(sql, "$")
		if indexStart >= 0 {
			indexEnd := 0

			for i := indexStart + 1; i < len(sql); i++ {
				found := sql[indexStart : i+1]
				if strings.Contains(found, ",") || strings.Contains(found, " ") ||
					strings.Contains(found, ")") || strings.Contains(found, "(") ||
					strings.Contains(found, "'") {
					indexEnd = i
					break
				}
			}

			if indexEnd == 0 {
				indexEnd = len(sql)
			}

			key := sql[indexStart+1 : indexEnd]
			key = strings.TrimSpace(key)
			if data[key] != nil {
				result = append(result, data[key])
			} else {
				fmt.Printf("Map Data with key '%s' Not Found! \n", key)
			}
			sql = strings.Replace(sql, fmt.Sprintf("$%s", key), " ? ", 1)
		} else {
			break
		}
	}

	return sql, result
}

func MapParam(sql string, params map[string]interface{}) (string, []interface{}) {
	//change any variable starts with "$"
	for key, v := range params {
		searchKey := fmt.Sprintf("$%s", key)
		if strings.Contains(sql, searchKey) {
			sql = strings.Replace(sql, searchKey, cast.ToString(v), -1)
		}
	}

	r, _ := regexp.Compile("@[a-zA-Z]+")

	result := make([]interface{}, 0)
	for {
		key := r.FindString(sql)
		if key != "" {
			key = strings.Replace(key, "@", "", 1)
			sql = strings.Replace(sql, fmt.Sprintf("(@%s)", key), fmt.Sprintf("@%s", key), 1)
			if s := reflect.ValueOf(params[key]); s.Kind() == reflect.Slice {
				arrLenght := 0
				for i := 0; i < s.Len(); i++ {
					if values := reflect.ValueOf(s.Index(i).Interface()); values.Kind() == reflect.Slice {
						for j := 0; j < values.Len(); j++ {
							result = append(result, values.Index(j).Interface())
							arrLenght += 1
						}
					} else {
						result = append(result, s.Index(i).Interface())
						arrLenght += 1
					}
				}
				sql = strings.Replace(sql, fmt.Sprintf("@%s", key), WhereIn(arrLenght), 1)
			} else {
				if params[key] != nil {
					result = append(result, params[key])
				} else {
					fmt.Printf("Map Data with key '%s' Not Found! \n", key)
				}
				sql = strings.Replace(sql, fmt.Sprintf("@%s", key), " ? ", 1)
			}
		} else {
			break
		}
	}
	return sql, result
}

func WhereIn(n int) string {
	return " (" + strings.TrimRight(strings.Repeat("?,", n), ",") + ") "
}

func GetIds(response sql.Result) ([]int64, error) {
	ids := make([]int64, 0)
	lastId, err := response.LastInsertId()
	if err != nil {
		return ids, err
	}

	rows, err := response.RowsAffected()
	if err != nil {
		return ids, err
	}

	for i := lastId; i >= lastId-rows; i-- {
		ids = append(ids, i)
	}
	fmt.Printf("lastId: %v, rows: %v, sizeIds: %v, ids: %v\n", lastId, rows, len(ids), ids)
	return ids, nil
}
