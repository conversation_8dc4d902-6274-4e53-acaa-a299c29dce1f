package db

import (
	"errors"
	"fmt"
	"log"
	"reflect"
	"runtime"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
)

func (db *Repository) Set(sql string, args ...interface{}) *Repository {
	db.sql = sql
	db.args = args
	return db
}

func (db *Repository) Get(model interface{}) error {
	if reflect.TypeOf(model).Kind() != reflect.Ptr {
		return errors.New("model should be pointer")
	}

	rows, err := db.Conn.Query(db.sql, db.args...)
	if err != nil {
		query := getSQLRaw(db.sql, db.args...)
		_, file, line, _ := runtime.Caller(1)
		fmt.Printf("%v:%v >> Query Err: %v \n>>%v\n", file, line, query, err)
		return err
	}

	defer func() {
		err := rows.Close()
		if err != nil {
			log.Println("closing sql row error")
		}
	}()

	columns, err := rows.Columns()
	if err != nil {
		return err
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	var structField reflect.Type
	var structArray reflect.Value
	isModelSlice := reflect.ValueOf(model).Elem().Kind() == reflect.Slice

	if isModelSlice {
		structField = reflect.TypeOf(model).Elem()
		structArray = reflect.ValueOf(model).Elem()
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return err
		}

		if isModelSlice {
			newStruct := reflect.New(structField.Elem()).Elem()
			for i, col := range columns {
				cast.SetField(newStruct, col, values[i])
			}
			structArray.Set(reflect.Append(structArray, newStruct))
		} else {
			for i, col := range columns {
				cast.SetField(model, col, values[i])
			}
			break
		}
	}

	return nil
}
