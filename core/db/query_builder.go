package db

import "github.com/valyala/fasthttp"

type QueryBuilder interface {
	Select(fields ...string) QueryBuilder
	ForUpdate() QueryBuilder
	//From(tables ...string) QueryBuilder
	InnerJoin(table string) QueryBuilder
	LeftJoin(table string) QueryBuilder
	RightJoin(table string) QueryBuilder
	On(cond string) QueryBuilder
	Where(cond string, param ...interface{}) QueryBuilder
	And(cond string, param interface{}) QueryBuilder
	Or(cond string) QueryBuilder
	In(vals ...string) QueryBuilder
	OrderBy(fields ...string) QueryBuilder
	Asc() QueryBuilder
	Desc() QueryBuilder
	Limit(limit int) QueryBuilder
	Offset(offset int) QueryBuilder
	GroupBy(fields ...string) QueryBuilder
	Having(cond string) QueryBuilder
	Update(tables ...string) QueryBuilder
	Set(kv ...string) QueryBuilder
	Delete(tables ...string) QueryBuilder
	InsertInto(table string, fields ...string) QueryBuilder
	Values(vals ...string) QueryBuilder
	Subquery(sub string, alias string) string
	Query(query string) QueryBuilder
	String() string
	Get()  ([]map[string]interface{}, error)
	DataTables(ctx *fasthttp.RequestCtx) (map[string]interface{}, error)
}

func Table(table string) QueryBuilder  {
	qbSql := new(MySQLQueryBuilder)
	qbSql.Raw = make(map[string]interface{})
	qbSql.Raw["from"] = table
	qbSql.Select("*")
	return qbSql
}

