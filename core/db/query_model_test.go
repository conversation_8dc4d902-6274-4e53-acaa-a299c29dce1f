package db

import (
	"database/sql"
	"fmt"
	"os"
	"testing"

	"github.com/joho/godotenv"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

func TestRepository_Query(t *testing.T) {
	godotenv.Load("/Users/<USER>/Documents/WORK/api-pos/.env")
	// source := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", username, password, dbHost, dbPort, dbName)
	source := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", os.<PERSON>env("db_user"), os.<PERSON>env("db_password"), os.<PERSON>env("db_host"), "3306", os.<PERSON>env("db_name"))
	db, err := sql.Open("mysql", source)
	if err != nil {
		t.Error(err)
	}
	sql := "select admin_id, email from admin limit 1"
	var singleAdmin models.AdminEntity

	repo := Repository{
		Conn: db,
	}
	type args struct {
		model interface{}
		sql   string
		args  []interface{}
	}
	tests := []struct {
		name    string
		repo    *Repository
		args    args
		wantErr bool
	}{
		{"single", &repo, args{model: &singleAdmin, sql: sql}, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := tt.repo.Query(tt.args.model, tt.args.sql, tt.args.args...); (err != nil) != tt.wantErr {
				t.Errorf("Repository.Query() error = %v, wantErr %v", err, tt.wantErr)
			}
			fmt.Println(">>> ", singleAdmin)
		})
	}
}
