package db

import (
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"gitlab.com/uniqdev/backend/api-pos/core/log"
)

type Transaction interface {
	Insert(table string, data map[string]interface{}) (sql.Result, error)
	Update(table string, data map[string]interface{}, whereCond string, whereParams ...interface{}) (sql.Result, error)
	Delete(table string, whereCond string, whereParams ...interface{}) sql.Result
	Exec(query string, params ...interface{}) (sql.Result, error)
}

type TxFn func(Transaction) error

type SqlTx struct {
	Tx *sql.Tx
}

func (s SqlTx) Insert(table string, data map[string]interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "INSERT INTO " + table + " ("
	for col, val := range data {
		query += col + ","
		values = append(values, val)
	}
	query = query[:len(query)-1] + ") VALUES (" + strings.Repeat("?, ", len(data)-1) + "?)"
	resp, err := s.Tx.Exec(query, values...)
	if err != nil {
		fmt.Printf("%v \nquery : %s", err, getSQLRaw(query, values...))
		panic(err)
	}
	return resp, err
}

func (s SqlTx) Update(table string, data map[string]interface{}, whereCond string, whereParams ...interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		query += col + " = ?,"
		values = append(values, val)
	}
	query = query[:len(query)-1] + " WHERE " + whereCond

	for _, param := range whereParams {
		values = append(values, param)
	}

	res, err := s.Tx.Exec(query, values...)
	if err != nil {
		fmt.Printf("%v \nquery : %s", err, getSQLRaw(query, values...))
		panic(err)
	}

	return res, err
}

func (s SqlTx) Delete(table string, whereCond string, whereParams ...interface{}) sql.Result {
	if whereCond == "" || len(whereParams) == 0 {
		fmt.Println("can not delete without where condition")
		return nil
	}
	query := "DELETE FROM " + table + " WHERE " + whereCond

	res, err := s.Tx.Exec(query, whereParams...)
	if err != nil {
		fmt.Println("Executing Query Error : ", getSQLRaw(query, whereParams...))
		panic(err)
	}

	return res
}

func (s SqlTx) Exec(query string, params ...interface{}) (sql.Result, error) {
	res, err := s.Tx.Exec(query, params...)
	if err != nil {
		fmt.Println("Executing native Query Error : ", getSQLRaw(query, params...))
		panic(err)
	}
	return res, err
}

func BeginTrx() (SqlTx, error) {
	tx, err := GetDb().Begin()
	return SqlTx{tx}, err
}

func WithTransaction(fn TxFn) (err error) {
	tx, err := BeginTrx()
	if err != nil {
		return err
	}

	defer func() {
		if p := recover(); p != nil {
			// a panic occurred, rollback and repanic
			log.Info("panic, rollback...")
			log.IfError(tx.Tx.Rollback())
			err = errors.New(fmt.Sprintf("%v", p))
			//panic(p)
		} else if err != nil {
			// something went wrong, rollback
			log.Info("rollback...")
			log.IfError(tx.Tx.Rollback())
		} else {
			// all good, commit
			if log.IfError(tx.Tx.Commit()) {
				log.Info("committing err...")
			}
		}
	}()
	err = fn(tx)
	return err
}

func TryTransaction() {
	err := WithTransaction(func(transaction Transaction) error {
		_, err := transaction.Insert("test", map[string]interface{}{"result": "hello 1"})

		_, err = transaction.Insert("test", map[string]interface{}{"result": "hello 2"})
		fmt.Println("END err : ", err)
		return nil
	})

	log.IfError(err)
}
