package db

import (
	"database/sql"
	"errors"
	"fmt"
	"reflect"
	"runtime"
	"strings"
)

func (db *Repository) Query(model interface{}, sql string, args ...interface{}) error {
	if reflect.TypeOf(model).Kind() != reflect.Ptr {
		return errors.New("model should be pointer")
	}

	rows, err := db.Conn.Query(sql, args...)
	if err != nil {
		query := getSQLRaw(db.sql, db.args...)
		_, file, line, _ := runtime.Caller(1)
		fmt.Printf("%v:%v >> Query Err: %v \n>>%v\n", file, line, query, err)
		return err
	}

	defer func() {
		err := rows.Close()
		if err != nil {
			fmt.Println("closing sql row error")
		}
	}()

	// Get concrete type
	modelType := reflect.TypeOf(model)

	// Scan into struct or slice
	if modelType.Kind() == reflect.Ptr {
		// Dereference Ptr
		modelType = modelType.Elem()
	}

	if modelType.Kind() == reflect.Struct {
		// Single struct
		if rows.Next() {
			return scanRow(rows, model)
		}
		return nil
	} else if modelType.Kind() == reflect.Slice {
		// Collection of structs
		slice := reflect.New(modelType).Elem()

		for rows.Next() {
			// s := slice.New()
			s := reflect.New(modelType.Elem()).Elem()
			err := scanRow(rows, s.Addr().Interface())
			if err != nil {
				return err
			}
			slice.Set(reflect.Append(slice, s))
		}
		reflect.Indirect(reflect.ValueOf(model)).Set(slice)
		return nil
	} else {
		return errors.New("model type must be struct or slice")
	}

	return nil
}

// scan row helper
func scanRow(rows *sql.Rows, model interface{}) error {
	// Create pointer to struct
	modelValue := reflect.ValueOf(model)

	// Dereference if needed
	if modelValue.Kind() == reflect.Ptr {
		modelValue = modelValue.Elem()
	}

	// Get number of fields
	numFields := modelValue.NumField()

	// Get column names from row
	cols, err := rows.Columns()
	if err != nil {
		return err
	}

	fmt.Println("cols: ", cols)
	// Scan each field
	for i := 0; i < numFields; i++ {
		field := modelValue.Field(i)

		// Get column name from tag
		tag := modelValue.Type().Field(i).Tag.Get("json")

		// Find index of column in rows
		colIdx := -1
		for j, col := range cols {
			if col == tag || strings.HasPrefix(tag, fmt.Sprintf("%s,", col)) {
				colIdx = j
				break
			}
		}

		// Scan column into field
		if colIdx != -1 {
			if err := rows.Scan(field.Addr().Interface()); err != nil {
				return err
			}
		}
	}

	return nil
}
