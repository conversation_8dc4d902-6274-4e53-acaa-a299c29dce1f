package utils

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"math/rand"
	"net"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"reflect"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"golang.org/x/crypto/bcrypt"
)

func Cleanup(ctx *fasthttp.RequestCtx) {
	if r := recover(); r != nil {
		date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
		ctx.SetContentType("text/plain; charset=utf-8")
		ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		fmt.Fprint(ctx, r)
		fmt.Println(date, " : Panic ", r)
	}
}

func CleanupPanic() {
	if r := recover(); r != nil {
		fmt.Println("PANIC occur ---> ", r)
	}
}

// return true if found error
func CheckError(ctx *fasthttp.RequestCtx, err error) bool {
	if err != nil {
		date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
		ctx.SetContentType("text/plain; charset=utf-8")
		ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		fmt.Fprintln(ctx, "Server Error "+err.Error())
		_, fn, line, _ := runtime.Caller(1)
		//fmt.Printf("%s   %s:%d %v", date, fn, line, err)
		log.Printf("%s   %s:%d %v", date, fn, line, err)

		errMsg := fmt.Sprintf("[%s] %s [error] %s:%d %v", os.Getenv("server"), date, fn, line, err)
		//SendMailError(errMsg)
		if os.Getenv("server") != "localhost" && os.Getenv("server") != "development" {
			SendMessageToSlack(errMsg)
		}

		return true
	}
	return false
}

// return true if found error
func CheckErr(err error) bool {
	if err != nil {
		date := time.Unix(time.Now().Unix()+25200, 0).Format("02-01-2006 15:04:05")
		//fmt.Println(date, "   Error While ", onDoing, ". ", err)
		_, fn, line, _ := runtime.Caller(1)
		//fmt.Printf("%s  [error] %s:%d %v", date, fn, line, err)

		log.Printf("%s  [error] %s:%d %v", date, fn, line, err)
		errMsg := fmt.Sprintf("[%s] - %s  [error] %s:%d %v", os.Getenv("server"), date, fn, line, err)
		//SendMailError(errMsg)

		if os.Getenv("server") != "localhost" && os.Getenv("server") != "development" {
			SendMessageToSlack(errMsg)
		}
		return true
	}
	return false
}

func SendMessageToSlack(message string) {
	request := HttpRequest{}
	request.Method = "POST"
	request.Url = os.Getenv("SLACK_WEBHOOK_URL")

	if request.Url == "" {
		request.Url = "*****************************************************************************"
	}
	//request.PostRequest.Body = map[string]interface{}{
	//	"text": message,
	//}

	var slackMsg models.SlackMessage
	var slackFields []models.SlackFields
	slackFields = append(slackFields, models.SlackFields{
		Title: "Project",
		Value: "API POS Go",
		Short: true,
	})
	slackFields = append(slackFields, models.SlackFields{
		Title: "Environment",
		Value: os.Getenv("server"),
		Short: true,
	})
	slackMsg.Attachments = append(slackMsg.Attachments, models.SlackAttachments{
		Fallback: message,
		Text:     message,
		Color:    "#F35A00",
		Fields:   slackFields,
	})
	request.PostRequest.Body = slackMsg

	_, err := request.Execute()
	if err != nil {
		fmt.Println("Sending to slack error ", err)
	}
}

func MillisToDateTime(timeMillis int64) (string, error) {
	t := time.Unix(timeMillis, 0)
	return t.Format("2006-01-02 15:04:05"), nil
}

func ByteToInt(data []byte) int {
	intValue, err := strconv.Atoi(string(data))
	if err != nil {
		return 0
	}
	return intValue
}

func StringToInt(data string) int {
	res, err := strconv.Atoi(data)
	if err != nil {
		return 0
	} else {
		return res
	}
}

func ReplaceEmoji(data string) string {
	//[^a-zA-Z0-9]+
	//[\x{1F600}-\x{1F6FF}|[\x{2600}-\x{26FF}]
	var emojiRx = regexp.MustCompile(`[^a-zA-Z0-9]+`)
	return emojiRx.ReplaceAllString(data, ` `)
}

func CurrencyFormat(amount int) string {
	result := ""
	amountStr := strconv.Itoa(amount)
	index := 1
	for i := len(amountStr); i > 0; i-- {
		data := amountStr[i-1 : i]
		if index%3 == 0 {
			result += data + "."
		} else {
			result += data
		}
		index++
	}
	result = Reverse(result)
	if strings.HasPrefix(result, ".") {
		result = result[1:]
	} else if strings.HasPrefix(result, "-.") {
		result = "-" + result[2:]
	}

	return result
}

func Reverse(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

func ExeCommand(name string, args ...string) (string, error) {
	cmd := exec.Command(name, args...)

	cmdOutput := &bytes.Buffer{}
	// Attach buffer to command
	cmd.Stdout = cmdOutput

	err := cmd.Run()
	if err != nil {
		fmt.Printf("==> Error When Executing : %s\n", strings.Join(cmd.Args, " "))
		fmt.Println("==> Error Message : ", err.Error())
		return "", err
	}

	outputByte := cmdOutput.Bytes()
	if len(outputByte) > 0 {
		return string(outputByte), nil
	} else {
		return "", nil
	}
}

func ReadFile(path string) (string, error) {
	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()
	result, err := ioutil.ReadAll(file)
	return string(result), nil
}

const letterBytes = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

var seededRand *rand.Rand = rand.New(
	rand.NewSource(time.Now().UnixNano()))

func RandStringBytes(n int) string {
	b := make([]byte, n)
	for i := range b {
		b[i] = letterBytes[seededRand.Intn(len(letterBytes))]
	}
	return string(b)
}

func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), 14)
	return string(bytes), err
}

func CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func ToString(data interface{}) string {
	switch v := data.(type) {
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float32:
		return fmt.Sprintf("%f", v)
	case float64:
		return strconv.FormatFloat(v, 'f', 2, 64)
	case string:
		return v
	case []uint8:
		return string(v)
	case []interface{}:
		return fmt.Sprintf("%s", v[0])
	case nil:
		return ""
	default:
		fmt.Println("[ToString] - Invalid recognize data type toString. Type : ", reflect.TypeOf(data), " | Data : ", data)
		return fmt.Sprintf("%s", data)
	}
}

func ToInt(data interface{}) int {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.Atoi(dataStr)
	if err != nil {
		switch i := data.(type) {
		case float32:
			return int(i)
		case float64:
			return int(i)
		default:
			fmt.Printf("failed converting '%v' to int, type is %v\n", data, reflect.TypeOf(data))
			return 0
		}
	} else {
		return result
	}
}

func ToInt64(data interface{}) int64 {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.ParseInt(dataStr, 10, 64)
	if err != nil {

		return 0
	} else {
		return result
	}
}

func ToAbs(data int, isToAbs bool) int {
	if (isToAbs && data < 0) || (!isToAbs && data >= 0) {
		return data * -1
	} else {
		return data
	}
}

func ToFloat(data interface{}) float64 {
	dataStr := ToString(data)
	result, err := strconv.ParseFloat(dataStr, 64)
	if err != nil {
		//fmt.Println("Converting string to float error", err, "Data : ", data)
		return 0
	}
	return result
}

func ForcePositive(data int) int {
	if data < 0 {
		return data * -1
	}
	return data
}

func TakeMax(data string, max int) string {
	if len(data) > max {
		return data[:max]
	}
	return data
}

func TakeNotEmpty(texts ...string) string {
	for _, txt := range texts {
		if strings.TrimSpace(txt) != "" {
			return txt
		}
	}
	return ""
}

func LogRequest(data map[string]interface{}) {
	url := "https://us-central1-uniq-pos.cloudfunctions.net/api/log_request"
	jsonData, _ := json.Marshal(data)

	var jsonStr = []byte(jsonData)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	req.Header.Set("Authorization", "Bearer AYlgZtsoEaZOkSddZccRpuZOQkCRMPMhrKdATRoS")
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
}

// FromRequest return client's real public IP address from http request headers.
func GetIp(r *fasthttp.RequestCtx) string {
	// Fetch header value
	xRealIP := string(r.Request.Header.Peek("X-Real-Ip"))
	xForwardedFor := string(r.Request.Header.Peek("X-Forwarded-For"))

	// If both empty, return IP from remote address
	if xRealIP == "" && xForwardedFor == "" {
		var remoteIP string

		// If there are colon in remote address, remove the port number
		// otherwise, return remote address as is
		add := r.RemoteAddr().String()
		if strings.ContainsRune(add, ':') {
			remoteIP, _, _ = net.SplitHostPort(add)
		} else {
			remoteIP = r.RemoteAddr().String()
		}

		return remoteIP
	}

	// Check list of IP in X-Forwarded-For and return the first global address
	for _, address := range strings.Split(xForwardedFor, ",") {
		address = strings.TrimSpace(address)
		isPrivate, err := isPrivateAddress(address)
		if !isPrivate && err == nil {
			return address
		}
	}

	// If nothing succeed, return X-Real-IP
	return xRealIP
}

func OneOfThese(options ...interface{}) interface{} {
	for _, option := range options {
		if option != nil {
			return option
		}
	}
	return nil
}

func IsNumber(data interface{}) bool {
	if _, err := strconv.Atoi(ToString(data)); err == nil {
		return true
	}
	return false
}

func ConvertToMap([]map[string]interface{}) map[string]map[string]interface{} {
	result := make(map[string]map[string]interface{})

	return result
}

func ConvertToSingleMap(dataMapArray []map[string]interface{}, key, value string) map[interface{}]interface{} {
	result := make(map[interface{}]interface{})
	for _, data := range dataMapArray {
		var resultKey, resultValue interface{}
		for k, v := range data {
			if k == key {
				resultKey = v
			} else if k == value {
				resultValue = v
			}
		}
		result[resultKey] = resultValue
	}
	return result
}

func GetOutboundIP() net.IP {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		log.Fatal(err)
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)

	return localAddr.IP
}

func SimplyToJson(data interface{}) string {
	if data == nil {
		return ""
	}

	resp, err := json.Marshal(data)
	if err == nil {
		return string(resp)
	} else {
		return "<INVALID>"
	}
}

func SimplyToJsonPretty(data interface{}) string {
	resp, err := json.Marshal(data)
	if err == nil {
		var prettyJSON bytes.Buffer
		if err = json.Indent(&prettyJSON, []byte(string(resp)), "", "\t"); err == nil {
			return string(prettyJSON.Bytes())
		}
		return string(resp)
	} else {
		return "<INVALID>"
	}
}

func CurrentMillis() int64 {
	return time.Now().UnixNano() / (int64(time.Millisecond) / int64(time.Nanosecond))
}

func CurrentMillisFormat(format string, timeOffsetMinutes int) string {
	return time.Now().Add(time.Duration(timeOffsetMinutes) * time.Minute).Format(format)
}

func GetFileName(path string, withoutExtension bool) string {
	pathArray := strings.Split(path, "/")
	lastPath := path
	if len(pathArray) > 1 {
		lastPath = pathArray[len(pathArray)-1]
	}

	if withoutExtension {
		lastPath = strings.TrimSuffix(lastPath, filepath.Ext(lastPath))
	}

	return lastPath
}

func CopyFile(sourcePath, destPath string) error {
	inputFile, err := os.Open(sourcePath)
	if err != nil {
		return fmt.Errorf("couldn't open source file: %s", err)
	}
	isExist, _ := Exist(filepath.Dir(destPath))
	if !isExist {
		err = os.MkdirAll(filepath.Dir(destPath), os.ModePerm)
		if err != nil {
			return err
		}
	}

	outputFile, err := os.Create(destPath)
	if err != nil {
		inputFile.Close()
		return fmt.Errorf("couldn't open dest file: %s", err)
	}
	defer outputFile.Close()
	_, err = io.Copy(outputFile, inputFile)
	inputFile.Close()
	if err != nil {
		return fmt.Errorf("writing to output file failed: %s", err)
	}
	return nil
}

func Exist(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return true, err
}

func TakesOnly(data map[string]interface{}, keys ...string) map[string]interface{} {
	result := make(map[string]interface{})
	for _, key := range keys {
		result[key] = data[key]
	}
	return result
}

func ArrayOf(data ...interface{}) []interface{} {
	result := make([]interface{}, 0)
	for _, row := range data {
		fmt.Println(row, reflect.TypeOf(row).Kind())
		if reflect.TypeOf(row).Kind() == reflect.Slice {
			result = append(result, ToInterfaceArray(row)...)
		} else {
			result = append(result, row)
		}
	}

	return result
}

func ToInterfaceArray(data interface{}) []interface{} {
	if reflect.TypeOf(data).Kind() != reflect.Slice {
		return []interface{}{data}
	}

	result := make([]interface{}, 0)
	switch v := data.(type) {
	case []string:
		for _, row := range v {
			result = append(result, row)
		}
	case []int:
		for _, row := range v {
			result = append(result, row)
		}
	case []int32:
		for _, row := range v {
			result = append(result, row)
		}
	case []int64:
		for _, row := range v {
			result = append(result, row)
		}
	case []float32:
		for _, row := range v {
			result = append(result, row)
		}
	case []float64:
		for _, row := range v {
			result = append(result, row)
		}
	default:
		fmt.Println("unknown slice of", reflect.TypeOf(data))
	}

	return result
}

func GetErrCodeSQL(msg string) string {
	code := "0"
	p := regexp.MustCompile("^Error ([0-9]{4}): ")
	result := p.FindAllStringSubmatch(msg, 1)
	if len(result) > 0 {
		code = result[0][1]
	}
	return code
}

func ReverseString(s string) string {
	result := ""
	runes := []rune(s)
	for i := 0; i < len(runes); i++ {
		str := fmt.Sprintf("%c", runes[i])

		//if uppercase convert to lowercase
		if strings.ToUpper(str) == str {
			result += strings.ToLower(str)
		} else {
			result += strings.ToUpper(str)
		}
	}
	return result
}

func DownloadFileBase64(base64Data interface{}, savePath string) error {
	var dec []byte
	var err error

	switch v := base64Data.(type) {
	case string:
		dec, err = base64.StdEncoding.DecodeString(v)
	case []byte:
		dec = v
	default:
		err = fmt.Errorf("unrecognise base64 data type: %v", reflect.TypeOf(base64Data))
	}

	if err != nil {
		fmt.Println("decode error...", base64Data)
		return err
	}

	dir, _ := filepath.Split(savePath)
	if dir != "" {
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			err = os.MkdirAll(dir, os.ModePerm)
			if err != nil {
				fmt.Println("failed to create dir : ", dir)
				return err
			}
		}
	}

	f, err := os.Create(savePath)
	if err != nil {
		return err
	}
	defer f.Close()

	if _, err := f.Write(dec); err != nil {
		return err
	}
	if err := f.Sync(); err != nil {
		return err
	}

	return nil
}

// SaveBase64Image saves a base64 encoded image string to a file.
func SaveBase64Image(base64Image string, filePath string) error {
	// Decode the base64 string
	data, err := base64.StdEncoding.DecodeString(base64Image)
	if err != nil {
		return fmt.Errorf("error decoding base64 string: %w", err)
	}

	// Write the decoded data to the file
	err = os.WriteFile(filePath, data, 0644) // 0644: read/write permissions for owner, read-only for others
	if err != nil {
		return fmt.Errorf("error writing to file: %w", err)
	}

	return nil
}

func MinifyHtml(str string) string {
	str = strings.Replace(str, "\n", "", -1)
	str = regexp.MustCompile(`\s+`).ReplaceAllString(str, " ")
	return str
}

func ReplaceWithMap(original, prefix string, mapReplacer map[string]string) string {
	relacerArr := make([]string, len(mapReplacer)*2)
	for k, v := range mapReplacer {
		relacerArr = append(relacerArr, prefix+k, v)
	}
	return strings.NewReplacer(relacerArr...).Replace(original)
}

func GetPositionFromDisplayNota(id string) (int, error) {
	// Determine the length of the alphabet prefix
	alphabetLength := 0
	for i, char := range id {
		if !strings.ContainsAny(string(char), "ABCDEFGHIJKLMNOPQRSTUVWXYZ") {
			alphabetLength = i
			break
		}
	}

	// Extract the row number
	rowNumberStr := id[alphabetLength+8:] // Skip alphabet and date (8 digits)
	rowNumber, err := strconv.Atoi(rowNumberStr)
	if err != nil {
		return 0, fmt.Errorf("failed to parse row number: %s", err)
	}

	return rowNumber, nil
}

// converting receipt id back to its original value (number), if it conaints alphabet it doesn change
func ConvertReceiptId(receiptId string) string {
	// Mapping of characters to their corresponding numbers
	charToNum := map[string]string{
		"M": "0", "N": "1", "K": "2", "O": "3", "H": "4", "S": "5", "I": "7", "A": "8", "L": "9",
		"P": "1", "U": "5", "Q": "7", "D": "8", "E": "9",
	}

	var newReceiptId []string

	// Iterate through each character in the receipt ID
	for _, char := range receiptId {
		if num, ok := charToNum[string(char)]; ok {
			// If the character is in the mapping, replace it with the number
			newReceiptId = append(newReceiptId, num)
		} else {
			// If the character is not in the mapping, keep it as it is
			newReceiptId = append(newReceiptId, string(char))
		}
	}

	// Join the characters back into a string
	return strings.Join(newReceiptId, "")
}
