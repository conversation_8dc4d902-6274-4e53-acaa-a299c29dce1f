package array

import (
	"reflect"
	"strings"
)

func RemoveEmpty(data map[string]interface{}, ignoreEmpty ...string) map[string]interface{} {
	for k, v := range data {
		if strings.Contains(strings.Join(ignoreEmpty, ","), k) {
			continue
		}
		if row, ok := v.(string); ok && strings.TrimSpace(row) == "" {
			delete(data, k)
		} else if reflect.ValueOf(v).Kind() == reflect.Slice && reflect.ValueOf(v).Len() == 0 {
			delete(data, k)
		} else if row, ok := v.(int); ok && row == 0 {
			delete(data, k)
		} else if row, ok := v.(int64); ok && row == 0 {
			delete(data, k)
		} else if row, ok := v.(float32); ok && row == 0 {
			delete(data, k)
		} else if row, ok := v.(float64); ok && row == 0 {
			delete(data, k)
		}

	}
	return data
}

func Take(data map[string]interface{}, keys ...string) map[string]interface{} {
	result := make(map[string]interface{})
	for _, key := range keys {
		result[key] = data[key]
	}
	return result
}

func Remove(data map[string]interface{}, keys ...string) {
	for _, key := range keys {
		delete(data, key)
	}
}

func Merge(mainMap map[string]interface{}, anyMaps ...map[string]interface{}) {
	for _, m := range anyMaps {
		for k, v := range m {
			mainMap[k] = v
		}
	}
}
