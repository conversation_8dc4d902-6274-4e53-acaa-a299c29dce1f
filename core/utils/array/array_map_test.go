package array

import (
	"fmt"
	"reflect"
	"testing"
)

func TestRemoveEmpty(t *testing.T) {
	dataInput := map[string]interface{}{
		"id":    1,
		"name":  "",
		"total": 0,
	}
	dataOutput := map[string]interface{}{
		"id":    1,
		"total": 0,
	}
	type args struct {
		data        map[string]interface{}
		ignoreEmpty []string
	}
	tests := []struct {
		name string
		args args
		want map[string]interface{}
	}{
		{"test1", args{dataInput, []string{"total"}}, dataOutput},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RemoveEmpty(tt.args.data, tt.args.ignoreEmpty...); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RemoveEmpty() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRemove(t *testing.T) {
	data := map[string]interface{}{"id": 1, "name": "me", "from": "id"}
	type args struct {
		data map[string]interface{}
		keys []string
	}
	tests := []struct {
		name string
		args args
		want map[string]interface{}
	}{
		{"simple", args{data: data, keys: []string{"name", "id"}}, map[string]interface{}{"from": "id"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			Remove(tt.args.data, tt.args.keys...)
			if got := tt.args.data; !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Remove() = %v, want %v", got, tt.want)
			}
			fmt.Println("data now: ", tt.args.data)
		})
	}
}

func TestMerge(t *testing.T) {
	type args struct {
		mainMap map[string]interface{}
		anyMaps []map[string]interface{}
	}

	dataMaps := []map[string]interface{}{
		{
			"address": "jogja",
		},
	}
	tests := []struct {
		name string
		args args
	}{
		{"simple", args{map[string]interface{}{"name": "annas"}, dataMaps}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			Merge(tt.args.mainMap, tt.args.anyMaps...)
			fmt.Println("after merge: ", tt.args.mainMap)
		})
	}
}
