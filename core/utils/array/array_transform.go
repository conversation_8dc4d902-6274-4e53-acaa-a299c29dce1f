package array

import "gitlab.com/uniqdev/backend/api-pos/core/cast"

func FlatMapArray(data []map[string]interface{}, key string) map[string]map[string]interface{} {
	result := make(map[string]map[string]interface{})
	for _, row := range data {
		if row[key] == nil {
			continue
		}
		result[cast.ToString(row[key])] = row
	}
	return result
}

func GroupBy(data []map[string]interface{}, groupKey string) map[string][]map[string]interface{} {
	result := make(map[string][]map[string]interface{})
	for _, row := range data {
		if _, ok := result[cast.ToString(row[groupKey])]; !ok {
			result[cast.ToString(row[groupKey])] = make([]map[string]interface{}, 0)
		}
		result[cast.ToString(row[groupKey])] = append(result[cast.ToString(row[groupKey])], row)
	}
	return result
}

func GetUnique(data []map[string]interface{}, key string) []interface{} {
	result := make(map[string]bool)
	for _, row := range data {
		if row[key] == nil {
			continue
		}
		result[cast.ToString(row[key])] = true
	}
	keys := make([]interface{}, 0, len(result))
	for k := range result {
		keys = append(keys, k)
	}
	return keys
}