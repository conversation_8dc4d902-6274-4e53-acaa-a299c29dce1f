package array

import (
	"fmt"
	"reflect"
)

func Contain(data, search interface{}) bool {
	if s := reflect.ValueOf(data); s.Kind() == reflect.Slice {
		for i := 0; i < s.Len(); i++ {
			fmt.Printf(" '%v' VS '%v' | ", s.Index(i).Interface(), search)
			if s.Index(i).Interface() == search {
				fmt.Println("match...")
				return true
			}
		}
	} else {
		fmt.Println("array.Contain warn: not slice")
	}
	fmt.Println("nof match...")
	return false
}

// LogSizeData prints the size of each data slice provided as arguments.
func LogSizeData(data ...interface{}) {
	for i, arg := range data {
		// Get the type of the argument
		argType := reflect.TypeOf(arg)

		// Check if the argument is a slice
		if argType.Kind() == reflect.Slice {
			// Get the length of the slice
			sliceLen := reflect.ValueOf(arg).Len()

			// Print the size of the slice
			fmt.Printf("Data %d: %s, size: %d\n", i+1, argType.String(), sliceLen)
		} else {
			// If the argument is not a slice, print a message
			fmt.Printf("Data %d: Not a slice, cannot determine size\n", i+1)
		}
	}
}
