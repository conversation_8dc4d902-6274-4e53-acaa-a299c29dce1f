package utils

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
)

type Auth struct {
	Username string
	Password string
}

var authIndex = 0

// username: UserId, password: API Key
var authCollections = []Auth{
	{
		Username: "0794c0f3-a583-4345-ab29-b2094eeb57a9",
		Password: "0b65c264-fb17-43b3-b367-36299ebad352",
	},
	{
		Username: "3905e511-bba3-4b9d-a5f2-4c1595072b75",
		Password: "eaffb943-89e5-489c-acea-96532e59e365",
	},
	{
		Username: "7498a965-c52f-421b-8df8-b3a6ecee5733",
		Password: "7a87a9c7-25b1-495b-bc65-37101c092766",
	},
	{
		Username: "f6735979-0abd-420d-84cd-bf93912462d3",
		Password: "cd9fdfaa-fb54-48ae-aa14-8873d083c7ce",
	},
	{
		Username: "3950ff4b-42a5-4fb1-905a-57e17ab9770e",
		Password: "a7479721-0181-4227-ac12-5c243cf8feb0",
	},
	{
		Username: "ac12058b-f34f-4ae0-a77c-e2e51111e28b",
		Password: "a1acf59a-e996-4f52-b977-640ac26a4660",
	},
	{
		Username: "eba8f247-0cc2-4f02-a577-27d38d46dcb1",
		Password: "b996a1e1-c18e-4eb4-8124-e143cf9cee88",
	},
	{
		Username: "940bfda4-3181-47b3-9779-4c8fb982a941",
		Password: "0668ef43-5ae9-4a0f-9013-ad598bc7427e",
	},
	{
		Username: "ee64c7b1-1224-4414-a48f-0333393384c4",
		Password: "c1deed5c-815d-4ec3-9694-f53e73694e5a",
	},
	{
		Username: "216afd73-e794-4a5b-9f94-284cf317b509",
		Password: "617cfa2e-3ac7-4795-8fb2-d581db782ca7",
	},
}

func HtmlToImage(html, css string) (string, error) {
	if strings.TrimSpace(html) == "" {
		return "", fmt.Errorf("empty html")
	}

	htmlUniq := fmt.Sprintf(`<html><style>%s</style>%s</html>`, css, html)
	result, err := HtmlToImageWithUniq(htmlUniq)
	if err == nil && result != "" {
		return result, nil
	}

	result, err = HtmlToImageWithHtci(html, css)
	return result, err
}

func HtmlToImageWithUniq(html string) (string, error) {
	if strings.TrimSpace(html) == "" {
		return "", fmt.Errorf("empty html")
	}

	// if os.Getenv("ENV") == "staging" {
	// 	return "", fmt.Errorf("not running in staging")
	// }

	req := HttpRequest{
		Method: "POST",
		Url:    "https://us-central1-uniq-187911.cloudfunctions.net/service/generator/html",
		PostRequest: PostRequest{
			Body: map[string]interface{}{
				"html":   html,
				"output": "base64",
			},
		},
	}

	resp, err := req.Execute()
	if err != nil {
		return "", err
	}

	// fmt.Println("image generator with UNIQ result: ", utils.TakeMax(string(resp), 50))
	var result map[string]interface{}
	err = json.Unmarshal(resp, &result)
	if err != nil {
		return "", err
	}

	if ToString(result["base64"]) == "" {
		return "", fmt.Errorf("empty base64 image")
	}

	return ToString(result["base64"]), err
}

func HtmlToImageWithHtci(html, css string) (string, error) {
	if strings.TrimSpace(html) == "" {
		return "", fmt.Errorf("empty html")
	}

	if os.Getenv("server") == "development" {
		htmlStr := strings.ReplaceAll(html, "\n", " ")
		htmlStr = strings.ReplaceAll(htmlStr, "\t", " ")
		fmt.Println("HTML: ", strings.ReplaceAll(htmlStr, "\n", " "))
	}

	req := HttpRequest{
		Method: "POST",
		Url:    "https://hcti.io/v1/image",
		PostRequest: PostRequest{
			Body: map[string]interface{}{
				"html": html,
				"css":  css,
			},
		},
	}

	auth := authCollections[authIndex]
	req.SetBasicAuth(auth.Username, auth.Password)
	resp, err := req.Execute()
	if err != nil {
		//if err.Error() == "status code : 429" {}
		fmt.Println(authIndex, "image generator err: ", err)
		authIndex = (authIndex + 1) % len(authCollections)
		return "", err
	}

	fmt.Println("image generator result: ", string(resp))
	var result map[string]interface{}
	err = json.Unmarshal(resp, &result)
	return ToString(result["url"]), err
}
