package utils

import "os"

func SendMailError(message string) {
	// Set up authentication information.
	//auth := smtp.PlainAuth("", "<EMAIL>", "makestories", "smtp.zoho.com")
	//
	//// Connect to the server, authenticate, set the sender and recipient,
	//// and send the email all in one step.
	//to := []string{"<EMAIL>"}
	//msg := []byte("To: <EMAIL>\r\n" +
	//	"Subject: UNIQ Server Error\r\n" +
	//	"\r\n" +
	//	message+"\r\n")
	//err := smtp.SendMail("smtp.zoho.com:587", auth, "<EMAIL>", to, msg)
	//if err != nil {
	//	log.Fatal(err)
	//}
	env := os.Getenv("server")
	if env != "localhost" && env != "development"{
		message = "*Server "+ os.Getenv("server") + " Error* \n" + message
		SendWhatsAppMessage(message, "group", "tech", "6285742257881-1495197570")
	}
}
