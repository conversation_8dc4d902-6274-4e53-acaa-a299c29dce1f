package utils

import "testing"

func TestHttpRequest_constructUrl(t *testing.T) {
	req := HttpRequest{
		Url: "https://example.com",
		Query: map[string]string{
			"key": "123",
		},
	}
	expected := "https://example.com?key=123"

	tests := []struct {
		name   string
		fields HttpRequest
		want   string
	}{
		{"test1", req, expected},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.fields.constructUrl(); got != tt.want {
				t.<PERSON>rrorf("HttpRequest.constructUrl() = %v, want %v", got, tt.want)
			}
		})
	}
}
