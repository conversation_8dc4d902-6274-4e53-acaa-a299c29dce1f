package utils

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"net/url"
	net "net/url"
	"os"
	"path/filepath"
	"strings"
)

type HttpRequest struct {
	Method           string
	Url              string
	Header           map[string]interface{}
	PostRequest      PostRequest
	MultipartRequest MultipartRequest
	Query            map[string]string
}

type PostRequest struct {
	Body interface{}
	Form map[string]string
}

type MultipartRequest struct {
	FilePath  string
	FileParam string
	Form      map[string]string
}

func (req *HttpRequest) SetBasicAuth(username, password string) {
	if req.Header == nil {
		req.Header = make(map[string]interface{})
	}

	auth := fmt.Sprintf("%s:%s", username, password)
	req.Header["Authorization"] = fmt.Sprintf("Basic %s", base64.StdEncoding.EncodeToString([]byte(auth)))
}

func (req HttpRequest) constructUrl() string {
	if req.Query == nil || len(req.Query) == 0 {
		return req.Url
	}

	u, err := url.Parse(req.Url)
	if err != nil {
		fmt.Println("Failed to parse URL:", err)
		return req.Url
	}

	queryParams := u.Query()
	for k, v := range req.Query {
		queryParams.Set(k, v)
	}

	u.RawQuery = queryParams.Encode()
	return u.String()
}

func (req HttpRequest) Execute() ([]byte, error) {
	return request(req)
}

func request(request HttpRequest) ([]byte, error) {
	logs := make([]string, 0)
	defer fmt.Println(strings.Join(logs, " || "))

	logs = append(logs, fmt.Sprintf("[REQ] | %s |  %s", request.Method, request.constructUrl()))

	var body string
	if len(request.PostRequest.Form) > 0 {
		data := net.Values{}
		for key, value := range request.PostRequest.Form {
			data.Set(key, value)
		}
		body = data.Encode()
		if request.Header == nil {
			request.Header = make(map[string]interface{})
		}
		request.Header["Content-Type"] = "application/x-www-form-urlencoded"
		logs = append(logs, fmt.Sprintf("Form: %s", SimplyToJson(request.PostRequest.Form)))
	} else if request.PostRequest.Body != nil {
		if request.Header == nil {
			request.Header = make(map[string]interface{})
		}
		request.Header["Content-Type"] = "application/json"
		dataJson, err := json.Marshal(request.PostRequest.Body)
		if err != nil {
			fmt.Println("Parse map to json error : ", err)
		}
		body = string(dataJson)
		logs = append(logs, fmt.Sprintf("Body: %s", body))
	}

	var req *http.Request
	var err error

	if request.MultipartRequest.FilePath != "" {
		req, err = request.multipart()
	} else {
		if body == "" {
			req, err = http.NewRequest(request.Method, request.constructUrl(), nil)
		} else {
			req, err = http.NewRequest(request.Method, request.constructUrl(), strings.NewReader(body))
		}
	}

	if err != nil {
		fmt.Println("Creating http request error ", err)
		return nil, err
	}

	if req == nil {
		return nil, fmt.Errorf("req is nil")
	}

	for key, value := range request.Header {
		req.Header.Set(key, ToString(value))
	}

	logs = append(logs, fmt.Sprintf("Headers: %v", SimplyToJson(req.Header)))
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("[REQ] '%s' | error %v \n", request.constructUrl(), err)
		return nil, err
	}

	logs = append(logs, fmt.Sprintf("status code %v", resp.StatusCode))
	fmt.Println(strings.Join(logs, " | "))

	defer resp.Body.Close()
	bodyResp, err := ioutil.ReadAll(resp.Body)
	if resp.StatusCode != 200 {
		fmt.Println("response body: ", string(bodyResp))
		return bodyResp, fmt.Errorf("status code : %d", resp.StatusCode)
	}
	return bodyResp, err
}

func createMultipartRequest(request HttpRequest) (*http.Request, error) {
	file, err := os.Open(request.MultipartRequest.FilePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile(request.MultipartRequest.FileParam, filepath.Base(request.MultipartRequest.FilePath))
	if err != nil {
		return nil, err
	}
	_, err = io.Copy(part, file)
	if err != nil {
		fmt.Println("copy file err: ", err)
		return nil, err
	}

	for key, val := range request.MultipartRequest.Form {
		_ = writer.WriteField(key, val)
	}
	err = writer.Close()
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", request.constructUrl(), body)
	if err != nil {
		fmt.Println("Failed to create HTTP request:", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	fmt.Println("multipart request set...", request.MultipartRequest.FileParam, request.MultipartRequest.FilePath)
	return req, err
}

func (req HttpRequest) multipart() (*http.Request, error) {
	file, err := os.Open(req.MultipartRequest.FilePath)
	if err != nil {
		fmt.Println("Failed to open file:", err)
		return nil, err
	}
	defer file.Close()

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	part, err := writer.CreateFormFile(req.MultipartRequest.FileParam, filepath.Base(req.MultipartRequest.FilePath))
	if err != nil {
		fmt.Println("Failed to create form file:", err)
		return nil, err
	}

	_, err = io.Copy(part, file)
	if err != nil {
		fmt.Println("Failed to copy file data:", err)
		return nil, err
	}

	err = writer.Close()
	if err != nil {
		fmt.Println("Failed to close multipart writer:", err)
		return nil, err
	}

	result, err := http.NewRequest("POST", req.constructUrl(), body)
	if err != nil {
		fmt.Println("Failed to create HTTP request:", err)
		return nil, err
	}
	//set header
	result.Header.Add("Content-Type", "multipart/form-data")
	result.Header.Set("Content-Type", writer.FormDataContentType())	
	result.Header.Set("accept", "application/json")
	fmt.Println(">> ", SimplyToJson(result.Header))

	return result, nil
}
