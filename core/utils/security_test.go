package utils

import "testing"

func TestDecrypt<PERSON>ith<PERSON>ey(t *testing.T) {
	qr := "e3YetCFXIhVvAevwO9s38FrZcO6f"

	type args struct {
		cryptoText string
		key        string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"qr-deals", args{cryptoText: qr, key: KEY_MEMBER_BARCODE}, ""},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := DecryptWithKey(tt.args.cryptoText, tt.args.key); got != tt.want {
				t.<PERSON>rf("DecryptWithKey() = %v, want %v", got, tt.want)
			}
		})
	}
}
