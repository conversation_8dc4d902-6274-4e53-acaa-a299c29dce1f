package utils

import (
	"bytes"
	"github.com/valyala/fasthttp"
	"net"
	"strings"
)

type ipRange struct {
	start net.IP
	end net.IP
}

// inRange - check to see if a given ip address is within a range given
func inRange(r ipRange, ipAddress net.IP) bool {
	// strcmp type byte comparison
	if bytes.Compare(ipAddress, r.start) >= 0 && bytes.Compare(ipAddress, r.end) < 0 {
		return true
	}
	return false
}

var privateRanges = []ipRange{
	ipRange{
		start: net.ParseIP("10.0.0.0"),
		end:   net.ParseIP("**************"),
	},
	ipRange{
		start: net.ParseIP("**********"),
		end:   net.ParseIP("***************"),
	},
	ipRange{
		start: net.ParseIP("**********"),
		end:   net.ParseIP("**************"),
	},
	ipRange{
		start: net.ParseIP("*********"),
		end:   net.ParseIP("***********"),
	},
	ipRange{
		start: net.ParseIP("***********"),
		end:   net.ParseIP("***************"),
	},
	ipRange{
		start: net.ParseIP("**********"),
		end:   net.ParseIP("**************"),
	},
}


// isPrivateSubnet - check to see if this ip is in a private subnet
func isPrivateSubnet(ipAddress net.IP) bool {
	// my use case is only concerned with ipv4 atm
	if ipCheck := ipAddress.To4(); ipCheck != nil {
		// iterate over all our ranges
		for _, r := range privateRanges {
			// check if this ip is in a private range
			if inRange(r, ipAddress){
				return true
			}
		}
	}
	return false
}

func GetIPAdress(ctx *fasthttp.RequestCtx) string {
	for _, h := range []string{"X-Forwarded-For", "X-Real-Ip"} {
		addresses := strings.Split(string(ctx.Request.Header.Peek(h)), ",")
		// march from right to left until we get a public address
		// that will be the address right before our proxy.
		for i := len(addresses) -1 ; i >= 0; i-- {
			ip := strings.TrimSpace(addresses[i])
			// header can contain spaces too, strip those out.
			realIP := net.ParseIP(ip)
			if !realIP.IsGlobalUnicast() || isPrivateSubnet(realIP) {
				// bad address, go to next
				continue
			}
			return ip
		}
	}
	return ""
}
