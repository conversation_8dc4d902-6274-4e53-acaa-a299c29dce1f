package utils

import (
	"fmt"
	"os"
)

func BaseUrl() string {
	url := ""
	switch os.Getenv("server") {
	case "production":
		url = "https://client.uniq.id"
	case "demo":
		url = "https://staging.uniq.id"
	case "development":
		url = "https://uniq-dev.tech"
	}
	return url
}

func GetTmpDir(child string) string {
	basePath := "temp/" + child
	_, err := os.Stat(basePath)
	if os.IsNotExist(err) {
		err = os.MkdirAll(basePath, os.ModePerm)
		if err != nil {
			fmt.Printf("-- Error -- cant not create dir '%v' : %v \n", basePath, err)
		}
	}
	return basePath
}
