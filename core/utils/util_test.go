package utils

import (
	"testing"
)

func TestHashPassword(t *testing.T) {
	str := `**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	type args struct {
		password string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{"test1", args{str}, "", false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := HashPassword(tt.args.password)
			if (err != nil) != tt.wantErr {
				t.Errorf("HashPassword() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("HashPassword() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestReplaceWithMap(t *testing.T) {
	original1 := "$name : xx, $age: 00"
	lang1 := map[string]string{
		"name": "Nama",
		"age":  "Umur",
	}
	translated1 := "Nama : xx, Umur: 00"

	type args struct {
		original    string
		mapReplacer map[string]string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"tet1", args{original: original1, mapReplacer: lang1}, translated1},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ReplaceWithMap(tt.args.original, "$", tt.args.mapReplacer); got != tt.want {
				t.Errorf("ReplaceWithMap() = %v, want %v", got, tt.want)
			}
		})
	}
}
