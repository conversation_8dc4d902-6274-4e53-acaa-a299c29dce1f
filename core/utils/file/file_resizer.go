package file

import (
	"encoding/base64"
	"fmt"
	"image"
	"image/jpeg"
	_ "image/jpeg"
	_ "image/png"
	"os"
	"strings"
)

func ResizeImageToSize(imagePath string, targetSizeKB int) error {
	// Validate if file exists
	if !Exists(imagePath) {
		return fmt.Errorf("image file not found: %s", imagePath)
	}

	// Open the input image file
	inputFile, err := os.Open(imagePath)
	if err != nil {
		return fmt.Errorf("error opening image file: %w", err)
	}
	defer inputFile.Close()

	data, err := ToBase64(imagePath)
	if err != nil {
		return err
	}
	reader := base64.NewDecoder(base64.StdEncoding, strings.NewReader(data))
	fmt.Println("get config....", imagePath)

	// config, format, err := image.DecodeConfig(reader)
	// if err != nil {
	// 	return err
	// }
	// fmt.Println("Width:", config.Width, "Height:", config.Height, "Format:", format)

	// Decode the image
	img, _, err := image.Decode(reader)
	if err != nil {
		return fmt.Errorf("error decoding image: %w", err)
	}

	// Initial quality (start with a high quality)
	quality := 90

	for {
		// Create a temporary output file
		tempFile, err := os.CreateTemp("", "resized_*.jpg")
		if err != nil {
			return fmt.Errorf("error creating temporary file: %w", err)
		}
		defer os.Remove(tempFile.Name()) // Ensure temp file is deleted

		// Encode the image with current quality
		err = jpeg.Encode(tempFile, img, &jpeg.Options{Quality: quality})
		if err != nil {
			return fmt.Errorf("error encoding image: %w", err)
		}

		// Get the size of the temporary file in KB
		fileInfo, err := tempFile.Stat()
		if err != nil {
			return fmt.Errorf("error getting file info: %w", err)
		}
		fileSizeKB := fileInfo.Size() / 1024
		fmt.Printf("file size %vkb, want: %vkb, quality: %v\n", fileSizeKB, targetSizeKB, quality)

		// Check if the size matches the target
		if fileSizeKB <= int64(targetSizeKB) || quality < 10  {
			// Rename the temporary file to the original file path
			err = os.Rename(tempFile.Name(), imagePath)
			if err != nil {
				return fmt.Errorf("error renaming file: %v", err)
			}
			
			fmt.Printf("Image resized to %d KB\n", fileSizeKB)
			return nil
		}

		// Decrease quality for the next iteration
		quality -= 5		
	}
}
