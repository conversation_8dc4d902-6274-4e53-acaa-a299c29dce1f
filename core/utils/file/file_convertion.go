package file

import (
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/Sebastiaan<PERSON>/go-wkhtmltopdf"
)

func HtmlToPdf(source string) (string, error) {
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		return "", err
	}

	f, err := os.Open(source)
	if f != nil {
		defer f.Close()
	}
	if err != nil {
		return "", err
	}

	defer f.Close()
	pdfg.AddPage(wkhtmltopdf.NewPageReader(f))

	pdfg.Orientation.Set(wkhtmltopdf.OrientationPortrait)
	pdfg.Dpi.Set(300)

	err = pdfg.Create()
	if err != nil {
		return "", err
	}

	fileName := filepath.Base(source)
	fileName = strings.TrimSuffix(fileName, filepath.Ext(fileName))
	fileDetionation := fmt.Sprintf("%s.pdf", fileName)
	err = pdfg.WriteFile(fileDetionation)
	if err != nil {
		return "", err
	}

	fmt.Println("pdf created: ", fileDetionation)
	return fileDetionation, nil
}

func ToBase64(filePath string) (string, error) {
	// Read the file content
	data, err := os.ReadFile(filePath)
	if err != nil {
		return "", fmt.Errorf("error reading file: %w", err)
	}

	// Encode the data as base64
	encodedData := base64.StdEncoding.EncodeToString(data)

	return encodedData, nil
}
