package file

import (
	"fmt"
	"os"
)

func Move(sourcePath, destinationPath string) error {
	// ... (source file check) ...

	// Copy the file
	data, err := os.ReadFile(sourcePath)
	if err != nil {
		return fmt.Errorf("error reading source file: %w", err)
	}
	err = os.Write<PERSON>ile(destinationPath, data, 0644) // 0644: file permissions
	if err != nil {
		return fmt.Errorf("error writing destination file: %w", err)
	}

	// Delete the source file
	err = os.Remove(sourcePath)
	if err != nil {
		return fmt.Errorf("error deleting source file: %w", err)
	}

	fmt.Printf("File moved from %s to %s\n", sourcePath, destinationPath)
	return nil
}
