package file

import "testing"

func TestResizeImageToSize(t *testing.T) {
	type args struct {
		imagePath    string
		targetSizeKB int
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{"test", args{imagePath:"/tmp/resized_1487437562.jpg", targetSizeKB: 500 }, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ResizeImageToSize(tt.args.imagePath, tt.args.targetSizeKB); (err != nil) != tt.wantErr {
				t.Errorf("ResizeImageToSize() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
