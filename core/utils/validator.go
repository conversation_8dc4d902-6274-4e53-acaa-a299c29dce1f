package utils

import (
	"fmt"
	"os"
	"regexp"

	"github.com/valyala/fasthttp"
)

func IsValidPostInput(ctx *fasthttp.RequestCtx, params ...string) (bool, string) {
	status := true
	message := ""
	for _, param := range params {
		if ctx.PostArgs().Peek(param) == nil {
			status = false
			message = fmt.Sprintf("%s is required", param)
			break
		}
	}
	return status, message
}

func MustHaveFile(path string) string {
	if os.Getenv("server") == "localhost" || os.Getenv("ENV") == "localhost" {
		return path
	}

	info, err := os.Stat(path)
	if os.IsNotExist(err) || info.IsDir() {
		panic(fmt.Sprintf("this file is not exist or directory : %s", path))
	}
	return path
}

func MustHaveFiles(paths ...string) {
	for _, path := range paths {
		info, err := os.Stat(path)
		if os.IsNotExist(err) || info.IsDir() {
			panic(fmt.Sprintf("this file is not exist or directory : %s", path))
		}
	}
}

func MustHaveEnv(keys ...string) {
	unInitializedEnv := ""
	for _, key := range keys {
		if os.Getenv(key) == "" {
			unInitializedEnv += key + ", "
		}
	}
	if unInitializedEnv != "" {
		panic(fmt.Sprintf("these env must be initialized : %s", unInitializedEnv))
	}
}

func MustGetEnv(key string) string {
	result := os.Getenv(key)
	if result == "" {
		fmt.Println("args: ", os.Args)
		panic(fmt.Sprintf("env '%s' is not initialized", key))
	}
	return result
}

func SetFromEnv(key string, data *string) {
	if os.Getenv(key) == "" {
		panic(fmt.Sprintf("env '%s' is not initialized", key))
	} else {
		*data = os.Getenv(key)
	}
}

func IsValidEmail(email string) bool {
	regex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return regex.MatchString(email)
}
