package utils

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

func ParseCurrencyToInt(input string) int {
	// Use regex to find and replace comma/dot followed by 3 digits
	re := regexp.MustCompile(`([.,])(\d{3})`)
	fmt.Printf("before: %v ", input)
	input = re.ReplaceAllString(input, "$2")
	fmt.Printf("after: %v \n", input)

	re = regexp.MustCompile(`([.,])(\d{2})`)
	fmt.Printf("2 - before: %v ", input)
	input = re.ReplaceAllString(input, "$1")
	fmt.Printf("after: %v \n", input)
	input = strings.TrimRight(input, ",")
	input = strings.TrimRight(input, ".")

	num, err := strconv.Atoi(input)
	if err != nil {
		fmt.Println("Error converting string to integer:", err)
		return 0
	}
	return num
}

