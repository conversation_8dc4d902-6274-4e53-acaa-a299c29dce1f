package utils

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"strings"
)

type Emoji struct {
	Key        string `json:"key"`
	Value      string `json:"value"`
	Descriptor string `json:"descriptor"`
}

// Emojis - Map of Emoji Runes as Hex keys to their description
var Emojis map[string]Emoji

// SearchResult - Occurence of an emoji in a string
type SearchResult struct {
	Match       interface{}
	Occurrences int
	Locations   [][]int
}

// SearchResults - The result of a search
type SearchResults []SearchResult

// IndexOf - Check to see if search results contains a specific element
func (results SearchResults) IndexOf(result interface{}) int {
	for i, r := range results {
		if r.Match == result {
			return i
		}
	}

	return -1
}

func initEmoticon() error {
	// Work out where we are in relation to the caller
	//_, filename, _, ok := runtime.Caller(0)
	//if !ok {
	//	return errors.New("No caller information")
	//}

	// Open the Emoji definition JSON and Unmarshal into map
	//jsonFile, err := os.Open(path.Dir(filename) + "/data/emoji.json")
	jsonFile, err := os.Open("config/data/emoji.json")
	defer jsonFile.Close()
	if err != nil {
		fmt.Println(err)
		return err
	}

	byteValue, e := ioutil.ReadAll(jsonFile)
	if e != nil {
		fmt.Println("failed to load emoticon file")
		return err
	}

	json.Unmarshal(byteValue, &Emojis)
	return nil
}

func RemoveAllEmoji(input string) string {
	err := initEmoticon()
	if err != nil {
		return input
	}
	// Find all the emojis in this string
	matches := FindAll(input)

	for _, item := range matches {
		emo := item.Match.(Emoji)
		rs := []rune(emo.Value)
		for _, r := range rs {
			input = strings.Replace(input, string([]rune{r}), "",-1)
		}
	}

	// Remove and trim and left over whitespace
	return strings.TrimSpace(strings.Join(strings.Fields(input), " "))
	//return input
}

func FindAll(input string) (detectedEmojis SearchResults) {

	// Convert our string to UTF runes
	runes := []rune(input)

	// Any potential modifiers such as a skin tone/gender
	detectedModifiers := map[int]bool{}

	// Loop over each "word" in the string
	for index, r := range runes {

		// If this index has been flaged as a modifier we do
		// not want to process it again
		if detectedModifiers[index] {
			continue
		}

		// Grab the initial hex value of this run
		hexKey := RunesToHexKey([]rune{r})

		// Ignore any basic runes, we'll get funny partials
		// that we dont care about
		if len(hexKey) < 4 {
			continue
		}

		previousKey := hexKey
		potentialMatches := Emojis
		nextIndex := index + 1

		for {
			// Search the Emoji definitions map to see if we have
			// any matching results
			potentialMatches = findEmoji(hexKey, potentialMatches)

			// We found a definitive match
			if len(potentialMatches) == 1 {
				break
			} else if len(potentialMatches) == 0 {
				// We didnt find anything, so we'll check if its a single rune emoji
				// Reset to original hexKey
				if _, match := Emojis[previousKey]; match {
					potentialMatches[previousKey] = Emojis[previousKey]
				}

				// Definately no modifiers
				detectedModifiers = map[int]bool{}

				break
			} else {
				// Have we hit the last rune? If so we'll stop
				if nextIndex == len(runes) {
					// We need to return the match for this current key
					potentialMatches = map[string]Emoji{
						hexKey: Emojis[hexKey],
					}
					break
				}

				// We have more than one potential match so we'll add the
				// next UTF rune to the key and search again!
				previousKey = hexKey
				hexKey = hexKey + "-" + RunesToHexKey([]rune{runes[nextIndex]})
				detectedModifiers[nextIndex] = true
				nextIndex++
			}
		}

		// Loop over potential matches and ensure we're not counting partials
		for key, e := range potentialMatches {
			if _, match := Emojis[key]; match {

				// How many runes does this emoji use
				emojiRuneLength := len(strings.Split(e.Key, "-"))

				// Have we already accounted for this match?
				if i := detectedEmojis.IndexOf(e); i != -1 {
					detectedEmojis[i].Occurrences++
					detectedEmojis[i].Locations = append(detectedEmojis[i].Locations, []int{index, index + emojiRuneLength})
				} else {
					detectedEmojis = append(detectedEmojis, SearchResult{
						Match:       e,
						Occurrences: 1,
						Locations: [][]int{
							[]int{index, index + emojiRuneLength},
						},
					})
				}
			}
		}
	}

	// Return a map of Emojis and their counts
	return detectedEmojis
}

// Search an array of emoji definitions for a key with a partial match
func findEmoji(term string, list map[string]Emoji) (results map[string]Emoji) {
	results = map[string]Emoji{}

	// Look for anything that has
	for key, value := range list {
		if strings.Index(key, term) == 0 {
			results[key] = value
		}
	}
	return
}

// RunesToHexKey - Convert a slice of runes to hex string representation of their Unicode Code Point value
func RunesToHexKey(runes []rune) (output string) {
	// Build a slice of hex representations of each rune
	hexParts := []string{}
	for _, rune := range runes {
		hexParts = append(hexParts, fmt.Sprintf("%X", rune))
	}

	// Join the hex strings with a hypen - this is the key used in the emojis map
	output = strings.Join(hexParts, "-")
	return
}