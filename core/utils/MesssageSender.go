package utils

import (
	"fmt"
	"log"
	"net/http"
	"net/smtp"
	net "net/url"
	"os"
	"strings"
)

const (
	MEDIA_WA    = "whatsapp"
	MEDIA_EMAIL = "email"
)

type Messenger struct {
	Title    string
	Message  string
	Media    string
	Receiver string
}

func SendMessage(msg Messenger) {
	//_, err := db.Insert("scheduled_message", map[string]interface{}{
	//	"title":    msg.Title,
	//	"message":  msg.Message,
	//	"media":    msg.Media,
	//	"receiver": msg.Receiver,
	//})
	//
	//if err != nil {
	//	SendMessageToSlack(fmt.Sprintf("[%s] Send to whatsapp error : %v", os.Getenv("server"), err))
	//}
}

//if adminId is not 0, the message will send using admin WhatsApp Number
func SendMessageToGateWay(message, phone string, adminId int) error {
	request := HttpRequest{}
	request.Url = fmt.Sprintf("%s/send/message", os.Getenv("messager_gateway"))
	request.Header = map[string]interface{}{
		"Authorization": os.Getenv("messenger_auth"),
	}
	request.Method = "POST"
	request.PostRequest.Body = map[string]interface{}{
		"message": message,
		"title":   "",
		"recipient": []map[string]interface{}{
			{
				"media":    "whatsapp",
				"address":  phone,
				"admin_id": adminId,
			},
		},
	}

	resp, err := request.Execute()
	if err != nil {
		SendMessageToSlack(fmt.Sprintf("[%s] Send to whatsapp error : %v", os.Getenv("server"), err))
	} else {
		fmt.Printf("send wa resp : %s \n", string(resp))
	}
	return err
}

func SendWhatsAppMessage(message string, msgType string, id string, phone string) error {
	//if msgType == "group" && phone == "6287838362747-1495704774" {
	//	SendWhatsAppMessage(message, "employee", "92", "6285742257881")
	//}
	if os.Getenv("server") == "production" {
		if msgType == "secure" {
			phone = Decrypt(phone)
		}
		if strings.HasPrefix(phone, "08") {
			phone = "62" + phone[1:]
		}
		//_, err := ExeCommand("yowsup-cli", "demos", "-d", "-s", phone, message, "-c","/home/<USER>/yowsup/config")
		//return err

		//SendWhatsApp(phone, message)

		data := net.Values{}
		data.Set("phone", phone)
		data.Set("message", message)

		res, err := http.PostForm("http://127.0.0.1:1719/send/wa", data)
		if err != nil {
			fmt.Println("Send WA to Local Error ", err)
			return err
		}

		defer res.Body.Close()
		//body, err := ioutil.ReadAll(res.Body)
		//if err != nil {
		//	fmt.Println("Read body response WA error, ", err)
		//	return err
		//}

		//fmt.Println("Send WA Body : ", string(body))

		return err
	} else {
		params := net.Values{}
		params.Add("message", message)
		params.Add("type", msgType)
		params.Add("id", id)
		params.Add("phone", phone)

		client := &http.Client{}
		req, err := http.NewRequest("POST", "http://go.uniq.id/messager/wa", strings.NewReader(params.Encode()))

		if err != nil {
			fmt.Println(err)
			return err
		}

		req.Header.Add("Authorization", "Bearer N5t9JNaCW4HfwhG9yXaQL0mdtx1M3BOX")
		req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

		resp, err := client.Do(req)
		if err != nil {
			fmt.Println("Error - ", err)
			return err
		}

		defer resp.Body.Close()
		return err
	}

	//post := net.Values{}
	//post.Set("token", "1686f3e0281936877077d6ce89e577b35b07dcbbbb473")
	//post.Set("uid", "62895359021068")
	//post.Set("to", phone)
	//post.Set("custom_uid", strconv.FormatInt(time.Now().Unix(), 10))
	//post.Set("text", message)
	//
	//_, err := http.PostForm("https://www.waboxapp.com/api/send/chat", post)
	//if err != nil{
	//	fmt.Println("Send report to WA Error ", err)
	//}
}

func SendEmail(to []string, message string) {
	// Set up authentication information.
	auth := smtp.PlainAuth("", "<EMAIL>", "makestories", "smtp.zoho.com")

	// Connect to the server, authenticate, set the sender and recipient,
	// and send the email all in one step.
	msg := []byte("To: <EMAIL>\r\n" +
		"Subject: UNIQ Server Error\r\n" +
		"\r\n" +
		message + "\r\n")
	err := smtp.SendMail("smtp.zoho.com:587", auth, "<EMAIL>", to, msg)
	if err != nil {
		log.Fatal(err)
	}
}

func SendEmailZoho(fromAddress, toAddress, subject, content string) {
	///send/email
	request := HttpRequest{}
	request.Url = fmt.Sprintf("%s/send/message", os.Getenv("messager_gateway"))
	request.Method = "POST"
	request.PostRequest.Body = map[string]interface{}{
		"message": content,
		"title":   subject,
		"recipient": []map[string]interface{}{
			{
				"media":        "email",
				"address":      toAddress,
				"from_address": fromAddress,
			},
		},
	}

	resp, err := request.Execute()
	if err != nil {
		SendMessageToSlack(fmt.Sprintf("[%s] Send to email error : %v", os.Getenv("server"), err))
	} else {
		fmt.Printf("send email resp : %s \n", string(resp))
	}

	//url := "https://mail.zoho.com/api/accounts/5959564000000008002/messages"
	//fmt.Println("URL:>", url)
	//
	//jsonMap := map[string]string{
	//	"fromAddress": "<EMAIL>",
	//	"toAddress":   toAddress,
	//	"subject":     subject,
	//	"content":     content,
	//}
	//
	//json, err := json.Marshal(jsonMap)
	//if err != nil {
	//	fmt.Println("Parse map to json error : ", err)
	//}
	//
	//var jsonStr = []byte(json)
	//req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	//req.Header.Set("Authorization", "6269fea601cf0fe94cde7b67fd849499")
	//req.Header.Set("Content-Type", "application/json")
	//
	//client := &http.Client{}
	//resp, err := client.Do(req)
	//if err != nil {
	//	panic(err)
	//}
	//defer resp.Body.Close()
	//
	//fmt.Println("Send Email to "+toAddress+" response Status:", resp.Status)

	//fmt.Println("response Headers:", resp.Header)
	//body, _ := ioutil.ReadAll(resp.Body)
	//fmt.Println("response Body:", string(body))
}
