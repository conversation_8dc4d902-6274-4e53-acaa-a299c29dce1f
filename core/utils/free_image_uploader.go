package utils

import (
	"encoding/json"
	"fmt"
	"os"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/models"
)

type FreeImageRequest struct {
	models.ImageUploadRequest
}

func (r FreeImageRequest) Upload() (string, error) {
	fmt.Println("FreeImageRequest, upload sourceType: ", r.SourceType)
	if r.SourceType == models.ImageSourceBase64 {
		path := fmt.Sprintf("%v.jpg", time.Now().UnixMilli())
		err := DownloadFileBase64(r.Source, path)
		if err != nil {
			fmt.Println("download base64 err: ", err)
			return "", err
		}
		r.Source = path
	}

	defer os.Remove(r.Source)
	// 6d207e02198a847aa98d0a2a901485a5
	// 6d207e02198a847aa98d0a2a901485a5

	req := HttpRequest{
		Method: "POST",
		Url:    "https://freeimage.host/api/1/upload",
		Query: map[string]string{
			"key": "6d207e02198a847aa98d0a2a901485a5", //6d207e02198a847aa98d0a2a901485a5
		},
		MultipartRequest: MultipartRequest{
			FilePath:  r.Source,
			FileParam: "source",
		},
	}

	resp, err := req.Execute()
	if err != nil {
		return "", err
	}

	var result models.FreeImageResponse
	err = json.Unmarshal(resp, &result)
	if err != nil {
		return "", err
	}

	return result.Image.URL, nil
}
