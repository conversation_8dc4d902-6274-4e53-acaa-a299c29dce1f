package utils

import (
	"fmt"
	"time"
)

func MillisAdd(day, month, year int) int64 {
	hour := time.Now().Hour()
	timeAdd := time.Now().AddDate(year, month, day).Add(time.Duration(hour*-1) * time.Hour)
	result := timeAdd.Unix()
	fmt.Println(">>", timeAdd.Format("2006-01-02 15:04:05"))
	return result * 1000
}

func GetStartOfDayMillis(millis int64, timeZoneOffsetMinutes int) int64 {
	// Convert milliseconds to time.Time object
	t := time.Unix(0, millis*int64(time.Millisecond)).UTC().Add(time.Duration(timeZoneOffsetMinutes/60/60) * time.Hour)
	t = time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.UTC)
	return t.UnixMilli() - int64(timeZoneOffsetMinutes*1000)
}

func GetEndOfDayMillis(millis int64, timeZoneOffsetMinutes int) int64 {
	// t := time.Unix(0, millis*int64(time.Millisecond))
	// loc := time.FixedZone("UTC+offset", timeZoneOffsetMinutes*60)
	// t = time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 999999999, loc)
	// return t.UnixNano() / int64(time.Millisecond)

	t := time.Unix(0, millis*int64(time.Millisecond)).UTC().Add(time.Duration(timeZoneOffsetMinutes/60/60) * time.Hour)
	t = time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 999999999, time.UTC)
	return t.UnixMilli() - int64(timeZoneOffsetMinutes*1000)
}
