package utils

import (
	"encoding/json"
	"fmt"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"strings"
)

const SHORT_URL_UNIQ string = "https://y.uniq.id/"

type ShortUrlModel struct {
	LongUrl string
	Title   string
	Tags    []string
}

type ShortUrlResponse struct {
	Status   bool   `json:"status"`
	Message  string `json:"message"`
	ShortURL string `json:"short_url"`
}

func ShortUrl(model ShortUrlModel) string {
	result := ShortUniq(model)
	if result == "" {
		result = ShortBitLy(model)
	}
	return result
}

func ShortUniq(model ShortUrlModel) string {
	request := HttpRequest{}
	request.Method = "POST"
	request.Url = SHORT_URL_UNIQ + "create"
	request.Header = map[string]interface{}{
		"Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJzaG9yLXVybCIsIm5hbWUiOiJVTklRLURFViIsImlhdCI6MTU4MTY1MDU1MDY0Mn0.OKztNL736gbCdue72GBr4tzXLuhns4dBpjlUfuK_Jbc",
		"Content-Type":  "application/x-www-form-urlencoded",
	}
	request.PostRequest.Form = map[string]string{
		"url": model.LongUrl,
		"tag": strings.Join(model.Tags, ","),
	}

	var short ShortUrlResponse
	resp, err := request.Execute()
	if err == nil {
		fmt.Println("short with uniq resp : ", string(resp))
		err = json.Unmarshal(resp, &short)
		if err != nil {
			fmt.Println("parsing json at short url with uniq error - ", err)
		}
	} else {
		fmt.Println("short url with uniq error - ", err)
	}
	if short.ShortURL != "" {
		return SHORT_URL_UNIQ + short.ShortURL
	}

	return short.ShortURL
}

func ShortBitLy(model ShortUrlModel) string {
	request := HttpRequest{}
	request.Method = "POST"
	request.Url = "https://api-ssl.bitly.com/v4/bitlinks"
	request.Header = map[string]interface{}{
		"Authorization": "Bearer c9c7e0987e65803245b34c25a71e1acb06c86a18",
		"Content-Type":  "application/json",
	}
	request.PostRequest.Body = map[string]interface{}{
		"title":    model.Title,
		"long_url": model.LongUrl,
		"tags":     model.Tags,
	}
	var bitly models.BitLy
	resp, err := request.Execute()
	if err == nil {
		err = json.Unmarshal(resp, &bitly)
		if err != nil {
			fmt.Println("parsing json at short url with bitly error - ", err)
		}
	} else {
		fmt.Println("short url with bitly error - ", err)
	}

	return bitly.Link
}
