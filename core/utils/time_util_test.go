package utils

import (
	"fmt"
	"testing"
	"time"
)

func TestGetStartOfDayMillis(t *testing.T) {
	start := GetStartOfDayMillis(1727114400000, 25200)
	fmt.Println("----", start, time.Unix(0, start*int64(time.Millisecond)).Format("2006/01/02 15:04:05"))
}

func TestMillisAdd(t *testing.T) {
	type args struct {
		day   int
		month int
		year  int
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{"test1", args{-1, 0, 0}, 1670518800000},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MillisAdd(tt.args.day, tt.args.month, tt.args.year); got != tt.want {
				t.Errorf("MillisAdd() = %v, want %v", got, tt.want)
			}
		})
	}
}
