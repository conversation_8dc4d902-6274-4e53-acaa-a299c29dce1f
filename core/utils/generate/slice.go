package generate

import (
	"math/rand"
	"reflect"
)

func RandomArray[T any](length int) []T {
	// rand.Seed(time.Now().UnixNano())

	// Get the type of the element to be generated
	elemType := reflect.TypeOf((*T)(nil)).Elem()

	// Create a slice of the specified type
	result := reflect.MakeSlice(reflect.SliceOf(elemType), length, length)

	// Generate random values based on the type
	for i := 0; i < length; i++ {
		switch elemType.Kind() {
		case reflect.Int:
			result.Index(i).SetInt(rand.Int63())
		case reflect.Int8:
			result.Index(i).SetInt(int64(rand.Intn(256))) // Range for int8
		case reflect.Int16:
			result.Index(i).SetInt(int64(rand.Int31n(65536))) // Range for int16
		case reflect.Int32:
			result.Index(i).SetInt(int64(rand.Int31()))
		case reflect.Int64:
			result.Index(i).SetInt(rand.Int63())
		case reflect.Float32:
			result.Index(i).SetFloat(float64(rand.Float32()))
		case reflect.Float64:
			result.Index(i).SetFloat(rand.Float64())
		case reflect.String:
			result.Index(i).SetString(RandomString(1)) // Generate random string
		default:
			panic("Unsupported type for random array generation")
		}
	}

	// Convert the reflected slice to the correct type
	return result.Interface().([]T)
}
