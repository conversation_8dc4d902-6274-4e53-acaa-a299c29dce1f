package generate

import (
	"math/rand"
	"strconv"
	"strings"
	"time"
)

// generating sales id from give timestamp, if 0 (zero) will automatically using current timestamp
func SalesId(timestamp int64) string {
	if timestamp <= 0 {
		timestamp = time.Now().UnixMilli()
	}
	words := []string{"M", "N", "K", "O", "H", "S", "N", "I", "A", "L"}
	timeStr := strconv.Itoa(int(timestamp))
	timeArr := strings.Split(timeStr, "")
	changes := make([]int, 0)
	max := len(timeArr)
	min := 1
	for i := 0; i < len(timeArr)/2; i++ {
		index := rand.Intn(max-min+1) + min
		if !containsInt(changes, index) {
			changes = append(changes, index)
		}
	}

	var value strings.Builder
	for i, char := range timeArr {
		if char != "" {
			if containsInt(changes, i) {
				value.WriteString(words[int(char[0]-'0')])
			} else {
				value.WriteString(char)
			}
		}
	}

	return value.String()
}

func SalesIdFrom(timeStr string) string {
	words := []string{"M", "N", "K", "O", "H", "S", "N", "I", "A", "L"}
	timeArr := strings.Split(timeStr, "")
	changes := make([]int, 0)
	max := len(timeArr)
	min := 1
	for i := 0; i < len(timeArr)/2; i++ {
		index := rand.Intn(max-min+1) + min
		if !containsInt(changes, index) {
			changes = append(changes, index)
		}
	}

	var value strings.Builder
	for i, char := range timeArr {
		if char != "" {
			if isNumber(char) && containsInt(changes, i) {
				value.WriteString(words[int(char[0]-'0')])
			} else {
				value.WriteString(char)
			}
		}
	}

	return value.String()
}

func DisplayNota(adminID, outletID, salesCount int) string {
	nota := strings.Builder{}
	id := outletID + adminID

	words := []string{"M", "N", "K", "O", "H", "S", "N", "I", "A", "L"}
	digits := strconv.Itoa(id)
	for _, digit := range digits {
		if isNumber(string(digit)) {
			index, _ := strconv.Atoi(string(digit))
			nota.WriteString(words[index])
		}
	}

	now := time.Now().Add(7 * time.Hour)
	nota.WriteString(strconv.Itoa(now.Year()))
	nota.WriteString(now.Format("01"))
	nota.WriteString(now.Format("02"))
	nota.WriteString(strconv.Itoa(salesCount + 1))

	return nota.String()
}

func containsInt(s []int, e int) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

func isNumber(element string) bool {
	return strings.ContainsAny(element, "0123456789")
}
