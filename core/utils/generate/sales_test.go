package generate

import (
	"fmt"
	"testing"
	"time"
)

func TestGenerateSalesId(t *testing.T) {
	// Seed the random number generator for predictable results in tests
	// rand.Seed(42)

	type args struct {
		timestamp int64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Custom Timestamp 1",
			args: args{timestamp: 1727161311654},
			want: "17K7N6N31165H", // Calculate expected value here
		},
		{
			name: "Custom Timestamp 2",
			args: args{timestamp: 1727161311654},
			want: "172716NON16S4", // Calculate expected value here
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SalesId(tt.args.timestamp); got != tt.want {
				t.Errorf("generateSalesId() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGenerateSalesIdFrom(t *testing.T) {
	// Seed the random number generator for predictable results in tests
	// rand.Seed(42)

	type args struct {
		timestamp string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "all integer",
			args: args{timestamp: "1727161311654"},
			want: "17K7N6N31165H", // Calculate expected value here
		},
		{
			name: "all alphabet",
			args: args{timestamp: "CLMKGHABFHEJRT"},
			want: "CLMKGHABFHEJRT", // Calculate expected value here
		},
		{
			name: "mix",
			args: args{timestamp: "C1791652037786"},
			want: "172716NON16S4", // Calculate expected value here
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SalesIdFrom(tt.args.timestamp); got != tt.want {
				t.Errorf("generateSalesId() = %v, want %v, param: %v", got, tt.want, tt.args.timestamp)
			}
		})
	}
}

func TestCode(t *testing.T) {
	x := "1"
	fmt.Println("----", x[0], time.Now().UnixMilli())
}
