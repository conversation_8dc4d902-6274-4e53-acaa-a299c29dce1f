package utils

import "os"

const KEY_MEMBER_BARCODE string = "buvaZZvR2xKN7rCeuJ4btZ7yLw72vFYH"
const KEY_PROMOTION_BARCODE string = "SjYfD9n4cygvrPqynPLFSdTPLVJGQ77V"
const KEY_FEEDBACK string = "579gt6Q7Fq4nXJXKTTk0cNgf8Wn7zp0j"

const BEHAVE_ORDER_PREFIX string = "BHV"
const ROLE_OWNER = "master"
const ROLE_EMPLOYEE = "slave"

const PROMO_FREE int = 6

const ERR_SQL_DUPLICATE_PRIMARY = 1062
const ERR_SQL_LOCK_TIMEOUT = 1205
const ERR_SQL_DEADLOCK = 1213

const (
	KEY_BILLING_AUTH = "5c7aTznVTLtQFmfzBT5M8c5h6v5pKCk4"
)

func FirebaseDynamicLink() string {
	if link := os.Getenv("FIREBASE_DYNAMIC_LINK"); link != "" {
		return link
	}

	env := os.Getenv("server")
	if env == "demo" || env == "staging" || env == "production" {
		return "https://uniqapps.page.link"
	}
	return "https://uniqcrm.page.link"
}
