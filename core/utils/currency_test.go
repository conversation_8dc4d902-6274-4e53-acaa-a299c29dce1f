package utils

import "testing"

func TestParseCurrencyToInt(t *testing.T) {
	type args struct {
		input string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{"Valid Integer", args{"1234"}, 1234},
		{"Integer with Comma", args{"1,234"}, 1234},
		{"Integer with Dot", args{"1.234"}, 1234},
		{"Integer with Many Dots", args{"1.234.567"}, 1234567},
		{"Integer with Dot and comma", args{"1.234,00"}, 1234},
		{"Integer with Comma and 3+ Digits", args{"123,456"}, 123456},
		{"Integer with Dot and 3+ Digits", args{"123.456"}, 123456},
		{"Integer with Comma and 2 Digits", args{"500,00"}, 500},
		{"Integer with Dot and 2 Digits", args{"500.00"}, 500},
		{"Negative Integer", args{"-1234"}, -1234},
		{"Negative Integer with Comma", args{"-1,234"}, -1234},
		{"Negative Integer with Dot", args{"-1.234"}, -1234},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ParseCurrencyToInt(tt.args.input); got != tt.want {
				t.Errorf("ParseCurrencyToInt() = %v, want %v", got, tt.want)
			}
		})
	}
}

