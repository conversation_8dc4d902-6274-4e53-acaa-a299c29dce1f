package cast

import (
	"fmt"
	"reflect"
	"testing"

	"gitlab.com/uniqdev/backend/api-pos/core/utils"
)

type MyStruct struct {
	Name string
}

func TestConvertPointer(t *testing.T) {
	data1 := []MyStruct{
		{"<PERSON>"},
		{"<PERSON>"},
		{"<PERSON>"},
	}

	data2 := ToPointerSlice(data1).([]*MyStruct)
	fmt.Println("data --> ", data2[0])
	fmt.Printf("data1 : %p ", data1)
	fmt.Printf("data2 : %p ", data2)
}

func TestSetFieldPointer(t *testing.T) {
	var results []*MyStruct
	setModelSlice(&results)
	if len(results) == 0 || results == nil {
		t.Error("results should not be empty")
		return
	}
	fmt.Println(utils.SimplyToJson(results))
	fmt.Println("---- finish -----")
}

func TestSetFieldNonPointer(t *testing.T) {
	var results []MyStruct
	setModelSlice(&results)
	if len(results) == 0 || results == nil {
		t.Error("results should not be empty")
		return
	}
	fmt.Println(utils.SimplyToJson(results))
	fmt.Println("---- finish -----")
}

func setModelSlice(model interface{}) {
	if reflect.TypeOf(model).Kind() != reflect.Ptr {
		fmt.Println("model should be pointer")
		return
	}

	isModelSlice := reflect.ValueOf(model).Elem().Kind() == reflect.Slice
	if !isModelSlice {
		fmt.Println("model is not slice")
		return
	}

	// Check if model is a slice of pointers.
	// modelValue := reflect.ValueOf(model).Elem()
	modelValue := reflect.Indirect(reflect.ValueOf(model))
	if modelValue.Kind() != reflect.Slice {
		fmt.Println("model should be a slice")
		return
	}

	// Create a new instance of the element type of the slice.
	structType := modelValue.Type().Elem()
	newStruct := reflect.New(structType).Elem()

	// structField := reflect.TypeOf(model).Elem()
	// structArray := reflect.ValueOf(model).Elem()

	columns := []string{"Name"}

	// newStruct := reflect.New(structField.Elem()).Elem()
	for i, col := range columns {
		SetField(newStruct, col, fmt.Sprintf("test%d", i))
	}
	// structArray.Set(reflect.Append(structArray, newStruct))
	modelValue.Set(reflect.Append(modelValue, newStruct))
}

func TestSetField(t *testing.T) {
	var result MyStruct
	var result2 *MyStruct

	type args struct {
		m      interface{}
		key    string
		value  interface{}
		result string
	}
	tests := []struct {
		name string
		args args
	}{
		{"non-pointer", args{m: &result, key: "Name", value: "uniq", result: result.Name}},
		{"with-pointer", args{m: &result2, key: "Name", value: "uniq", result: ""}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SetField(tt.args.m, tt.args.key, tt.args.value)
			fmt.Println(">>", tt.name, ":::", utils.SimplyToJson(tt.args.m))
		})
	}

}

type TestCustomer struct {
	Id   int     `json:"id,omitempty"`
	Name string  `json:"name,omitempty"`
	Age  int     `json:"age,omitempty"`
	High float32 `json:"high,omitempty"`
}

func TestSetFieldArrayStruct(t *testing.T) {
	var result []TestCustomer
	RunSetField(&result)

	fmt.Println(">> ", utils.SimplyToJson(result))
}

func RunSetField(model interface{}) {
	n := 10
	data := make([]map[string]interface{}, 0)
	for i := 0; i < n; i++ {
		data = append(data, map[string]interface{}{
			"id":   i,
			"name": fmt.Sprintf("account-%d", i),
			"age":  i,
			"high": float32(i) * 1.2,
		})
	}

	structField := reflect.TypeOf(model).Elem()
	structArray := reflect.ValueOf(model).Elem()

	for _, columns := range data {
		newStruct := reflect.New(structField.Elem()).Elem()
		for k, v := range columns {
			SetField(newStruct, k, v)
		}
		structArray.Set(reflect.Append(structArray, newStruct))
	}

}

// go test -bench . -benchmem -cpuprofile prof.cpu
// go tool pprof
func BenchmarkSetField(b *testing.B) {
	for i := 0; i < b.N; i++ {
		var result []TestCustomer
		RunSetField(&result)
	}
}
