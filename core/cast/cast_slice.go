package cast

import "reflect"

func ToPointer<PERSON>lice(data interface{}) interface{} {
	// Check if data is a slice.
	dataValue := reflect.ValueOf(data)
	if dataValue.Kind() != reflect.Slice {
		return nil
	}

	// Get the type of elements in the slice.
	elemType := dataValue.Type().Elem()

	// Create a new slice for pointers to elements.
	pointerSlice := reflect.MakeSlice(reflect.SliceOf(reflect.PtrTo(elemType)), dataValue.Len(), dataValue.Cap())

	// Iterate over the elements and convert them to pointers.
	for i := 0; i < dataValue.Len(); i++ {
		element := dataValue.Index(i)
		pointer := element.Addr()
		pointerSlice.Index(i).Set(pointer)
	}

	// Convert the result back to an interface.
	result := pointerSlice.Interface()
	return result
}
