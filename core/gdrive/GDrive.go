package gdrive

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/core/db"
	google2 "gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"golang.org/x/net/context"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/drive/v3"
	"google.golang.org/api/option"
)

const (
	// If modifying these scopes, delete your previously saved token.json.
	// See https://developers.google.com/identity/protocols/oauth2/scopes
	tokenFile                  = "config/credentials/gdrive_token.json"
	gdriveClientCredentialFile = "config/credentials/gdrive_client_credential.json"
)

type GDriveUploader struct {
	OriginPath      string
	DestinationPath string
	ParentIndex     int // [OPTIONAL] --> INDEX is starting from 0
	SubParentIndex  int //[OPTIONAL] --> index must be bigger than ParentIndex, and must not be 0
	SubParentId     string
}

func GetDriveClient() (*http.Client, error) {
	fmt.Println("getting gdrive client...")
	b, err := os.ReadFile(gdriveClientCredentialFile)
	if log.IfError(err) {
		return nil, err
	}

	// log.Info("starting gdrive client...")
	config, err := google.ConfigFromJSON(b, drive.DriveScope)
	if log.IfError(err) {
		return nil, err
	}

	// log.Info("gdrive client created")
	client := getClient(config)

	return client, nil
}

func UploadToGoogleDriveV2(uploader GDriveUploader) (*drive.File, error) {
	client, err := GetDriveClient()

	if err != nil {
		log.Info("getting gdrive client error: %v", err)
		return nil, err
	}

	if client == nil {
		log.Info("gdrive client is nil")
		return nil, errors.New("gdrive client is nil")
	}

	ctx := context.Background()
	// srv, err := drive.New(client) //	// deprecated
	srv, err := drive.NewService(ctx, option.WithHTTPClient(client))
	if log.IfError(err) {
		//log.Fatalf("Unable to retrieve drive Client %v", err)
		return nil, err
	}

	log.Info("try listing files...")
	r, err := srv.Files.List().PageSize(5).
		Fields("nextPageToken, files(id, name)").Do()
	if err != nil {
		log.Info("Unable to retrieve files: %v", err)
	} else {
		fmt.Println("Files:")
		if len(r.Files) == 0 {
			fmt.Println("No files found.")
		} else {
			for _, i := range r.Files {
				fmt.Printf("%s (%s)\n", i.Name, i.Id)
			}
		}
	}

	f, err := os.Open(uploader.OriginPath)
	if err != nil {
		log.Info("error opening file %q: %v \n", uploader.OriginPath, err)
		return nil, err
	}
	defer f.Close()

	var folderIDList []string
	//parentId := getOrCreateFolder(srv, filepath.Dir(destinationPath))
	//folderIDList = append(folderIDList, parentId)
	folderIDList, err = getOrCreateSubFolderV2(srv, uploader)
	if err != nil {
		return nil, err
	}

	if len(folderIDList) <= 0 {
		fmt.Println("Folder list id size ", len(folderIDList))
		return nil, errors.New("can not get folder list id")
	}

	driveFile, err := srv.Files.Create(&drive.File{Name: filepath.Base(uploader.DestinationPath), Parents: folderIDList}).Media(f).Do()
	if err != nil {
		fmt.Printf("Unable to create file: %v", err)
		return nil, err
	}

	//log.Printf("file: %+v", driveFile)
	fmt.Println("upload to gdrive finish: ", uploader.DestinationPath)

	return driveFile, err
}

func getOrCreateSubFolderV2(d *drive.Service, uploader GDriveUploader) ([]string, error) {
	folderName := filepath.Dir(uploader.DestinationPath) //replace file name
	parentIdFromDb := getParentIdFromDb(folderName)
	if parentIdFromDb != "" {
		return []string{parentIdFromDb}, nil
	}

	folderId := "root"
	idParents := make([]string, 0)
	if folderName == "" {
		return idParents, nil
	}

	paths := strings.Split(folderName, "/")
	start := 0
	subParentDriveId := ""
	parentDriveId := ""

	if uploader.SubParentIndex > 0 && uploader.SubParentId != "" {
		subParentDriveId = getParentIdFromDb(uploader.SubParentId)
		if subParentDriveId != "" {
			folderId = subParentDriveId
			start = uploader.SubParentIndex + 1
		}
	}

	parentPath := ""
	if subParentDriveId == "" && uploader.ParentIndex > 0 {
		parentPath = strings.Join(paths[0:(uploader.ParentIndex+1)], "_")
		parentDriveId = getParentIdFromDb(parentPath)
		if parentDriveId != "" {
			folderId = parentDriveId
			start = uploader.ParentIndex + 1
		}
	}

	for i := start; i < len(paths); i++ {
		path := paths[i]

		q := fmt.Sprintf("name='%s' and mimeType='application/vnd.google-apps.folder' and trashed=false", path)
		//for _,idParent := range idParents {
		//	q += fmt.Sprintf(" and '%s' in parents", idParent)
		//}

		if folderId != "" {
			q += fmt.Sprintf(" and '%s' in parents", folderId)
		}

		r, err := d.Files.List().Q(q).PageSize(1).Do()
		if err != nil {
			log.Info("Unable to retrieve foldername by query, Path: %v, Query: '%s', Err: %v \n", path, q, err)
			return nil, err
		}

		if len(r.Files) > 0 { //check if folder exist on google drive
			folderId = r.Files[0].Id
			idParents = append(idParents, folderId)
		} else {
			// no folder found create new
			f := &drive.File{Name: path, Description: "Auto Create by gdrive-upload", MimeType: "application/vnd.google-apps.folder", Parents: []string{folderId}}
			r, err := d.Files.Create(f).Do()
			if err != nil {
				fmt.Printf("error occurred when create folder, folderId: %v, err: %v\n", folderId, err)
				return nil, (fmt.Errorf("an error occurred when create folder: %v", err))

			}
			folderId = r.Id
			idParents = append(idParents, folderId)
		}

		//if subParentDriveId is empty, parentDriveId must be empty too
		if i == uploader.SubParentIndex && (subParentDriveId == "" || subParentDriveId != folderId) {
			SaveParentIdToDb(uploader.SubParentId, folderId)
		} else if i == uploader.ParentIndex && (parentDriveId == "" || parentDriveId != folderId) {
			SaveParentIdToDb(parentPath, folderId)
		}
	}

	SaveParentIdToDb(folderName, folderId)

	return []string{folderId}, nil
}

func UploadToGoogleDrive(originPath, destinationPath string) (*drive.File, error) {
	client, err := GetDriveClient()

	if err != nil {
		return nil, err
	}

	srv, err := drive.New(client)
	if err != nil {
		//log.Fatalf("Unable to retrieve drive Client %v", err)
		return nil, err
	}

	f, err := os.Open(originPath)
	if err != nil {
		log.Info("error opening %q: %v \n", originPath, err)
		return nil, err
	}
	defer f.Close()

	var folderIDList []string
	//parentId := getOrCreateFolder(srv, filepath.Dir(destinationPath))
	//folderIDList = append(folderIDList, parentId)
	folderIDList, err = getOrCreateSubFolder(srv, filepath.Dir(destinationPath))
	if err != nil {
		return nil, err
	}

	if len(folderIDList) <= 0 {
		log.Info("Folder list id size %v", len(folderIDList))
		return nil, errors.New("Can not get folder list id")
	}

	driveFile, err := srv.Files.Create(&drive.File{Name: filepath.Base(destinationPath), Parents: folderIDList}).Media(f).Do()
	if err != nil {
		log.Info("Unable to create file: %v", err)
		return nil, err
	}

	//log.Printf("file: %+v", driveFile)
	//fmt.Println("upload file ", filepath.Base(destinationPath), "finish")

	return driveFile, err
}

func getOrCreateSubFolder(d *drive.Service, folderName string) ([]string, error) {
	parentIdFromDb := getParentIdFromDb(folderName)
	if parentIdFromDb != "" {
		return []string{parentIdFromDb}, nil
	}

	folderId := "root"
	idParents := make([]string, 0)
	if folderName == "" {
		return idParents, nil
	}

	paths := strings.Split(folderName, "/")
	for _, path := range paths {
		q := fmt.Sprintf("name='%s' and mimeType='application/vnd.google-apps.folder' and trashed=false", path)
		//for _,idParent := range idParents {
		//	q += fmt.Sprintf(" and '%s' in parents", idParent)
		//}

		if folderId != "" {
			q += fmt.Sprintf(" and '%s' in parents", folderId)
		}

		r, err := d.Files.List().Q(q).PageSize(1).Do()
		if err != nil {
			log.Info("Unable to retrieve foldername by query '%s'. \n%v \n", q, err)
			return nil, err
		}

		if len(r.Files) > 0 {
			folderId = r.Files[0].Id
			idParents = append(idParents, folderId)
		} else {
			// no folder found create new
			f := &drive.File{Name: path, Description: "Auto Create by gdrive-upload", MimeType: "application/vnd.google-apps.folder", Parents: []string{folderId}}
			r, err := d.Files.Create(f).Do()
			if err != nil {
				fmt.Printf("An error occurred when create folder: %v\n", err)
				return nil, errors.New(fmt.Sprintf("An error occurred when create folder: %v\n", err))

			}
			folderId = r.Id
			idParents = append(idParents, folderId)
		}
	}

	SaveParentIdToDb(folderName, folderId)

	return []string{folderId}, nil
}

func SaveParentIdToDb(path string, parentId string) {
	log.Debug("saving to gdrive_parent_id. id : %s | '%s'", path, parentId)

	firebase := google2.GetFirebaseDb()
	if firebase != nil {
		log.IfError(firebase.NewRef("gdrive_id/"+safePath(path)).Set(context.Background(), parentId))
	} else {
		db := db.GetDbJson()
		if err := db.Write("gdrive_parent_id", strings.Replace(path, "/", "", -1), parentId); err != nil {
			//utils.SendErrorToSlack(fmt.Sprintf("[[ %s ]] - %s ", os.Getenv("server"), err.Error()))
			fmt.Println("Error", err)
		}
	}
}

func getParentIdFromDb(path string) string {
	var result string
	firebase := google2.GetFirebaseDb()
	if firebase != nil {
		log.IfError(firebase.NewRef("gdrive_id/"+safePath(path)).Get(context.Background(), &result))
	} else {
		db := db.GetDbJson()
		if err := db.Read("gdrive_parent_id", strings.Replace(path, "/", "", -1), &result); err != nil {
			//if error occur, it might be data is not created yet
			//utils.SendErrorToSlack(fmt.Sprintf("[[ %s ]] - %s ", os.Getenv("server"), err.Error()))
			//fmt.Println("Error", err)
		}
	}

	return result
}

// Retrieve a token, saves the token, then returns the generated client.
func getClient(config *oauth2.Config) *http.Client {
	// The file token.json stores the user's access and refresh tokens, and is
	// created automatically when the authorization flow completes for the first
	// time.
	// tokFile := "config/credentials/gdrive_token.json"
	tok, err := tokenFromFile(tokenFile)
	if err != nil {
		tok = getTokenFromWeb(config)
		saveToken(tokenFile, tok)
	}
	return config.Client(context.Background(), tok)
}

func tokenFromFile(file string) (*oauth2.Token, error) {
	f, err := os.Open(file)
	if err != nil {
		return nil, err
	}
	defer f.Close()
	tok := &oauth2.Token{}
	err = json.NewDecoder(f).Decode(tok)
	log.Info("gdrive client %v", tok)
	return tok, err
}

func getTokenFromWeb(config *oauth2.Config) *oauth2.Token {
	authURL := config.AuthCodeURL("state-token", oauth2.AccessTypeOffline)
	fmt.Printf("Go to the following link in your browser then type the "+
		"authorization code: \n%v\n", authURL)

	fmt.Println(">>> Enter the code: http://localhost/?state=state-token&code=")
	var authCode string
	if _, err := fmt.Scan(&authCode); err != nil {
		//log.Fatalf("Unable to read authorization code %v", err)
		fmt.Println("Error read code (gdrive): ", err)
	}

	log.Info(">> got auth code for google drive: ", authCode)
	tok, err := config.Exchange(context.Background(), authCode)
	if err != nil {
		//log.Fatalf("Unable to retrieve token from web %v", err)
		fmt.Println("Unable to retrieve token from web ", err)
	}
	return tok
}

func saveToken(path string, token *oauth2.Token) {
	fmt.Printf("Saving credential file to: %s\n", path)
	f, err := os.OpenFile(path, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0600)
	if err != nil {
		//log.Fatalf("Unable to cache oauth token: %v", err)
		log.Info("Unable to cache oauth token: %v", err)
	}
	defer f.Close()
	json.NewEncoder(f).Encode(token)
}

func safePath(path string) string {
	r := strings.NewReplacer(".", "")
	return r.Replace(path)
}

// run every first day of month
func RemoveOldLogs() {
	ctx := context.Background()
	// if time.Now().Day() != 1 {
	// 	return
	// }
	log.Info("job removeOldLogs is running....", time.Now().Day())

	client, err := GetDriveClient()

	if err != nil {
		fmt.Println("getting gdrive client error", err)
		return
	}

	srv, err := drive.NewService(ctx, option.WithHTTPClient(client))
	// srv, err := drive.New(client) //deprecated

	if log.IfError(err) {
		//log.Fatalf("Unable to retrieve drive Client %v", err)
		log.Info("Unable to retrieve drive Client %v", err)
		return
	}

	//find parent, path: UNIQ/APP LOG/
	parentPath := "UNIQ/APP LOG"
	parentId := getId(srv, parentPath)
	fmt.Println("parent Id: ", parentId)

	if parentId == "" {
		log.IfError(fmt.Errorf("parent path not found, path: %v", parentPath))
		return
	}

	folderPaths := getChild(srv, parentId)
	fmt.Println("####################")
	fmt.Println(utils.SimplyToJson(folderPaths))
	fmt.Println("####################")
}

type DriveFolder struct {
	Path   string
	PathId string
	Childs []DriveFolder
}

func getChild(srv *drive.Service, parentId string) []DriveFolder {
	q := fmt.Sprintf("mimeType='application/vnd.google-apps.folder' and trashed=false and '%s' in parents", parentId)
	pageToken := ""
	result := make([]DriveFolder, 0)
	re := regexp.MustCompile("^[0-9]{4}_[0-9]{2}$")
	for {
		r, err := srv.Files.List().Q(q).
			// PageSize(100).
			Fields("nextPageToken, files(id, name)").
			PageToken(pageToken).
			Do()
		if err != nil {
			log.Info("Unable to retrieve files: %v", err)
		}

		fmt.Println("Folders:", len(r.Files))
		if len(r.Files) == 0 {
			fmt.Println("No Folder found.")
		} else {
			for _, i := range r.Files {
				fmt.Printf("%s (%s)\n", i.Name, i.Id)
				if re.MatchString(i.Name) {
					fmt.Println(">>> ", i.Name)
					result = append(result, DriveFolder{Path: i.Name, PathId: i.Id})
					if shouldDelete(i.Name) {
						err = srv.Files.Delete(i.Id).Do()
						if err != nil {
							fmt.Printf("failed delete %s | %v", i.Name, err)
							panic(err)
						} else {
							fmt.Println("|| SUCCESS ||", i.Name)
						}
					}
				} else {
					childs := getChild(srv, i.Id)
					fmt.Printf("%s has %d childs \n", i.Name, len(childs))
					result = append(result, DriveFolder{Path: i.Name, PathId: i.Id, Childs: childs})

					if len(childs) == 0 {
						err = srv.Files.Delete(i.Id).Do()
					}
				}

			}
			pageToken = r.NextPageToken
		}

		if len(r.Files) == 0 || pageToken == "" {
			break
		}
	}
	fmt.Println("--------------------")
	fmt.Println(utils.SimplyToJson(result))
	fmt.Println("--------------------")
	return result
}

func shouldDelete(folderName string) bool {
	dirs := strings.Split(folderName, "_")
	if len(dirs) != 2 {
		fmt.Println(">>> NOT DATE FOLDER", folderName)
		return false
	}

	// year, month, day := time.Now().Date()

	year, month := utils.ToInt(dirs[0]), utils.ToInt(dirs[1])
	//handle if converting toInt failed, mean the dir is not number
	if year == 0 || month == 0 {
		return false
	}

	result := year < time.Now().Year() || month <= int(time.Now().Month())-2
	fmt.Println("> should delete : ", folderName, result)
	return result
}

func getId(srv *drive.Service, fullPath string) string {
	parentId := ""
	for _, path := range strings.Split(fullPath, "/") {
		q := fmt.Sprintf("mimeType='application/vnd.google-apps.folder' and trashed=false and name='%s'", path)
		if parentId != "" {
			q += fmt.Sprintf(" and '%s' in parents ", parentId)
		}

		r, err := srv.Files.List().Q(q).
			// PageSize(1).
			Fields("nextPageToken, files(id, name)").Do()
		if err != nil {
			log.Info("Unable to retrieve files: %v", err)
		}
		fmt.Println("Files:", len(r.Files))

		for _, i := range r.Files {
			fmt.Printf("%s (%s)\n", i.Name, i.Id)
			if strings.EqualFold(path, i.Name) {
				parentId = i.Id
			}
		}
	}

	return parentId
}
