package format

import (
	"strconv"
	"strings"
)

func Currency(amount int) string {
	result := ""
	amountStr := strconv.Itoa(amount)
	index := 1
	for i := len(amountStr); i > 0; i-- {
		data := amountStr[i-1 : i]
		if index%3 == 0 {
			result += data + "."
		} else {
			result += data
		}
		index++
	}
	result = reverse(result)
	if strings.HasPrefix(result, ".") {
		result = result[1:]
	} else if strings.HasPrefix(result, "-.") {
		result = "-" + result[2:]
	}

	return result
}

func reverse(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}
