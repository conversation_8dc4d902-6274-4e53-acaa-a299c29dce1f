package uploader

import (
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"

	imagekit "github.com/imagekit-developer/imagekit-go"
	"github.com/imagekit-developer/imagekit-go/api/uploader"
)

func UploadImageKit(filePath string) (string, error) {
	ik, err := imagekit.New()
	if err != nil {
		fmt.Println("error creating imagekit client:", err)
		return "", err
	}

	//convert file to base64
	file, err := os.ReadFile(filePath)
	if err != nil {
		fmt.Println("error reading file:", err)
		return "", err
	}

	//get fileName from filePath
	fileName := filepath.Base(filePath)

	base64 := base64.StdEncoding.EncodeToString(file)
	res, err := ik.Uploader.Upload(context.Background(), base64, uploader.UploadParam{
		FileName: fileName,
	})
	if err != nil {
		fmt.Println("error uploading image:", err)
		return "", err
	}
	fmt.Println("imagekit res:", res)
	return res.Data.Url, nil
}
