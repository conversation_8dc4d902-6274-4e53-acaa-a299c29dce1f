variables:
  DOCKER_DRIVER: "overlay2"
  IMAGE_NAME: gcr.io/${G_PROJECT_ID}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}-latest
  IMAGE_TAG_OLD: ${CI_REGISTRY}/${CI_PROJECT_PATH}:latest
  # IMAGE_TAG: ${CI_REGISTRY}/${CI_PROJECT_PATH}/${CI_COMMIT_REF_NAME}:latest
  IMAGE_URL: $CI_REGISTRY_IMAGE/${CI_COMMIT_REF_NAME}
  IMAGE_TAG: $IMAGE_URL:$CI_COMMIT_SHORT_SHA
  IMAGE_TAG_LATEST: $IMAGE_URL:latest
  IMAGE_TAG_CP: $IMAGE_TAG
  MEM_LIMIT: 300m
  REGION: asia-southeast1

stages:
  - build
  - deploy

before_script:
  # - echo ${GOOGLE_CREDENTIAL} | base64 -d > config/credentials/google_credential.json
  - echo $G_GOOGLE_STORAGE_KEY | base64 -d > config/credentials/google_credential.json
  - echo ${GDRIVE_CRED} | base64 -d > config/credentials/gdrive_client_credential.json  
  # - echo ${GDRIVE_TOKEN} | base64 -d > config/credentials/gdrive_token.json
  - cat ${GDRIVE_TOKEN} > config/credentials/gdrive_token.json
  - echo ${G_PUBSUB_RW_KEY} | base64 -d > config/credentials/pubsub_credential.json
  - echo ${FIREBASE_CREDENTIAL} | base64 -d > config/credentials/uniq-pos-firebase-adminsdk.json
  - cat ${FIREBASE_CREDENTIAL_CRM} > config/credentials/uniq-crm-firebase-adminsdk.json
  - cat $GCLOUD_SERVICE_ACCOUNT >  config/credentials/gcloud_service_account.json


.buildImage:
  stage: build
  image: docker:26.1.1-alpine3.19
  services:
    - docker:26.1.1-dind-alpine3.19
  script:    
    - echo ${PRIV_KEY} | base64 -d > config/rsa/app.rsa.key
    - echo ${PUB_KEY} | base64 -d > config/rsa/app.rsa.pub.key
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
    - docker build -t ${IMAGE_TAG} -t ${IMAGE_TAG_LATEST} .
    - docker push ${IMAGE_TAG}
    - docker push ${IMAGE_TAG_LATEST}
  after_script:
    - docker logout ${CI_REGISTRY}
  # tags:
  #   - testing-docker
    # - saas-linux-medium-amd64

buildImageDev:
  extends: 
    - .buildImage
  rules:
    - if: ($CI_PIPELINE_SOURCE == "web" || $CI_PIPELINE_SOURCE == "trigger") && $IMAGE_TAG_CP != $IMAGE_TAG && $CI_COMMIT_BRANCH == "dev"
      when: never
    - if: $CI_COMMIT_BRANCH == "dev"
  environment:
    name: development

buildImageStaging:
  extends: 
    - .buildImage 
  rules:
    - if: ($CI_PIPELINE_SOURCE == "web" || $CI_PIPELINE_SOURCE == "trigger") && $IMAGE_TAG_CP != $IMAGE_TAG && $CI_COMMIT_BRANCH == "staging"
      when: never
    - if: $CI_COMMIT_BRANCH == "staging"
  environment:
    name: staging
  tags:
    - staging

buildImageProduction:
  extends: 
    - .buildImage
  rules:
    - if: ($CI_PIPELINE_SOURCE == "web" || $CI_PIPELINE_SOURCE == "trigger") && $IMAGE_TAG_CP != $IMAGE_TAG && $CI_COMMIT_BRANCH == "master"
      when: never
    - if: $CI_COMMIT_BRANCH == "master"
  environment:
    name: production   
  tags:
    - production 

.deploy:
  stage: deploy
  image: docker/compose:alpine-1.29.2
  before_script:
    - cat ${ENV} > .env
  script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
    - docker pull ${IMAGE_TAG_LATEST}
    - docker container rm -f $CI_PROJECT_NAME || true
    - docker run --restart unless-stopped --name ${CI_PROJECT_NAME} --network uniq-network -v /docker/api-pos:/temp --env-file .env --log-driver=gcplogs -p 1522:8000 -m $MEM_LIMIT --memory-swap $MEM_LIMIT -d --cpus=0.5  ${IMAGE_TAG_LATEST}

deployDev:
  extends: 
    - .deploy
  environment:
    name: development
  only:
    - dev
  tags:
    - testing-docker    

deployStaging:
  extends: 
    - .deploy
  environment:
    name: staging
  only:
    - staging
  variables:
    MEM_LIMIT: 600m
  tags:
    - staging

deployProduction:
  extends: 
    - .deploy
  environment:
    name: production
  only:
    - master
  # needs: ["buildImageProduction"]
  variables:
    MEM_LIMIT: 600m
  tags:
    - production    

.cloudRun:
  stage: build
  image: google/cloud-sdk:alpine
  script:
    - echo ${PRIV_KEY} | base64 -d > config/rsa/app.rsa.key
    - echo ${PUB_KEY} | base64 -d > config/rsa/app.rsa.pub.key
    - cat $ENV > .env
    - cat $ENV_YAML > .env.yaml
    - echo $G_GCLOUD_RUN_KEY | base64 -d > ${HOME}/gcloud-service-key.json    
    - gcloud auth activate-service-account --key-file ${HOME}/gcloud-service-key.json
    - gcloud config set project $G_PROJECT_ID
    - gcloud config set gcloudignore/enabled false
    - gcloud run deploy ${CI_PROJECT_NAME}-${CI_COMMIT_REF_NAME} --source . --env-vars-file .env.yaml --allow-unauthenticated --region $REGION --add-cloudsql-instances=$DB_INSTANCE

deployCloudRunDev:
  extends: 
    - .cloudRun
  environment:
    name: development
  when: manual
  only:
    - dev
  variables:
    REGION: us-central1

deployCloudRunStaging:
  extends: 
    - .cloudRun
  environment:
    name: staging
  when: manual
  only:
    - staging
  variables:
    DB_INSTANCE: uniq-187911:asia-southeast1:primary-db     

deployCloudRunProduction:
  extends: 
    - .cloudRun
  environment:
    name: production
  when: manual
  only:
    - master
  variables:
    DB_INSTANCE: uniq-187911:asia-southeast1:primary-db       
