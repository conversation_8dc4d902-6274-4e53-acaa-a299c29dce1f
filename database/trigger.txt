CREATE TRIGGER `tg_sales_breakdown` AFTER INSERT ON `sales_detail`
 FOR EACH ROW BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE ids, qtys INT;
    DECLARE cur CURSOR FOR SELECT item_product_detail_fkid, qty FROM breakdown WHERE product_detail_fkid = NEW.product_detail_fkid;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN cur;
        ins_loop: LOOP
            FETCH next FROM cur INTO ids, qtys;
            IF done THEN
                LEAVE ins_loop;
            END IF;
            INSERT INTO sales_breakdown (sales_detail_fkid, product_detail_fkid, qty_total) VALUES (NEW.sales_detail_id, ids, qtys * NEW.qty);
        END LOOP;
    CLOSE cur;
END

