package models

type ReturProduct struct {
	AdminFkid           int         `json:"admin_fkid"`
	DataCreated         int64       `json:"data_created"`
	DataStatus          string      `json:"data_status"`
	EmployeeFkid        interface{} `json:"employee_fkid"`
	HargaStok           int         `json:"harga_stok"`
	KeteranganRetur     string      `json:"keterangan_retur"`
	PurchaseProductFkid int         `json:"purchase_product_fkid"`
	QtyNota             int         `json:"qty_nota"`
	QtyRetur            int         `json:"qty_retur"`
	QtyStok             int         `json:"qty_stok"`
	ReturProductID      int         `json:"retur_product_id"`
	TotDis              int         `json:"tot_dis"`
	Total               int         `json:"total"`
}
