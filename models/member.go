package models

type MemberResponse struct {
	MemberID        int64  `db:"member_id" json:"member_id,omitempty"`
	Name            string `db:"name" json:"name,omitempty"` // Assuming this is the member's name from the members table
	Phone           string `db:"phone" json:"phone,omitempty"`
	TypeFkID        int64  `db:"type_fkid" json:"type_fk_id,omitempty"`
	TypeName        string `db:"type_name" json:"type_name,omitempty"` // Assuming this is the product name acting as the member type name
	SecretIDExpired int64  `db:"secret_id_expired" json:"secret_id_expired,omitempty"`
}

type MemberRequest struct {
	SecretId string
	QrCode   string
	MemberId int
}
