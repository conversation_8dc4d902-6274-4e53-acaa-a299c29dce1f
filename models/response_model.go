package models

type ResponseArray struct {
	Status  bool                     `json:"status"`
	Code    int                      `json:"code"`
	Millis  int64                    `json:"millis"`
	Message string                   `json:"message"`
	Data    []map[string]interface{} `json:"data"`
}

type Response struct {
	Status  bool                   `json:"status"`
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Millis  int64                  `json:"millis"`
	Data    map[string]interface{} `json:"data"`
}

type ResponseString struct {
	Status  bool   `json:"status"`
	Code    int    `json:"code"`
	Message string `json:"message"`
	Millis  int64  `json:"millis"`
	Data    string `json:"data"`
}

type ResponseAny struct {
	Status  bool        `json:"status"`
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Millis  int64       `json:"millis"`
	Data    interface{} `json:"data"`
}
