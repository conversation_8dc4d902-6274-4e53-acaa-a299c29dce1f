package models

type FreeImageResponse struct {
	StatusCode int `json:"status_code"`
	Success    struct {
		Message string `json:"message"`
		Code    int    `json:"code"`
	} `json:"success"`
	Image struct {
		Name             string      `json:"name"`
		Extension        string      `json:"extension"`
		Width            int         `json:"width"`
		Height           int         `json:"height"`
		Size             int         `json:"size"`
		Time             int         `json:"time"`
		Expiration       int         `json:"expiration"`
		Adult            int         `json:"adult"`
		Status           int         `json:"status"`
		Cloud            int         `json:"cloud"`
		Vision           int         `json:"vision"`
		Likes            int         `json:"likes"`
		Description      interface{} `json:"description"`
		OriginalExifdata interface{} `json:"original_exifdata"`
		OriginalFilename string      `json:"original_filename"`
		ViewsHTML        int         `json:"views_html"`
		ViewsHotlink     int         `json:"views_hotlink"`
		AccessHTML       int         `json:"access_html"`
		AccessHotlink    int         `json:"access_hotlink"`
		File             struct {
			Resource struct {
				Chain struct {
					Image string `json:"image"`
					Thumb string `json:"thumb"`
				} `json:"chain"`
				ChainCode struct {
					Image string `json:"image"`
					Thumb string `json:"thumb"`
				} `json:"chain_code"`
			} `json:"resource"`
		} `json:"file"`
		IsAnimated       int     `json:"is_animated"`
		Nsfw             int     `json:"nsfw"`
		IDEncoded        string  `json:"id_encoded"`
		Ratio            float64 `json:"ratio"`
		SizeFormatted    string  `json:"size_formatted"`
		Filename         string  `json:"filename"`
		URL              string  `json:"url"`
		URLShort         string  `json:"url_short"`
		URLSeo           string  `json:"url_seo"`
		URLViewer        string  `json:"url_viewer"`
		URLViewerPreview string  `json:"url_viewer_preview"`
		URLViewerThumb   string  `json:"url_viewer_thumb"`
		Image            struct {
			Filename  string `json:"filename"`
			Name      string `json:"name"`
			Mime      string `json:"mime"`
			Extension string `json:"extension"`
			URL       string `json:"url"`
			Size      int    `json:"size"`
		} `json:"image"`
		Thumb struct {
			Filename  string `json:"filename"`
			Name      string `json:"name"`
			Mime      string `json:"mime"`
			Extension string `json:"extension"`
			URL       string `json:"url"`
		} `json:"thumb"`
		DisplayURL         string `json:"display_url"`
		DisplayWidth       int    `json:"display_width"`
		DisplayHeight      int    `json:"display_height"`
		ViewsLabel         string `json:"views_label"`
		LikesLabel         string `json:"likes_label"`
		HowLongAgo         string `json:"how_long_ago"`
		DateFixedPeer      string `json:"date_fixed_peer"`
		Title              string `json:"title"`
		TitleTruncated     string `json:"title_truncated"`
		TitleTruncatedHTML string `json:"title_truncated_html"`
		IsUseLoader        bool   `json:"is_use_loader"`
	} `json:"image"`
	StatusTxt string `json:"status_txt"`
}
