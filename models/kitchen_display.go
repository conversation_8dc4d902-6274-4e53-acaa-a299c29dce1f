package models

type KitchenDisplay struct {
	SettingKitchenDisplayID int64                    `json:"setting_kitchen_display_id"`
	Name                    string                   `json:"name"`
	Address                 string                   `json:"address"`
	Categories              []KitchenDisplayCategory `json:"categories"`
	OutletFKID              int                      `json:"outlet_fkid"`
	DataCreated             int64                    `json:"data_created"`
	DataModified            int64                    `json:"data_modified"`
}

type KitchenDisplayCategory struct {
	CategoryID   int    `json:"category_id"`
	CategoryName string `json:"category_name"`
}
