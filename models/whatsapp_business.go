package models

type MessageContentDetail struct {
	Type       string      `json:"type,omitempty"`
	Attributes interface{} `json:"attributes,omitempty"`
}

type CrmAuthAttribute struct {
	AppName   string `json:"app_name,omitempty"`
	AuthUrl   string `json:"auth_url,omitempty"`
	ExpiredAt int64  `json:"expired_at,omitempty"`
}

type ReceiptAttribute struct {
	ImageUrl    string `json:"image_url,omitempty"`
	FeedbackUrl string `json:"feedback_url,omitempty"`
	Language    string `json:"language,omitempty"`
}
