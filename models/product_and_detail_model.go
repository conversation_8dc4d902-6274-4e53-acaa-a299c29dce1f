package models

type ProductAndDetail struct {
	Active                     string `json:"active"`
	AdminFkid                  int    `json:"admin_fkid"`
	CatalogueType              string `json:"catalogue_type"`
	DataCreated                int64  `json:"data_created"`
	DataModified               int64  `json:"data_modified"`
	DataStatus                 string `json:"data_status"`
	Discount                   string `json:"discount"`
	Name                       string `json:"name"`
	OutletFkid                 int    `json:"outlet_fkid"`
	Photo                      string `json:"photo"`
	PriceBuy                   int    `json:"price_buy"`
	PriceBuyStart              int    `json:"price_buy_start"`
	PriceSell                  int    `json:"price_sell"`
	ProductCategoryFkid        int    `json:"product_category_fkid"`
	ProductDetailID            int    `json:"product_detail_id"`
	ProductFkid                int    `json:"product_fkid"`
	ProductID                  int    `json:"product_id"`
	ProductSubcategoryFkid     int    `json:"product_subcategory_fkid"`
	ProductTypeFkid            int    `json:"product_type_fkid"`
	PurchaseReportCategoryFkid int    `json:"purchase_report_category_fkid"`
	Sku                        string `json:"sku"`
	Stock                      string `json:"stock"`
	StockManagement            int    `json:"stock_management"`
	UnitFkid                   int    `json:"unit_fkid"`
	Voucher                    string `json:"voucher"`
}