package models

type OrderSales struct {
	OrderSalesId     string `json:"order_sales_id"`
	SalesFkid        string `json:"sales_fkid"`
	Status           string `json:"status"`
	TimeAcceptReject int64  `json:"time_accept_reject"`
	TimeReady        int64  `json:"time_ready"`
	TimeTaken        int64  `json:"time_taken"`
	OutletFkid       int    `json:"outlet_fkid"`
	RejectReason     string `json:"reject_reason"`
}

type OrderSalesEntity struct {
	ID               int         `json:"id,omitempty"`
	OrderSalesID     string      `json:"order_sales_id,omitempty"`
	SalesFkid        interface{} `json:"sales_fkid,omitempty"`
	MemberFkid       int         `json:"member_fkid,omitempty"`
	OutletFkid       int         `json:"outlet_fkid,omitempty"`
	OrderType        string      `json:"order_type,omitempty"`
	OrderNote        string      `json:"order_note,omitempty"`
	PickupTime       interface{} `json:"pickup_time,omitempty"`
	Status           string      `json:"status,omitempty"`
	TimeOrder        int64       `json:"time_order,omitempty"`
	TimeModified     int64       `json:"time_modified,omitempty"`
	TimeAcceptReject int64       `json:"time_accept_reject,omitempty"`
	TimeReady        int         `json:"time_ready,omitempty"`
	TimeTaken        int         `json:"time_taken,omitempty"`
	Items            string      `json:"items,omitempty"`
	RejectReason     interface{} `json:"reject_reason,omitempty"`
	GrandTotal       int         `json:"grand_total,omitempty"`
	PaymentInfo      string      `json:"payment_info,omitempty"`
}
