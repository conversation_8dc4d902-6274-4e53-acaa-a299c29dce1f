package models

type Purchase struct {
	AdminFkid     int              `json:"admin_fkid"`
	Bayar         int              `json:"bayar"`
	DataCreated   int64            `json:"data_created"`
	DataStatus    string           `json:"data_status"`
	Detail        []PurchaseDetail `json:"detail"`
	DiscountTotal int              `json:"discount_total"`
	EmployeeFkid  interface{}      `json:"employee_fkid"`
	GrandTotal    int              `json:"grand_total"`
	Hutang        int              `json:"hutang"`
	Invoice       string           `json:"invoice"`
	JatuhTempo    interface{}      `json:"jatuh_tempo"`
	Keterangan    string           `json:"keterangan"`
	OutletFkid    int              `json:"outlet_fkid"`
	PurchaseID    int              `json:"purchase_id"`
	StatusLunas   string           `json:"status_lunas"`
	SubTotal      int              `json:"sub_total"`
	SupplierFkid  int              `json:"supplier_fkid"`
	TotalPajak    int              `json:"total_pajak"`
	TypeDiscount  string           `json:"type_discount"`
	UserInput     string           `json:"user_input"`
}

type PurchaseDetail struct {
	DataCreated        string `json:"data_created"`
	DataStatus         string `json:"data_status"`
	Discount           int    `json:"discount"`
	PriceNota          int    `json:"price_nota"`
	PriceStok          int    `json:"price_stok"`
	ProductsFkid       int    `json:"products_fkid"`
	PurchaseFkid       int    `json:"purchase_fkid"`
	PurchaseProductsID int    `json:"purchase_products_id"`
	QtyNota            int    `json:"qty_nota"`
	QtyRetur           int    `json:"qty_retur"`
	QtyStok            int    `json:"qty_stok"`
	Retur              string `json:"retur"`
	TotDis             int    `json:"tot_dis"`
	Total              int    `json:"total"`
	UnitFkidNota       int    `json:"unit_fkid_nota"`
	UnitFkidStok       int    `json:"unit_fkid_stok"`
	Pajak              int    `json:"pajak"`
	Keterangan         string `json:"keterangan"`
	QtyReceive         int    `json:"qty_receive"`
}
