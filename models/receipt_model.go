package models

type Items struct {
	Product    string
	Qty        string
	Price      string
	SubTotal   string
	SubItems   []Items
	Promotions []Promotion
}

type PaymentMedia struct {
	Method string
	Total  string
}

type TaxAndDisc struct {
	Name  string
	Total string
}

type Receipt struct {
	ReceiptNo     string
	Date          string
	Cashier       string
	VoucherCode   string
	Outlet        string
	OutletAddress string
	OutletPhone   string
	OutletInfo    string
	OutletLogo    string
	Items         []Items
	TaxAndDisc    []TaxAndDisc
	GrandTotal    string
	Payments      []PaymentMedia
	Return        string
	ReceiptNote   string
	ReceiptSosMed string
	FeedbackUrl   string
	Promotions    []Promotion
	Customer      string
	Payment       string
	Table         string
	PointName     string
	PointTotal    int
	SalesTag      string
}

type ReceiptMessage struct {
	Title        string
	Content      string
	ReceiverType string
	Attachments  []string
	Detail       string
}
