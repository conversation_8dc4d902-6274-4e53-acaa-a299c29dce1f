package models

//type Product struct {
//	ProductID                  string `json:"product_id"`
//	Name                       string `json:"name"`
//	CatalogueType              string `json:"catalogue_type"`
//	ProductTypeFkid            string `json:"product_type_fkid"`
//	ProductCategoryFkid        string `json:"product_category_fkid"`
//	ProductSubcategoryFkid     string `json:"product_subcategory_fkid"`
//	PurchaseReportCategoryFkid string `json:"purchase_report_category_fkid"`
//	UnitFkid                   string `json:"unit_fkid"`
//	StockManagement            string `json:"stock_management"`
//	Barcode                    string `json:"barcode",omitempty`
//	Sku                        string `json:"sku",omitempty`
//	Photo                      string `json:"photo"`
//	AdminFkid                  string `json:"admin_fkid"`
//	DataCreated                string `json:"data_created"`
//	DataModified               string `json:"data_modified"`
//	DataStatus                 string `json:"data_status"`
//}