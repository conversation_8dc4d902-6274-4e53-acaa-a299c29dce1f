package models

import (
	"fmt"
	"os"
)

type SubscriptionStatus struct {
	DaysLeft          int
	Type              string //trial, or subscribe
	DateExpired       string
	TimeMillisExpired int64
	IsSlotAvailable   bool
	TotalSlot         int
	DeviceLoginNames  []string
	DeviceLogin       []Device `json:"device_login"`
}

type SubscriptionUser struct {
	TotalActiveSlot  int
	TotalFeatureSlot int
	MinDayLeft       int
	MaxDayLeft       int
	TrialLeft        int
	Slots            []Slot
}

type Slot struct {
	DayLeft  int
	IsActive bool
}

type Device struct {
	DeviceName   string `json:"name"`
	DeviceId     int    `json:"device_id"`
	OutletName   string `json:"outlet_name"`
	EmployeeName string `json:"employee_name"`
}

type RequestSubscription struct {
	Billing struct {
		//DetailTotal     int         `json:"detail_total"`
		//KodeUnik        int         `json:"kode_unik"`
		AdminFkid       string      `json:"admin_fkid"`
		GeneratedBy     string      `json:"generated_by"`
		BillingNotes    interface{} `json:"billing_notes"`
		InvoiceType     string      `json:"invoice_type"`
		Discount        interface{} `json:"discount"`
		DiscountPercent interface{} `json:"discount_percent"`
		DiscountNote    string      `json:"discount_note"`
	} `json:"billing"`
	Detail []struct {
		BillingFkid    interface{} `json:"billing_fkid"`
		SysServiceFkid int         `json:"sys_service_fkid"`
		Qty            int         `json:"qty"`
		//ServiceLengthDay string      `json:"service_length_day"`
		ServicePeriod  interface{} `json:"service_period"`
		ServiceFeature string      `json:"service_feature"`
		ServiceType    string      `json:"service_type"`
		Price          interface{} `json:"price"`
		Discount       interface{} `json:"discount"`
	} `json:"detail"`
}

type BillingAuth struct {
	User           string `json:"user"`
	EnableDiscount bool   `json:"enable_discount"`
}

type SubscriptionRequest struct {
	PaymentMethod string                `json:"payment_method"`
	Services      []SubscriptionService `json:"services"`
	Discount      int                   `json:"discount"`
	DiscountNote  string                `json:"discount_note"`
}

type SubscriptionService struct {
	ServiceID     int                    `json:"service_id"`
	Period        int                    `json:"period"`
	Qty           int                    `json:"qty"`
	ServiceDetail map[string]interface{} //this will be filled by system
}

func (sub SubscriptionUser) GenerateMessage() string {
	warn := ""
	if sub.TrialLeft >= 0 && sub.TrialLeft <= 5 {
		//warn only if user has not yet subscribed any slot
		if sub.TotalFeatureSlot == 0 {
			warn = "*[TRIAL REMINDER]*\n" +
				"Masa *Free Trial* anda "
			if sub.TrialLeft <= 0 {
				warn += "telah berakhir"
			} else {
				warn += fmt.Sprintf("anda akan segera berakhir dalam waktu %d hari", sub.TrialLeft)
			}
		}
	} else if sub.MinDayLeft >= 0 && sub.MinDayLeft <= 5 && sub.TrialLeft <= 0 {
		warn = "*[SUBSCRIPTION REMINDER]*\n"
		if sub.MinDayLeft == sub.MaxDayLeft {
			if sub.MinDayLeft == 0 {
				warn += "Masa *Berlangganan* anda telah berakhir"
			} else {
				warn += fmt.Sprintf("Masa *Berlangganan* anda akan segera berakhir dalam waktu %d hari", sub.MinDayLeft)
			}
		} else {
			totalExpiredSoon := 0
			for _, slot := range sub.Slots {
				if slot.DayLeft == sub.MinDayLeft {
					totalExpiredSoon += 1
				}
			}
			dayLeftInfo := fmt.Sprintf("segera berakhir dalam waktu %d hari", sub.MinDayLeft)
			if sub.MinDayLeft == 0 {
				dayLeftInfo = "berakhir hari ini"
			}
			warn += fmt.Sprintf("Masa *Berlangganan* %d dari %d slot yang anda miliki akan %s", totalExpiredSoon, sub.TotalActiveSlot+sub.TotalFeatureSlot, dayLeftInfo)
		}
	}
	if warn != "" {
		baseUrl := "https://client.uniq.id"
		if os.Getenv("server") == "demo" {
			baseUrl = "https://pos.uniq.id"
		}
		return fmt.Sprintf("\n\n%s\n"+
			"Upgrade sekarang untuk terus dapat menggunakan UNIQ.\n"+
			"*SUBSCRIBE :* _%s/settings/subscription-add_", warn, baseUrl)
	}
	return ""
}
