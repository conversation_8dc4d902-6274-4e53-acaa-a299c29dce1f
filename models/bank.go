package models

import "strings"

type BankDetailEntity struct {
	BankdetailID int    `json:"bankdetail_id,omitempty"`
	BankFkid     int    `json:"bank_fkid,omitempty"`
	OutletFkid   int    `json:"outlet_fkid,omitempty"`
	ActiveOnPos  int    `json:"active_on_pos,omitempty"`
	ActiveOnCrm  int    `json:"active_on_crm,omitempty"`
	DataModified string `json:"data_modified,omitempty"`
	DataStatus   string `json:"data_status,omitempty"`
}

type BankFilter struct {
	Name               string
	AdminId            int
	Provider           string //payment gateway provider, like midtrans, xendit, etc
	ProviderPaymentKey string //payment method, formatted
	IsInstantPayment   bool
}

func (b BankFilter) WhereSql() (string, map[string]interface{}) {
	where := make([]string, 0)
	params := make(map[string]interface{})
	if b.Name != "" {
		where = append(where, "name = @name")
		params["name"] = b.Name
	}
	if b.AdminId > 0 {
		where = append(where, "admin_fkid = @adminId")
		params["adminId"] = b.AdminId
	}

	return strings.Join(where, " AND "), params
}

func (b BankDetailEntity) ToMap() map[string]interface{} {
	m := make(map[string]interface{})
	m["bank_fkid"] = b.BankFkid
	m["outlet_fkid"] = b.OutletFkid
	m["active_on_pos"] = b.ActiveOnPos
	m["active_on_crm"] = b.ActiveOnCrm
	m["data_modified"] = b.DataModified
	m["data_status"] = b.DataStatus
	return m
}
