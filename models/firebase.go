package models

import (
	"os"
	"strings"
)

type RemoteConfig struct {
	Parameters map[string]RemoteConfigParameter `json:"parameters,omitempty"`
}

type RemoteConfigParameter struct {
	DefaultValue      interface{}                  `json:"defaultValue,omitempty"`
	ConditionalValues map[string]RemoteConfigValue `json:"conditionalValues,omitempty"`
}

type RemoteConfigValue struct {
	Value interface{} `json:"value,omitempty"`
}

func (r *RemoteConfig) Get(key string) interface{} {
	param := r.Parameters[key]
	value := param.DefaultValue

	//check if conditional parameter provided
	env := os.Getenv("ENV")
	for k, v := range param.ConditionalValues {
		if strings.Contains(strings.ToLower(k), strings.ToLower(env)) {
			value = v.Value
			break
		}
	}

	return value
}
