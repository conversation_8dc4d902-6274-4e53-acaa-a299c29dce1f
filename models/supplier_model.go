package models

type Supplier struct {
	Address      string      `json:"address"`
	AdminFkid    int         `json:"admin_fkid"`
	City         string      `json:"city"`
	DataCreated  string      `json:"data_created"`
	DataStatus   string      `json:"data_status"`
	Email        string      `json:"email"`
	Fax          string      `json:"fax"`
	Keterangan   string      `json:"keterangan"`
	Name         string      `json:"name"`
	Npwp         string      `json:"npwp"`
	Phone        string      `json:"phone"`
	SupplierID   interface{} `json:"supplier_id"`
	Tempo        int         `json:"tempo"`
	TimeCreated  int64       `json:"time_created"`
	TimeModified int64       `json:"time_modified"`
	Type         string      `json:"type"`
}
