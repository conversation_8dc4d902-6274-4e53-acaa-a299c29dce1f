package models

type DeviceEntity struct {
	DeviceID      int    `json:"device_id,omitempty"`
	Name          string `json:"name,omitempty"`
	Imei          string `json:"imei,omitempty"`
	OutletFkid    int    `json:"outlet_fkid,omitempty"`
	AdminFkid     int    `json:"admin_fkid,omitempty"`
	DataCreated   int64  `json:"data_created,omitempty"`
	DataModified  int64  `json:"data_modified,omitempty"`
	DataStatus    int    `json:"data_status,omitempty"`
	DeviceStatus  string `json:"device_status,omitempty"`
	OpenShiftFkid int    `json:"open_shift_fkid,omitempty"`
	EmployeeFkid  int    `json:"employee_fkid,omitempty"`
	Version       string `json:"version,omitempty"`
	LastSync      int64  `json:"last_sync,omitempty"`
	FirebaseToken string `json:"firebase_token,omitempty"`
	DeviceInfo    string `json:"device_info,omitempty"`
}
