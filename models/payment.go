package models

type PaymentNotificationEntity struct {
	PaymentNotificationID int    `db:"payment_notification_id" json:"payment_notification_id,omitempty"`
	TransactionID         string `db:"transaction_id" json:"transaction_id,omitempty"`
	OrderID               string `db:"order_id" json:"order_id,omitempty"`
	TransactionStatus     string `db:"transaction_status" json:"transaction_status,omitempty"`
	DataCreated           int64  `db:"data_created" json:"data_created,omitempty"`
	Info                  string `db:"info" json:"info,omitempty"`
	PaymentGateway        string `db:"payment_gateway" json:"payment_gateway,omitempty"`
}
