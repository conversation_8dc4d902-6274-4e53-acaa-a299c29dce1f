package models

type OpenShift struct {
	OpenShiftId  int64  `json:"open_shift_id"`
	EarlyCash    int    `json:"early_cash"`
	EmployeeFkid int    `json:"employee_fkid"`
	OutletFkid   int    `json:"outlet_fkid"`
	ShiftFkid    int    `json:"shift_fkid"`
	TimeOpen     int64  `json:"time_open"`
	DeviceID     string `json:"device_id"`
	DeviceName   string `json:"device_name"`
	TimeMillis   int64  `json:"time_millis"`
}
