package models

type EmployeeEntity struct {
	EmployeeID         int         `json:"employee_id,omitempty"`
	Name               string      `json:"name,omitempty"`
	Address            string      `json:"address,omitempty"`
	Phone              string      `json:"phone,omitempty"`
	Photo              interface{} `json:"photo,omitempty"`
	JabatanFkid        int         `json:"jabatan_fkid,omitempty"`
	Level              int         `json:"level,omitempty"`
	Email              string      `json:"email,omitempty"`
	RoleMobile         string      `json:"role_mobile,omitempty"`
	AccessMode         string      `json:"access_mode,omitempty"`
	AccessStatusWeb    string      `json:"access_status_web,omitempty"`
	AccessStatusMobile string      `json:"access_status_mobile,omitempty"`
	DateJoin           int64       `json:"date_join,omitempty"`
	AdminFkid          int         `json:"admin_fkid,omitempty"`
	DataCreated        int         `json:"data_created,omitempty"`
	DataModified       int64       `json:"data_modified,omitempty"`
	DataStatus         string      `json:"data_status,omitempty"`
	AccountID          int         `json:"account_id,omitempty"`
}

type MobileRole struct {
	Inputkasmasuk          bool `json:"inputkasmasuk,omitempty"`
	Inputkaskeluar         bool `json:"inputkaskeluar,omitempty"`
	Inputpembelian         bool `json:"inputpembelian,omitempty"`
	SettingPrinter         bool `json:"setting_printer,omitempty"`
	Tutupkasir             bool `json:"tutupkasir,omitempty"`
	ReprintNota            bool `json:"reprint_nota,omitempty"`
	ReprintOrder           bool `json:"reprint_order,omitempty"`
	ReprintTutupkasir      bool `json:"reprint_tutupkasir,omitempty"`
	Gantiharga             bool `json:"gantiharga,omitempty"`
	Gantidiskonperbill     bool `json:"gantidiskonperbill,omitempty"`
	Bukalaciuang           bool `json:"bukalaciuang,omitempty"`
	Pembayaran             bool `json:"pembayaran,omitempty"`
	Compliment             bool `json:"compliment,omitempty"`
	Voucher                bool `json:"voucher,omitempty"`
	Gantivoucherperitem    bool `json:"gantivoucherperitem,omitempty"`
	Simpankeorderlist      bool `json:"simpankeorderlist,omitempty"`
	Duty                   bool `json:"duty,omitempty"`
	Gantidiskonperitem     bool `json:"gantidiskonperitem,omitempty"`
	Gantipajak             bool `json:"gantipajak,omitempty"`
	ReprintRefund          bool `json:"reprint_refund,omitempty"`
	Refund                 bool `json:"refund,omitempty"`
	Inputbarangrusak       bool `json:"inputbarangrusak,omitempty"`
	Inputbaranghabis       bool `json:"inputbaranghabis,omitempty"`
	Inputpromo             bool `json:"inputpromo,omitempty"`
	Masterlogin            bool `json:"masterlogin,omitempty"`
	MejaSplit              bool `json:"meja_split,omitempty"`
	MejaMove               bool `json:"meja_move,omitempty"`
	Viewcloseregister      bool `json:"viewcloseregister,omitempty"`
	Viewtotalachievement   bool `json:"viewtotalachievement,omitempty"`
	Viewtransactionhistory bool `json:"viewtransactionhistory,omitempty"`
	ProductCreate          bool `json:"product_create,omitempty"`
	PrintDailyrecap        bool `json:"print_dailyrecap,omitempty"`
	// Authorization          string `json:"authorization,omitempty"`
}

type RoleAuthorization struct {
	Diskonall         bool `json:"diskonall,omitempty"`
	Diskonperitem     bool `json:"diskonperitem,omitempty"`
	Freeitem          bool `json:"freeitem,omitempty"`
	Refund            bool `json:"refund,omitempty"`
	ReprintRefund     bool `json:"reprint_refund,omitempty"`
	ReprintReceipt    bool `json:"reprint_receipt,omitempty"`
	Closeshift        bool `json:"closeshift,omitempty"`
	ReprintCloseshift bool `json:"reprint_closeshift,omitempty"`
	Void              bool `json:"void,omitempty"`
}
