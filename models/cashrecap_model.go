package models

type CashRecap struct {
	OutletFkid    int 	`json:"outlet_fkid"`
	EmployeeFkid  int	 `json:"employee_fkid"`
	TimeCreated   int64  `json:"time_created"`
	TimeModified  int64  `json:"time_modified"`
	Cash<PERSON>ecapID   int64  `json:"cash_recap_id"`
	Card          int    `json:"card"`
	SalesTotal    int    `json:"sales_total"`
	Cash          int    `json:"cash"`
	PreviousRecap int64  `json:"previous_recap"`
	OpenShiftFkid int    `json:"open_shift_fkid"`
}

type CashRecapReport struct {
	Total int
	Report string
	OutletId int
}

//sort ASC
type SortByTotal []CashRecapReport
func (a SortByTotal) Len() int { return len(a)}
func (a SortByTotal) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a SortByTotal) Less(i, j int) bool { return a[i].Total < a[j].Total}