package models

const (
	MediaEmail     = "email"
	MediaWhatsApp  = "whatsapp"
	MediaPushNotif = "push_notif"
)

type ScheduledMessage struct {
	ID                 int      `json:"id,omitempty"`
	IdentifierID       string   `json:"identifier_id,omitempty"`
	Title              string   `json:"title,omitempty"`
	Message            string   `json:"message,omitempty"`
	TimeDeliver        int64    `json:"time_deliver,omitempty"`
	TimeSent           int64    `json:"time_sent,omitempty"`
	DataCreated        int64    `json:"data_created,omitempty"`
	Media              string   `json:"media,omitempty"`
	Receiver           string   `json:"receiver,omitempty"`
	SentVia            string   `json:"sent_via,omitempty"`
	Status             string   `json:"status,omitempty"`
	AdminFkid          int      `json:"admin_fkid,omitempty"`
	Attachments        []string `json:"attachments,omitempty"`
	NotificationDetail string   `json:"notification_detail,omitempty"`
}

func (s ScheduledMessage) ToMap() map[string]interface{} {
	attachments := make([]interface{}, len(s.Attachments))
	for i, v := range s.Attachments {
		attachments[i] = v
	}

	return map[string]interface{}{
		"id":                  s.ID,
		"identifier_id":       s.IdentifierID,
		"title":               s.Title,
		"message":             s.Message,
		"time_deliver":        s.TimeDeliver,
		"time_sent":           s.TimeSent,
		"data_created":        s.DataCreated,
		"media":               s.Media,
		"receiver":            s.Receiver,
		"sent_via":            s.SentVia,
		"status":              s.Status,
		"admin_fkid":          s.AdminFkid,
		"attachments":         attachments,
		"notification_detail": s.NotificationDetail,
	}
}
