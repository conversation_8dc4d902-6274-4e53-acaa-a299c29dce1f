package models

import "time"

type ProductEntity struct {
	ProductID                  int    `json:"product_id"`
	Name                       string `json:"name"`
	CatalogueType              string `json:"catalogue_type"`
	ProductTypeFkid            int    `json:"product_type_fkid"`
	ProductCategoryFkid        int    `json:"product_category_fkid"`
	ProductSubcategoryFkid     int    `json:"product_subcategory_fkid"`
	PurchaseReportCategoryFkid int    `json:"purchase_report_category_fkid"`
	UnitFkid                   int    `json:"unit_fkid"`
	StockManagement            int    `json:"stock_management"`
	Barcode                    string `json:"barcode"`
	Sku                        string `json:"sku"`
	Photo                      string `json:"photo"`
	AdminFkid                  int    `json:"admin_fkid"`
	DataCreated                int64  `json:"data_created"`
	DataModified               int64  `json:"data_modified"`
	Description                string `json:"description"`
}

type ProductWithDetailAndVariant struct {
	ProductEntity
	ProductDetailEntity
	ProductVariantEntity
}

func (p *ProductEntity) ToMap() map[string]interface{} {
	result := map[string]interface{}{
		"product_id":                    p.ProductID,
		"name":                          p.Name,
		"catalogue_type":                p.CatalogueType,
		"product_type_fkid":             p.ProductTypeFkid,
		"product_category_fkid":         p.ProductCategoryFkid,
		"product_subcategory_fkid":      p.ProductSubcategoryFkid,
		"purchase_report_category_fkid": p.PurchaseReportCategoryFkid,
		"unit_fkid":                     p.UnitFkid,
		"stock_management":              p.StockManagement,
		"barcode":                       p.Barcode,
		"sku":                           p.Sku,
		"photo":                         p.Photo,
		"admin_fkid":                    p.AdminFkid,
		"description":                   p.Description,
		"data_created":                  p.DataCreated,
		"data_modified":                 p.DataModified,
	}

	if p.DataCreated == 0 {
		result["data_created"] = time.Now().UnixMilli()
	}

	if p.DataModified == 0 {
		result["data_modified"] = time.Now().UnixMilli()
	}

	return result
}
