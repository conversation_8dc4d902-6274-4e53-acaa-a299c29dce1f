package models

import "time"

type ProductDetailEntity struct {
	ProductDetailID        int     `json:"product_detail_id"`
	ProductFkid            int     `json:"product_fkid"`
	OutletFkid             int     `json:"outlet_fkid"`
	PriceBuyStart          int     `json:"price_buy_start"`
	PriceBuy               int     `json:"price_buy"`
	PriceSell              int     `json:"price_sell"`
	Voucher                string  `json:"voucher"`
	Discount               string  `json:"discount"`
	Active                 string  `json:"active"`
	TransferMarkupType     string  `json:"transfer_markup_type"`
	TransferMarkup         float32 `json:"transfer_markup"`
	CommissionStaffType    string  `json:"commission_staff_type"`
	CommissionStaff        float32 `json:"commission_staff"`
	CommissionCustomerType string  `json:"commission_customer_type"`
	CommissionCustomer     float32 `json:"commission_customer"`
	DataModified           int64   `json:"data_modified"`
	VariantFkid            int     `json:"variant_fkid"`
	Stock                  string  `json:"stock"`
	StockQty               int     `json:"stock_qty"`
	DataStatus             string  `json:"data_status"`
}

func (p *ProductDetailEntity) ToMap() map[string]interface{} {
	result := map[string]interface{}{
		"product_detail_id":        p.ProductDetailID,
		"product_fkid":             p.ProductFkid,
		"outlet_fkid":              p.OutletFkid,
		"price_buy_start":          p.PriceBuyStart,
		"price_buy":                p.PriceBuy,
		"price_sell":               p.PriceSell,
		"voucher":                  p.Voucher,
		"discount":                 p.Discount,
		"active":                   p.Active,
		"transfer_markup_type":     p.TransferMarkupType,
		"transfer_markup":          p.TransferMarkup,
		"commission_staff_type":    p.CommissionStaffType,
		"commission_staff":         p.CommissionStaff,
		"commission_customer_type": p.CommissionCustomerType,
		"commission_customer":      p.CommissionCustomer,
		"variant_fkid":             p.VariantFkid,
		"stock":                    p.Stock,
		"stock_qty":                p.StockQty,
	}

	if p.DataStatus == "" {
		result["data_status"] = "on"
	} else {
		result["data_status"] = p.DataStatus
	}

	if p.DataModified == 0 {
		result["data_modified"] = time.Now().UnixMilli()
	} else {
		result["data_modified"] = p.DataModified
	}

	return result
}
