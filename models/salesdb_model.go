package models

type SalesDb struct {
	CustomerName   string `json:"customer_name"`
	DataStatus     string `json:"data_status"`
	DateCreated    string `json:"date_created"`
	DateModified   string `json:"date_modified"`
	DiningTable    string `json:"dining_table"`
	Discount       int    `json:"discount"`
	DiscountInfo   string `json:"discount_info"`
	DisplayNota    string `json:"display_nota"`
	EmployeeFkid   int    `json:"employee_fkid"`
	GrandTotal     int    `json:"grand_total"`
	OpenShiftFkid  int    `json:"open_shift_fkid"`
	OutletFkid     int    `json:"outlet_fkid"`
	Payment        string `json:"payment"`
	QtyCustomers   int    `json:"qty_customers"`
	SalesID        string `json:"sales_id"`
	Status         string `json:"status"`
	TimeCreated    int64  `json:"time_created"`
	TimeModified   int64  `json:"time_modified"`
	TimePrediction int    `json:"time_prediction"`
	Voucher        int    `json:"voucher"`
	VoucherInfo    string `json:"voucher_info"`
	SalesDetail    []SalesDetail `json:"sales_detail"`
}

type SalesDetail struct {
	Discount          int    `json:"discount"`
	DiscountInfo      string `json:"discount_info"`
	EmployeeFkid      int    `json:"employee_fkid"`
	Parent            int    `json:"parent"`
	Price             int    `json:"price"`
	ProductDetailFkid int    `json:"product_detail_fkid"`
	ProductFkid       int    `json:"product_fkid"`
	Qty               int    `json:"qty"`
	SalesDetailID     int    `json:"sales_detail_id"`
	SalesFkid         string `json:"sales_fkid"`
	SubTotal          int    `json:"sub_total"`
	TimeCreated       int64  `json:"time_created"`
}

