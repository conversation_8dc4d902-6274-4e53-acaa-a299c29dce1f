package models

type Printer struct {
	Mac<PERSON>ddress                string `json:"mac_address"`
	AdminFkid                 int    `json:"admin_fkid"`
	PrinterName               string `json:"printer_name"`
	OutletFkid                int    `json:"outlet_fkid"`
	TimeCreated               int64  `json:"time_created"`
	TimeModified              int64  `json:"time_modified"`
	Type                      string `json:"type"`
	SettingClosingshift       string `json:"setting_closingshift"`
	SettingPrintlabel         string `json:"setting_printlabel"`
	SettingPrintorder         string `json:"setting_printorder"`
	SettingPrintpapersize     int    `json:"setting_printpapersize"`
	SettingPrintreceipt       string `json:"setting_printreceipt"`
	SettingPrintreceiptJumlah string `json:"setting_printreceipt_jumlah"`
}
