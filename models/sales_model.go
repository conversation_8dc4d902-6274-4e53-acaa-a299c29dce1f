package models

type Sale struct {
	Customer        string         `json:"customer,omitempty"`
	MemberId        string         `json:"memberId,omitempty"`
	MemberDetail    Member         `json:"memberDetail,omitempty"`
	Taxes           []Taxes        `json:"taxes,omitempty"`
	Discount        Discount       `json:"discount,omitempty"`
	EmployeeID      int            `json:"employeeID,omitempty"`
	EmployeeName    string         `json:"employeeName,omitempty"`
	Table           string         `json:"table,omitempty"`
	NoNota          string         `json:"noNota,omitempty"`
	OpenShiftID     int            `json:"openShiftId,omitempty"`
	OrderList       []Order        `json:"orderList,omitempty"`
	OutletID        int            `json:"outletID,omitempty"`
	OutletName      string         `json:"outletName,omitempty"`
	Payment         string         `json:"payment,omitempty"`
	Payments        []Payment      `json:"payments,omitempty"`
	Status          string         `json:"status,omitempty"`
	Synced          bool           `json:"synced,omitempty"`
	TimeModified    int64          `json:"timeModified,omitempty"`
	TimeCreated     int64          `json:"timeCreated,omitempty"`
	CustomersQty    int            `json:"customersQty,omitempty"`
	GrandTotal      int            `json:"grandTotal,omitempty"`
	TimePrediction  int            `json:"timePrediction,omitempty"`
	DisplayNota     string         `json:"displayNota,omitempty"`
	ReceiptReceiver string         `json:"receiptReceiver,omitempty"`
	Promotions      []Promotion    `json:"promotions,omitempty"`
	UniqueCode      int            `json:"unique_code,omitempty"` //for order_sales
	SalesTag        SalesTagEntity `json:"sales_tag,omitempty"`
	CustomFields    []CustomFields `json:"custom_fields,omitempty"`
}

type CustomFields struct {
	SettingTransactionFkid int    `json:"setting_transaction_fkid"`
	Label                  string `json:"label"`
	Value                  string `json:"value"`
}

type Taxes struct {
	Category string `json:"category"`
	ID       int    `json:"id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	Total    int    `json:"total"`
	Value    int    `json:"value"`
}

type Discount struct {
	VoucherType     string `json:"voucherType"`
	DiscountInfo    string `json:"discountInfo"`
	VoucherInfo     string `json:"voucherInfo"`
	DiscountType    string `json:"discountType"`
	Voucher         int    `json:"voucher"`
	DiscountNominal int    `json:"discountNominal"`
	VoucherNominal  int    `json:"voucherNominal"`
	Discount        int    `json:"discount"`
}

type Order struct {
	Discount           Discount       `json:"discount"`
	Extra              []Order        `json:"extra"`
	Taxes              []Taxes        `json:"taxes"`
	Product            Product        `json:"product"`
	Member             Member         `json:"member"`
	TmpID              int64          `json:"tmpId"`
	Price              int            `json:"price"`
	PriceAdd           int            `json:"priceAdd"`
	Printed            int            `json:"printed"`
	IsHold             bool           `json:"isHold"`
	Qty                int            `json:"qty"`
	SubTotal           int            `json:"subTotal"`
	IsItemVoid         bool           `json:"isItemVoid"`
	HoldQty            int            `json:"holdQty"`
	VoidedQty          int            `json:"voidedQty"`
	VoidInfo           string         `json:"voidInfo"`
	EmployeeId         int            `json:"employeeId"`
	VoidEmployeeAuthId int            `json:"voidEmployeeAuthId"`
	Promotion          PromotionSales `json:"promotion"`
	Note               string         `json:"note"`
	Info               string         `json:"info"`
	VoidParentId       int64          `json:"voidParentId"`
	ExtraType          string         `json:"extraType"`
}

type PromotionSales struct {
	PromotionId       int `json:"promotion_id"`
	PromotionTypeFkid int `json:"promotion_type_fkid"`
	Discount          int `json:"discount"`
	PromotionValue    int `json:"promotion_value"`
}

type Product struct {
	Active                     string      `json:"active,omitempty"`
	AdminFkid                  int         `json:"admin_fkid,omitempty"`
	Barcode                    interface{} `json:"barcode,omitempty"`
	CatalogueType              string      `json:"catalogue_type,omitempty"`
	DataCreated                int64       `json:"data_created,omitempty"`
	DataModified               int64       `json:"data_modified,omitempty"`
	DataStatus                 string      `json:"data_status,omitempty"`
	Discount                   string      `json:"discount,omitempty"`
	Name                       string      `json:"name,omitempty"`
	OutletFkid                 int         `json:"outlet_fkid,omitempty"`
	PriceBuy                   int         `json:"price_buy,omitempty"`
	PriceBuyStart              int         `json:"price_buy_start,omitempty"`
	PriceSell                  int         `json:"price_sell,omitempty"`
	ProductCategoryFkid        int         `json:"product_category_fkid,omitempty"`
	ProductDetailID            int         `json:"product_detail_id,omitempty"`
	ProductDetailFkid          int         `json:"product_detail_fkid,omitempty"`
	ProductFkid                int         `json:"product_fkid,omitempty"`
	ProductID                  int         `json:"product_id,omitempty"`
	ProductSubcategoryFkid     int         `json:"product_subcategory_fkid,omitempty"`
	ProductTypeFkid            int         `json:"product_type_fkid,omitempty"`
	PurchaseReportCategoryFkid int         `json:"purchase_report_category_fkid,omitempty"`
	Sku                        string      `json:"sku,omitempty"`
	StockManagement            int         `json:"stock_management,omitempty"`
	UnitFkid                   int         `json:"unit_fkid,omitempty"`
	Voucher                    string      `json:"voucher,omitempty"`
	VariantFkid                int         `json:"variant_fkid,omitempty"`
}

type Payment struct {
	Info    string `json:"info"`
	Method  string `json:"method"`
	Total   int    `json:"total"`
	Pay     int    `json:"pay"`
	Bank    Bank   `json:"bank,omitempty"`
	DueDate int64  `json:"due_date"`
}

type Bank struct {
	AccountNumber string `json:"account_number,omitempty"`
	AdminFkid     int    `json:"admin_fkid,omitempty"`
	BankID        int    `json:"bank_id,omitempty"`
	DataCreated   string `json:"data_created,omitempty"`
	DataModified  string `json:"data_modified,omitempty"`
	DataStatus    string `json:"data_status,omitempty"`
	Name          string `json:"name,omitempty"`
	NoRekening    string `json:"no_rekening,omitempty"`
	Provider      string `json:"provider,omitempty"`
	ProviderKey   string `json:"provider_key,omitempty"`
	Owner         string `json:"owner,omitempty"`
}

type Member struct {
	Address     string `json:"address"`
	DateOfBirth int64  `json:"date_of_birth"`
	Email       string `json:"email"`
	MemberID    string `json:"member_id"`
	Name        string `json:"name"`
	Phone       string `json:"phone"`
	TypeFkid    int    `json:"type_fkid"`
	Source      string `json:"source"`
}

type Promotion struct {
	Code              string          `json:"code,omitempty"`
	Name              string          `json:"name,omitempty"`
	Source            string          `json:"source,omitempty"`
	Type              string          `json:"type,omitempty"`
	TypeId            int             `json:"type_id,omitempty"`
	Value             interface{}     `json:"value,omitempty"`
	MinOrder          int             `json:"min_order,omitempty"`
	PromoNominal      int             `json:"promo_nominal,omitempty"`
	PromoDiscountType string          `json:"promo_discount_type,omitempty"`
	PromotionId       int             `json:"promotion_id,omitempty"`
	PromotionDetail   PromotionDetail `json:"promo,omitempty"`
	DiscountType      string          `json:"discount_type,omitempty"`
}

type PromotionDetail struct {
	Products []PromotionProduct `json:"products"`
}

type PromotionProduct struct {
	ProductDetailFkid int `json:"product_detail_fkid"`
}

func (b Bank) ToMap() map[string]interface{} {
	bankMap := make(map[string]interface{})
	bankMap["admin_fkid"] = b.AdminFkid
	bankMap["bank_id"] = b.BankID
	bankMap["data_created"] = b.DataCreated
	bankMap["data_modified"] = b.DataModified
	bankMap["data_status"] = b.DataStatus
	bankMap["name"] = b.Name
	bankMap["no_rekening"] = b.NoRekening

	if b.Provider != "" {
		bankMap["provider"] = b.Provider
	}
	if b.ProviderKey != "" {
		bankMap["provider_payment_key"] = b.ProviderKey
	}
	if b.Owner != "" {
		bankMap["owner"] = b.Owner
	}

	return bankMap
}
