package models

type ProductVariantEntity struct {
	VariantID      int    `json:"variant_id"`
	VariantName    string `json:"variant_name"`
	VariantSku     string `json:"variant_sku"`
	VariantBarcode string `json:"variant_barcode"`
	ProductFkid    int    `json:"product_fkid"`
	AdminFkid      int    `json:"admin_fkid"`
	DataCreated    int64  `json:"data_created"`
	DataModified   int64  `json:"data_modified"`
	DataStatus     int    `json:"data_status"`
}
