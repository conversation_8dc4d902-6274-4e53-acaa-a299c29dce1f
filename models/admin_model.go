package models

type Admin struct {
	AdminId  string `json:"admin_id"`
	Password string `json:"password"`
}

type AdminEntity struct {
	AdminID      int    `json:"admin_id,omitempty"`
	BusinessName string `json:"business_name,omitempty"`
	Name         string `json:"name,omitempty"`
	Email        string `json:"email,omitempty"`
	DataCreated  int64  `json:"data_created,omitempty"`
	Phone        string `json:"phone,omitempty"`
	Photo        string `json:"photo,omitempty"`
	BusinessType string `json:"business_type,omitempty"`
	AccountID    int    `json:"account_id,omitempty"`
}
