package models

type SalesEntity struct {
	SalesID         string `json:"sales_id,omitempty"`
	DisplayNota     string `json:"display_nota,omitempty"`
	Payment         string `json:"payment,omitempty"`
	DiningTable     string `json:"dining_table,omitempty"`
	CustomerName    string `json:"customer_name,omitempty"`
	MemberFkid      int    `json:"member_fkid,omitempty"`
	QtyCustomers    int    `json:"qty_customers,omitempty"`
	DataStatus      string `json:"data_status,omitempty"`
	EmployeeFkid    int    `json:"employee_fkid,omitempty"`
	OutletFkid      int    `json:"outlet_fkid,omitempty"`
	Status          string `json:"status,omitempty"`
	TimePrediction  int    `json:"time_prediction,omitempty"`
	OpenShiftFkid   int    `json:"open_shift_fkid,omitempty"`
	DateCreated     string `json:"date_created,omitempty"`
	DateModified    string `json:"date_modified,omitempty"`
	TimeCreated     int64  `json:"time_created,omitempty"`
	TimeModified    int64  `json:"time_modified,omitempty"`
	Discount        int    `json:"discount,omitempty"`
	DiscountInfo    string `json:"discount_info,omitempty"`
	Voucher         int    `json:"voucher,omitempty"`
	VoucherInfo     string `json:"voucher_info,omitempty"`
	GrandTotal      int    `json:"grand_total,omitempty"`
	ReceiptReceiver string `json:"receipt_receiver,omitempty"`
	PointEarned     int    `json:"point_earned,omitempty"`
	SalesTagFkid    int    `json:"sales_tag_fkid,omitempty"`
}