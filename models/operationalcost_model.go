package models

type OperationalCost struct {
	DataCreated         string      `json:"data_created"`
	DataStatus          string      `json:"data_status"`
	Harga               int         `json:"harga"`
	Keterangan          string      `json:"keterangan"`
	OpcostName          string      `json:"opcost_name"`
	OperationalcostID   interface{} `json:"operationalcost_id"`
	OutletFkid          int         `json:"outlet_fkid"`
	PrcCategoryFkid     interface{} `json:"prc_category_fkid"`
	SubCategoryFkid     int         `json:"sub_category_fkid"`
	PurcaseCategoryName string      `json:"purcase_category_name"`
	Qty                 int         `json:"qty"`
	SupplierFkid        interface{} `json:"supplier_fkid"`
	SupplierName        string      `json:"supplier_name"`
	TimeCreated         int64       `json:"time_created"`
	Total               int         `json:"total"`
	UserFkid            int         `json:"user_fkid"`
	ShiftFkid           interface{} `json:"shift_fkid"`
	EmployeeFkid        int         `json:"employee_fkid"`
	Payment             string      `json:"payment"`
	PaymentBankFkid     interface{} `json:"payment_bank_fkid"`
}
