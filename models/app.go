package models

type CrmAppInfo struct {
	Ios     Ios     `json:"ios,omitempty"`
	Android Android `json:"android,omitempty"`
	Web     Web     `json:"web,omitempty"`
}
type Ios struct {
	BundleID   string `json:"bundle_id,omitempty"`
	AppStoreId string `json:"app_store_id,omitempty"`
}
type Android struct {
	PackageName string `json:"package_name"`
}

type Web struct {
	Url string `json:"url,omitempty"`
}

type CrmAppConfig struct {
	Asset struct {
		Color struct {
			Accent      string `json:"accent,omitempty"`
			Primary     string `json:"primary,omitempty"`
			Secondary   string `json:"secondary,omitempty"`
			PrimaryDark string `json:"primary_dark,omitempty"`
		} `json:"color,omitempty"`
		AppIcon               string `json:"app_icon,omitempty"`
		AppBackground         string `json:"app_background,omitempty"`
		ToolbarBackground     string `json:"toolbar_background,omitempty"`
		ProfileCardBackground string `json:"profile_card_background,omitempty"`
	} `json:"asset,omitempty"`
	Contact struct {
		Whatsapp string `json:"whatsapp,omitempty"`
	} `json:"contact,omitempty"`
	Language struct {
		Point  string `json:"point,omitempty"`
		Outlet string `json:"outlet,omitempty"`
	} `json:"language,omitempty"`
	SocialMedia struct {
		Tiktok    string `json:"tiktok,omitempty"`
		Twitter   string `json:"twitter,omitempty"`
		Instagram string `json:"instagram,omitempty"`
	} `json:"social_media,omitempty"`
}
