package models

type BehaveVoucherResp struct {
	Result   VoucherResult `json:"result"`
	Status   string        `json:"status"`
	Message  string        `json:"message"`
	Messages string        `json:"messages"`
}

type VoucherResult struct {
	Voucher map[string]interface{} `json:"voucher"`
}

type Birthday struct {
	Date         string `json:"date"`
	TimezoneType int    `json:"timezone_type"`
	Timezone     string `json:"timezone"`
}

type RegisterDate struct {
	Date         string `json:"date"`
	TimezoneType int    `json:"timezone_type"`
	Timezone     string `json:"timezone"`
}

type MemberBehave struct {
	MemberId      string        `json:"member_id"`
	Source        string        `json:"source"`
	UID           string        `json:"uid"`
	Name          string        `json:"name"`
	Phone         string        `json:"phone"`
	Gender        string        `json:"gender"`
	Birthday      Birthday      `json:"birthday"`
	RegisterDate  RegisterDate  `json:"register_date"`
	City          string        `json:"city"`
	Vouchers      []interface{} `json:"vouchers"`
	CustomerLevel string        `json:"customer_level"`
	//PromoID       interface{}   `json:"promo_id"`
	Saldo         int           `json:"saldo"`
}
