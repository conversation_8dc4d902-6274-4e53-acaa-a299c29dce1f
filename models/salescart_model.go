package models

const (
	CartStatusPaid    = "paid"
	CartStatusPending = "pending"
	CartStatusExpire  = "expire"
)

type SalesCart struct {
	NoNota       string `json:"no_nota,omitempty"`
	OutletFkid   int    `json:"outlet_fkid,omitempty"`
	Sales        string `json:"sales,omitempty"`
	Status       string `json:"status,omitempty"`
	TimeModified int64  `json:"time_modified,omitempty"`
	TimeCreated  int64  `json:"time_created,omitempty"`
	GrandTotal   int    `json:"grand_total,omitempty"`
}

type SalesCartPaymentEntity struct {
	ID            int64  `db:"id" json:"id,omitempty"`
	TmpSalesFkid  string `db:"tmp_sales_fkid" json:"tmp_sales_fkid,omitempty"`
	TransactionID string `db:"transaction_id" json:"transaction_id,omitempty"`
	PaymentType   string `db:"payment_type" json:"payment_type,omitempty"`
	Status        string `db:"status" json:"status,omitempty"`
	Payload       string `db:"payload" json:"payload,omitempty"`
	TimeCreated   int64  `db:"time_created" json:"time_created,omitempty"`
}

func (t *SalesCartPaymentEntity) ToMap() map[string]interface{} {
	m := make(map[string]interface{})
	if t.ID > 0 {
		m["id"] = t.ID
	}
	m["tmp_sales_fkid"] = t.TmpSalesFkid
	m["transaction_id"] = t.TransactionID
	m["payment_type"] = t.PaymentType
	m["status"] = t.Status
	m["payload"] = t.Payload
	m["time_created"] = t.TimeCreated
	return m
}
