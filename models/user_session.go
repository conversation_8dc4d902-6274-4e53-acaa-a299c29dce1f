package models

type UserSessionEntity struct {
	ID           string `json:"id,omitempty"`
	IPAddress    string `json:"ip_address,omitempty"`
	Timestamp    int64  `json:"timestamp,omitempty"`
	Data         string `json:"data,omitempty"`
	Token        string `json:"token,omitempty"`
	CreatedAt    int64  `json:"created_at,omitempty"`
	ExpiredAt    int64  `json:"expired_at,omitempty"`
	DateModified string `json:"date_modified,omitempty"`
}

func (u UserSessionEntity) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"id":            u.ID,
		"ip_address":    u.IPAddress,
		"timestamp":     u.Timestamp,
		"data":          u.Data,
		"token":         u.To<PERSON>,
		"created_at":    u.CreatedAt,
		"expired_at":    u.ExpiredAt,
		"date_modified": u.DateModified,
	}
}
