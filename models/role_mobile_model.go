package models

type RoleMobile struct {
	Inputkasmasuk       bool `json:"inputkasmasuk"`
	Inputkas<PERSON>uar      bool `json:"inputkaskeluar"`
	Inputpembelian      bool `json:"inputpembelian"`
	SettingPrinter      bool `json:"setting_printer"`
	Tutupkasir          bool `json:"tutupkasir"`
	ReprintNota         bool `json:"reprint_nota"`
	ReprintOrder        bool `json:"reprint_order"`
	ReprintTutupkasir   bool `json:"reprint_tutupkasir"`
	Gantiharga          bool `json:"gantiharga"`
	Gantidiskonperbill  bool `json:"gantidiskonperbill"`
	<PERSON><PERSON><PERSON><PERSON><PERSON>        bool `json:"buka<PERSON><PERSON>uang"`
	Pembayaran          bool `json:"pembayaran"`
	Compliment          bool `json:"compliment"`
	Voucher             bool `json:"voucher"`
	Gantivoucherperitem bool `json:"gantivoucherperitem"`
	Simpankeorderlist   bool `json:"simpankeorderlist"`
	Duty                bool `json:"duty"`
	Gantidiskonperitem  bool `json:"gantidiskonperitem"`
	Gantipajak          bool `json:"gantipajak"`
	ReprintRefund       bool `json:"reprint_refund"`
	Refund              bool `json:"refund"`
	ReprintVoid         bool `json:"reprint_void"`
	Inputbarangrusak    bool `json:"inputbarangrusak"`
	Inputbaranghabis    bool `json:"inputbaranghabis"`
	Inputpromo          bool `json:"inputpromo"`
	MasterLogin 		bool `json:"masterlogin"`
}
