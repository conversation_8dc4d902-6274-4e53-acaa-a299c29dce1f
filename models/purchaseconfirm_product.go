package models

type PurchaseConfirm struct {
	DataStatus          string `json:"data_status"`
	DateCreated         int64  `json:"date_created"`
	DateUpdated         int64  `json:"date_updated"`
	PurchaseConfrimID   int    `json:"purchase_confrim_id"`
	PurchaseProductFkid int    `json:"purchase_product_fkid"`
	Qty                 int    `json:"qty"`
	QtyArive            int    `json:"qty_arive"`
	QtyNotConfrim       int    `json:"qty_notConfrim"`
	Retur               int    `json:"retur"`
	User                string `json:"user"`
}
