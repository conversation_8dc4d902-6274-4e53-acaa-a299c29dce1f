## Project Overview

This is an enterprise-grade Point of Sale API system that provides comprehensive functionality for managing sales, inventory, employees, and store operations.

## Architecture

The project follows Clean Architecture patterns with clear separation of concerns:

### Directory Structure
```
├── cmd/          # Command line tools and utilities
├── config/       # Configuration and credentials
├── controllers/  # HTTP request handlers (v1, v2) - DEPRECATED.
├── core/         # Core infrastructure and utilities
├── domain/       # Domain models and business logic interfaces
│   ├── account.go         # Account domain model and interfaces
│   ├── auth.go           # Authentication domain entities
│   ├── billing.go        # Billing and payment domain models
│   ├── employee.go       # Employee management entities
│   ├── product_entity.go # Product domain models
│   ├── sales.go         # Sales domain entities
│   └── ...              # Other domain models and interfaces
├── integration/  # Integration test suites
├── job/          # Background jobs and schedulers
├── models/       # Data models and database entities
├── module/       # Business logic implementation modules
│   ├── auth/           # Authentication module
│   │   ├── delivery/   # HTTP handlers
│   │   ├── repository/ # Data access layer
│   │   ├── usecase/    # Business logic
│   │   ├── repository.go    # Interfaces for repository
│   │   └── usecase.go       # Interfaces for usecase
│   ├── billing/        # Billing and payment processing
│   ├── employee/       # Employee management
│   ├── product/        # Product management
│   ├── sales/         # Sales operations
│   └── ...           # Other business modules
├── routers/      # API route definitions
└── test/         # Test files
```

### Architectural Flow

The project follows a clean architecture pattern with strict dependency rules and separation of concerns within each module:

1. **Module Structure**
   Each module (e.g., auth, billing, product) follows this structure:
   ```
   module/
   ├── delivery/     # HTTP handlers
   ├── repository/   # Data access implementation
   ├── usecase/      # Business logic implementation
   ├── repository.go # Repository interfaces
   └── usecase.go    # Usecase interfaces
   ```

2. **Request Flow**
   - Client requests are handled by the `delivery` layer
   - `delivery` handlers must not implement business logic directly
   - Instead, they call methods defined in the module's `usecase.go` interface
   - All business logic and validations are implemented in the `usecase` layer

3. **Business Logic (Usecase)**
   - Contains all business logic and request validation
   - Implements interfaces defined in `usecase.go`
   - Can call multiple repository methods if needed
   - Can use goroutines for parallel data operations
   - Responsible for data manipulation and business rules

4. **Data Access (Repository)**
   - Repository implementations must be simple and focused
   - Queries should be as simple as possible
   - Complex data manipulations should be done in usecase, not repository
   - Implements interfaces defined in `repository.go`
   - Should only focus on data retrieval/storage operations

5. **Database Query Patterns**
   - Use parameterized queries with named parameters for better readability and safety
   ```sql
   SELECT * FROM sales WHERE sales_ids IN @ids AND outlet_fkid = @outletId
   ```
   - Utilize the `db.MapParam` helper to transform named parameters into prepared statements:
   ```go
   sql, params := db.MapParam(sql, map[string]any{
       "ids":      []int{1,2},
       "outletId": 9,
   })
   // Transforms to: SELECT * FROM sales WHERE sales_ids IN (?,?) AND outlet_fkid = ?
   ```
   - For loading data into structs, use the standard pattern:
   ```go
   var result models.SalesCart
   err := s.db.Set(sql, params...).Get(&result)
   ```
   - `db.MapParam` automatically handles slices by expanding IN clauses with the correct number of placeholders
   - Use named parameters (@param) in queries for better maintainability and readability
   - Prefer multiple simple queries in usecase over complex joins in repository

6. **Design Principles**
   - Each layer can only communicate through interfaces
   - Repository operations should be atomic and simple
   - Complex operations should be handled in usecase layer
   - Multiple repository calls are preferred over complex queries
   - Goroutines can be used in usecase for parallel operations
