package domain

import "gitlab.com/uniqdev/backend/api-pos/models"

type Product struct{}

type ProductAddRequest struct {
	OutletId int                  `json:"outlet_id,omitempty"`
	Products []ProductItemRequest `json:"products,omitempty"`
}

type ProductItemRequest struct {
	Name                       string `json:"name,omitempty"`
	CategoryName               string `json:"category_name,omitempty"`
	Price                      int    `json:"price,omitempty"`
	StockManagement            int    `json:"stock_management,omitempty"`
	SubCategoryName            string `json:"sub_category_name,omitempty"`
	ProductTypeName            string `json:"product_type_name,omitempty"`
	PurchaseReportCategoryName string `json:"purchase_report_category_name,omitempty"`
	UnitName                   string `json:"unit_name,omitempty"`
	PriceBuy                   int    `json:"price_buy,omitempty"`
	Sku                        string `json:"sku,omitempty"`
	Barcode                    string `json:"barcode,omitempty"`
}

type RequestParam struct {
	OutletId         int
	LastSync         int64
	LastDataModified string
}

type ProductAddParam struct {
	models.ProductEntity
}
