package domain

type Employee struct{}

type EmployeePosition struct {
	JabatanId   int    `json:"jabatan_id"`
	Name        string `json:"name"`
	Level       int    `json:"level"`
	Role        string `json:"role"`
	RoleMobile  string `json:"role_mobile"`
	AdminFkid   int    `json:"admin_fkid"`
	DataCreated string `json:"data_created"`
	DataStatus  string `json:"data_status"`
}

type EmployeeUseCase interface {
	FetchEmployeePosition(userSession UserSession) ([]EmployeePosition, error)
}

type EmployeeRepository interface {
	FetchEmployeePosition(userSession UserSession) ([]EmployeePosition, error)
}
