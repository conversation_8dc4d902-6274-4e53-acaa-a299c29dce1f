package domain

type ProductEntity struct {
	ProductID                  int64   `gorm:"column:product_id;primaryKey;autoIncrement" json:"product_id"`
	Name                       string  `gorm:"column:name;type:varchar(50);not null" json:"name"`
	CatalogueType              string  `gorm:"column:catalogue_type;type:enum('equipment','ingridient','product','member','asset');not null" json:"catalogue_type"`
	ProductTypeFkid            *int64  `gorm:"column:product_type_fkid" json:"product_type_fkid"`
	ProductCategoryFkid        int64   `gorm:"column:product_category_fkid;not null" json:"product_category_fkid"`
	ProductSubcategoryFkid     int64   `gorm:"column:product_subcategory_fkid;not null" json:"product_subcategory_fkid"`
	AccountFkidPurchase        *int64  `gorm:"column:account_fkid_purchase" json:"account_fkid_purchase"`
	AccountFkidSales           *int64  `gorm:"column:account_fkid_sales" json:"account_fkid_sales"`
	PurchaseReportCategoryFkid *int64  `gorm:"column:purchase_report_category_fkid" json:"purchase_report_category_fkid"`
	UnitFkid                   int64   `gorm:"column:unit_fkid;not null" json:"unit_fkid"`
	StockManagement            bool    `gorm:"column:stock_management;not null" json:"stock_management"`
	Barcode                    *string `gorm:"column:barcode;type:varchar(30);uniqueIndex:unique_products_barcode,priority:1" json:"barcode"`
	SKU                        *string `gorm:"column:sku;type:varchar(30);uniqueIndex:unique_products_sku,priority:1" json:"sku"`
	Photo                      *string `gorm:"column:photo;type:varchar(250)" json:"photo"`
	AdminFkid                  int64   `gorm:"column:admin_fkid;not null;uniqueIndex:unique_products_barcode,priority:2;uniqueIndex:unique_products_sku,priority:2" json:"admin_fkid"`
	DataCreated                int64   `gorm:"column:data_created;not null" json:"data_created"`
	DataModified               int64   `gorm:"column:data_modified;not null" json:"data_modified"`
	DataStatus                 string  `gorm:"column:data_status;type:enum('on','off');not null" json:"data_status"`
	Description                *string `gorm:"column:description;type:text" json:"description"`
	AppShow                    *bool   `gorm:"column:app_show;default:1" json:"app_show"`
	MinQtyOrder                uint    `gorm:"column:min_qty_order;not null;default:1" json:"min_qty_order"`
	MinQtyOrder2               *int    `gorm:"column:min_qty_order2" json:"min_qty_order2"`
}

// TableName specifies the table name for the entity
func (ProductEntity) TableName() string {
	return "products"
}
