package domain

import (
	"strings"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
)

type UserSession struct {
	AdminId             int64
	DeviceId            string
	Role                string
	Insurer             string
	AuthorizedOutletIds []int
}

func UserSessionFastHttp(ctx *fasthttp.RequestCtx) UserSession {
	ids := strings.Split(string(ctx.Request.Header.Peek("outlet")), ",")
	authorizedIds := make([]int, 0)
	for _, id := range ids {
		authorizedIds = append(authorizedIds, utils.ToInt(id))
	}

	return UserSession{
		AdminId:             utils.ToInt64(ctx.Request.Header.Peek("admin_id")),
		DeviceId:            string(ctx.Request.Header.Peek("Device")),
		AuthorizedOutletIds: authorizedIds,
		Role:                string(ctx.Request.Header.Peek("role")),
		Insurer:             string(ctx.Request.Header.Peek("insurer")),
	}
}
