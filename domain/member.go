package domain

import "gitlab.com/uniqdev/backend/api-pos/models"

type Member struct {
	Name  string `json:"name"`
	Phone string `json:"phone"`
}
type MemberUseCase interface {
	AddMember(member Member, user UserSession) (map[string]interface{}, error)
	FetchMember(request models.MemberRequest, user UserSession) ([]models.MemberResponse, error)
}
type MemberRepository interface {
	FetchMemberByPhone(phone string) (map[string]interface{}, error)
	FetchMemberById(memberId int64, adminId int64) (map[string]interface{}, error)
	FetchMember(request models.MemberRequest, user UserSession) ([]models.MemberResponse, error)

	AddMember(member Member, user UserSession) (int64, error)
	AddMemberToOtherBusiness(memberId int64, adminId int64) error
}
