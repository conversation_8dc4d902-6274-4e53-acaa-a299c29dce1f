package domain

type SettingTransaction struct {
	ID           int64       `json:"id"`
	OutletID     int         `json:"outlet_id"`
	Label        string      `json:"label"`
	Type         string      `json:"type"`
	OptionValues interface{} `json:"option_values"`
	IsRequired   bool        `json:"is_required"`
	DisplayOrder int16       `json:"display_order"`
	CreatedAt    int64       `json:"created_at"`
	UpdatedAt    int64       `json:"updated_at"`
}

type SettingTransactionFilter struct {
	CreatedAfter int64   `json:"created_after"`
	OutletIDs    []int64 `json:"outlet_ids"`
}

type SettingRepository interface {
	FetchTransactionSettings(adminID int, filter SettingTransactionFilter) ([]SettingTransaction, error)
}

type SettingUseCase interface {
	GetTransactionSettings(user UserSession, filter SettingTransactionFilter) ([]SettingTransaction, error)
}
