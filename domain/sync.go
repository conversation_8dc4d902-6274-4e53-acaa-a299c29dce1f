package domain

import (
	"encoding/json"
	"fmt"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
)

type SyncDataEntity struct {
	SyncDataID            int64  `json:"sync_data_id"`
	OutletSourceFKID      int    `json:"outlet_source_fkid"`
	OutletDestinationFKID int    `json:"outlet_destination_fkid"`
	StartDate             int64  `json:"start_date"`
	Shifts                string `json:"shifts"` // JSON string
	FilterType            string `json:"filter_type"`
	FilterDetail          string `json:"filter_detail"` // JSON string
	Status                string `json:"status"`
	LastSync              int64  `json:"last_sync,omitempty"` // Use a pointer for nullable fields
	SyncLog               string `json:"sync_log,omitempty"`
	DataCreated           int64  `json:"data_created"`
	DataModified          int64  `json:"data_modified"`
}

type SyncSalesFilter struct {
	OutletId        int   `json:"outlet_id"`
	StartDate       int64 `json:"start_date"`
	GrandTotalAbove int   `json:"grand_total_above"` //grand total greater than
	GrandTotalBelow int   `json:"grand_total_below"` //grand total lower than
	ShiftIds        []int `json:"shift_ids,omitempty"`
	TagIds          []int `json:"tag_ids,omitempty"`
}

type SyncSalesResult struct {
	RowNum         int    `json:"row_num"`
	SalesId        string `json:"sales_id"`
	TimeCreated    int64  `json:"time_created"`
	DisplayNota    string `json:"display_nota"`
	NewDisplayNota string
	NewSalesId     string
}

type Shift struct {
	ShiftId int `json:"shift_id"`
}

type SalesDetailProduct struct {
	SalesDetailID   int64 `json:"sales_detail_id"`
	ProductDetailID int64 `json:"product_detail_id"`
	OutletFKID      int   `json:"outlet_fkid"`
}

type ProductDetail struct {
	ProductDetailID int64
	OutletFKID      int
	// Add other fields as needed
}

func (s SyncDataEntity) FilterDetailToMap() (map[string]interface{}, error) {
	var filterDetailMap map[string]interface{}
	err := json.Unmarshal([]byte(s.FilterDetail), &filterDetailMap)
	return filterDetailMap, err
}

// getting start date which will be used to get sales data
func (s SyncDataEntity) PickStartDate() int64 {
	startDate := s.StartDate
	if s.SyncLog != "" {
		var syncLogMap map[string]interface{}
		err := json.Unmarshal([]byte(s.SyncLog), &syncLogMap)
		if lastSyncLog, ok := syncLogMap["last_sync"]; ok && err == nil {
			startDate = cast.ToInt64(lastSyncLog)
		}
	}
	return startDate
}

// getting []int shift ids from the json
func (s SyncDataEntity) GetShiftIds() ([]int, error) {
	var shiftMap []interface{}
	err := json.Unmarshal([]byte(s.Shifts), &shiftMap)
	if err != nil {
		fmt.Printf("can not unmarshal: %v\n", s.Shifts)
		return nil, err
	}

	ids := make([]int, 0)
	for _, row := range shiftMap {
		ids = append(ids, cast.ToInt(row))
	}
	return ids, nil
}

// getting the basic filter: OutletId, StartDate, ShiftIds
func (s SyncDataEntity) GetBasicFilter() (SyncSalesFilter, error) {
	startDate := s.PickStartDate()
	shiftIds, err := s.GetShiftIds()
	if err != nil {
		return SyncSalesFilter{}, err
	}

	return SyncSalesFilter{
		OutletId:  s.OutletSourceFKID,
		StartDate: startDate,
		ShiftIds:  shiftIds,
	}, nil
}
