package domain

type SelfOrder struct{}

type SelfOrderUseCase interface {
	FetchSelfOrder(outletId int, session UserSession) ([]map[string]interface{}, error)
	FetchSelfOrderByCode(code interface{}, userSession UserSession) (map[string]interface{}, error)
}

type SelfOrderRepository interface{
	FetchSelfOrder(outletId int, session UserSession) ([]map[string]interface{}, error)
	FetchSelfOrderByCode(code interface{}, session UserSession) (map[string]interface{}, error)}
