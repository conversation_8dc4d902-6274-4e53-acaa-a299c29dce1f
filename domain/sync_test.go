package domain

import (
	"reflect"
	"testing"
)

func TestGetShiftIds(t *testing.T) {
	t.Run("Valid JSON", func(t *testing.T) {
		syncData := SyncDataEntity{
			Shifts: `[{"shift_id": 1}, {"shift_id": 2}, {"shift_id": 3}]`,
		}

		expectedIds := []int{1, 2, 3}
		actualIds, err := syncData.GetShiftIds()
		if err != nil {
			t.<PERSON>("Unexpected error: %v", err)
		}
		if !reflect.DeepEqual(actualIds, expectedIds) {
			t.<PERSON>rf("Expected shift IDs: %v, got %v", expectedIds, actualIds)
		}
	})

	t.Run("Invalid JSON", func(t *testing.T) {
		syncData := SyncDataEntity{
			Shifts: `[{"shift_id": 1}, "invalid", {"shift_id": 3}]`,
		}

		_, err := syncData.GetShiftIds()
		if err == nil {
			t.<PERSON><PERSON>("Expected error, got nil")
		}
	})
}
