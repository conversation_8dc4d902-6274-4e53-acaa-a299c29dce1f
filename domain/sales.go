package domain

const (
	OrderStatusPending             = "pending"
	OrderStatusCancel              = "cancel"
	OrderStatusAccept              = "accept"
	OrderStatusReject              = "reject"
	OrderStatusPaymentVerification = "payment_verification"
	OrderStatusPaymentVerified     = "payment_verified"
	OrderStatusPaymentReject       = "payment_reject"
	OrderStatusReady               = "ready"
	OrderStatusTaken               = "taken"
	OrderStatusArrived             = "arrived"
	OrderStatusReceived            = "received"
	OrderStatusComplaint           = "complaint"
)

// Note: order index is important! as status transition must be in order (can go up)
var OrderStatusList = []string{
	OrderStatusPending,
	OrderStatusAccept,
	OrderStatusPaymentVerification,
	OrderStatusPaymentVerified,
	OrderStatusPaymentReject,
	OrderStatusReady,
	OrderStatusTaken,
	OrderStatusArrived,
	OrderStatusReceived,
	OrderStatusComplaint,
	OrderStatusReject,
	OrderStatusCancel, //move to the last index, as will marked as finished
}

type Sales struct{}

type OrderSalesReqParam struct {
	LastSync     int64  `json:"last_sync,omitempty"`
	OrderSalesId string `json:"order_sales_id,omitempty"`
}

type OrderSalesUpdate struct {
	Status     string `json:"status,omitempty"`
	EmployeeId int    `json:"employee_id,omitempty"`
	Message    string `json:"message,omitempty"`
}

type SalesTagRequest struct {
	OutletId int `json:"outlet_id,omitempty"`
}

type SalesRequest struct {
	OutletId int
	LastSync int64
}

// OrderSalesV2Request represents the request parameters for fetching order sales v2
type OrderSalesV2Request struct {
	OutletId int   `json:"outlet_id"`
	LastSync int64 `json:"last_sync"`
}

// OrderSalesV2Response represents the response structure for order sales v2 with member data
type OrderSalesV2Response struct {
	ID               int                    `json:"id,omitempty"`
	OrderSalesID     string                 `json:"order_sales_id,omitempty"`
	SalesFkid        interface{}            `json:"sales_fkid,omitempty"`
	MemberFkid       int                    `json:"member_fkid,omitempty"`
	OutletFkid       int                    `json:"outlet_fkid,omitempty"`
	OrderType        string                 `json:"order_type,omitempty"`
	OrderNote        string                 `json:"order_note,omitempty"`
	PickupTime       interface{}            `json:"pickup_time,omitempty"`
	Status           string                 `json:"status,omitempty"`
	TimeOrder        int64                  `json:"time_order,omitempty"`
	TimeModified     int64                  `json:"time_modified,omitempty"`
	TimeAcceptReject int64                  `json:"time_accept_reject,omitempty"`
	TimeReady        int                    `json:"time_ready,omitempty"`
	TimeTaken        int                    `json:"time_taken,omitempty"`
	Items            string                 `json:"items,omitempty"`
	RejectReason     interface{}            `json:"reject_reason,omitempty"`
	GrandTotal       int                    `json:"grand_total,omitempty"`
	PaymentInfo      string                 `json:"payment_info,omitempty"`
	Member           *OrderSalesMemberData  `json:"member,omitempty"`
	MetaData         map[string]interface{} `json:"meta_data,omitempty"`
}

// OrderSalesMemberData represents member information in order sales response
type OrderSalesMemberData struct {
	MemberID int64  `json:"member_id,omitempty"`
	Name     string `json:"name,omitempty"`
	Phone    string `json:"phone,omitempty"`
	Email    string `json:"email,omitempty"`
}
