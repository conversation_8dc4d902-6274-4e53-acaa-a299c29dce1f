package behave

import (
	"encoding/json"
	"fmt"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"os"
	"strconv"
	"time"
)

func SyncTransactionWithBehave() {
	localDb := db.GetLocalDb()
	var lastSync int64
	err := localDb.Get("last_sync_transaction", &lastSync)
	if err != nil {
		fmt.Println("Getting last sync transaction error - ", err)
		lastSync = 0
	}

	sql := `select sales_id, display_nota, from_unixtime(s.time_created/1000+25200, '%Y-%m-%d %H:%i:%s') as tgl,
  e.name as cashier, s.discount as discount_bill, s.voucher as voucher_bill, grand_total, qty, sd.product_detail_fkid,
  price, sub_total, sd.discount as discount_item, subcategory.name as subcategory, outlet_fkid, p.name as menu
from sales s
join sales_detail sd on sd.sales_fkid=s.sales_id
  join employee e on s.employee_fkid = e.employee_id
  join products p on sd.product_fkid = p.product_id
  join products_subcategory subcategory on p.product_subcategory_fkid = subcategory.product_subcategory_id
where s.status = 'success'
      and outlet_fkid in (select outlet_id from outlets where admin_fkid=1)
      and sd.sales_detail_id not  in (select sales_void.product_detail_fkid from sales_void)
      and s.time_created > 1543597200000 and s.time_created > ?
order by s.outlet_fkid`

	fmt.Println("last sync transaction : ", lastSync)

	lastQuery := time.Now().Unix() * 1000
	transactions, err := db.QueryArray(sql, lastSync)
	utils.CheckErr(err)

	if len(transactions) == 0 {
		fmt.Println("All transaction synced")
		return
	}

	data := make(map[string]interface{})
	transactionArrMap := make([]map[string]interface{}, 0)
	transactionMap := make(map[string]interface{}, 0)
	salesDetail := make([]map[string]interface{}, 0)
	lastOutletId := ""
	lastSalesId := ""
	errorCount := 0

	for _, transaction := range transactions {
		if lastOutletId == "" {
			lastOutletId = utils.ToString(transaction["outlet_fkid"])
		} else {
			if lastOutletId != utils.ToString(transaction["outlet_fkid"]) {
				outletInt, _ := strconv.Atoi(lastOutletId)
				data["store_code"] = fmt.Sprintf("%03d", outletInt)
				data["transaction"] = transactionArrMap
				_, err := SendRequest(TRANSACTION, data)
				if utils.CheckErr(err) {
					errorCount += 1
				}
				dataJson, _ := json.Marshal(transactionArrMap)
				fmt.Println("JSON : ", string(dataJson))
				transactionArrMap = make([]map[string]interface{}, 0)
			}
		}

		if lastSalesId == "" {
			lastSalesId = utils.ToString(transaction["sales_id"])
		} else {
			if lastSalesId != utils.ToString(transaction["sales_id"]) {
				transactionMap["menu"] = salesDetail

				transactionArrMap = append(transactionArrMap, transactionMap)

				transactionMap = make(map[string]interface{}, 0)
				salesDetail = make([]map[string]interface{}, 0)
			}
		}

		salesDetail = append(salesDetail, map[string]interface{}{
			"plu_id":   transaction["product_detail_fkid"],
			"name":     transaction["menu"],
			"price":    transaction["price"],
			"qty":      transaction["qty"],
			"category": transaction["subcategory"],
		})

		transactionMap["trx_id"] = transaction["sales_id"]
		transactionMap["date_time"] = transaction["tgl"]
		transactionMap["cashier"] = transaction["cashier"]
		transactionMap["grand_total"] = transaction["grand_total"]

		lastOutletId = utils.ToString(transaction["outlet_fkid"])
		lastSalesId = utils.ToString(transaction["sales_id"])
	}
	transactionMap["menu"] = salesDetail
	transactionArrMap = append(transactionArrMap, transactionMap)

	outletInt, _ := strconv.Atoi(lastOutletId)
	data["store_code"] = fmt.Sprintf("%03d", outletInt)
	data["transaction"] = transactionArrMap
	_, err = SendRequest(TRANSACTION, data)
	if utils.CheckErr(err) {
		errorCount += 1
	}

	if errorCount == 0 {
		localDb.Put("last_sync_transaction", lastQuery)
	}
}

func SyncMenuWithBehave() {
	adminId := os.Getenv("behave_admin_id")
	localDb := db.GetLocalDb()

	var lastSync int64
	err := localDb.Get("last_sync_menu", &lastSync)
	if err != nil {
		fmt.Println("Getting last sync menu error - ", err)
		lastSync = 0
	}

	//fmt.Println("last sync menu : ", lastSync)

	sql := `select pd.product_detail_id, p.product_id, outlet_fkid, price_sell, active, p.name, subcategory.name as subcategory, p.data_status as data_status_product, 
pd.data_status as data_status_detail 
from products_detail pd
         join products p on pd.product_fkid = p.product_id
         join products_subcategory subcategory on p.product_subcategory_fkid = subcategory.product_subcategory_id
where p.admin_fkid = ?
  and catalogue_type = 'product'
  and (p.data_modified >= ? OR pd.data_modified >= ?)
order by outlet_fkid`

	lastQuery := time.Now().Unix() * 1000
	menues, err := db.QueryArray(sql, adminId, lastSync, lastSync)
	if log.IfError(err) {
		return
	}

	//taxes
	sql = `select g.name, g.tax_type, g.jumlah, pdt.product_detail_fkid
from products_detail_taxdetail pdt
         join gratuity g on pdt.tax_fkid = g.gratuity_id
where g.tax_status = 'permanent'
  and g.data_status = 'on'
  and (tax_category = 'service' OR tax_category = 'tax')
  and admin_fkid = ?`
	taxes, err := db.QueryArray(sql, adminId)
	if log.IfError(err) {
		return
	}

	if len(menues) > 0 {
		errorCount := 0

		taxesMap := make(map[int][]map[string]interface{})
		for _, tax := range taxes {
			taxesMap[utils.ToInt(tax["product_detail_fkid"])] = append(taxesMap[utils.ToInt(tax["product_detail_fkid"])], tax)
		}

		lastOutletId := ""
		dataMenu := make([]map[string]interface{}, 0)
		data := make(map[string]interface{})
		menuAvailability := make([]map[string]interface{}, 0)
		for index, menu := range menues {
			if lastOutletId == "" {
				lastOutletId = utils.ToString(menu["outlet_fkid"])
			} else {
				if utils.ToString(menu["outlet_fkid"]) != lastOutletId {
					outletInt, _ := strconv.Atoi(lastOutletId)
					data["store_code"] = fmt.Sprintf("%03d", outletInt)
					data["menu"] = dataMenu
					_, err := SendRequest(MENU, data)
					if utils.CheckErr(err) {
						errorCount += 1
					}
					dataMenu = make([]map[string]interface{}, 0)
				}
			}

			status := "Active"
			if utils.ToString(menu["active"]) == "off" || utils.ToString(menu["active"]) == "on_link" || utils.ToString(menu["data_status_product"]) == "off" || utils.ToString(menu["data_status_detail"]) == "off" {
				status = "Inactive"
			}

			menuAvailability = append(menuAvailability, map[string]interface{}{
				"id_product":           menu["product_id"],
				"product_stock_status": menu["stock"],
			})

			price := utils.ToInt(menu["price_sell"])
			priceTax := 0 //float64(price) * 0.1

			for _, tax := range taxesMap[utils.ToInt(menu["product_detail_id"])] {
				if utils.ToString(tax["tax_type"]) == "percentage" {
					priceTax += price * utils.ToInt(tax["jumlah"]) / 100
				} else {
					priceTax += utils.ToInt(tax["jumlah"])
				}
			}

			dataMenu = append(dataMenu, map[string]interface{}{
				"plu_id":     menu["product_id"],
				"name":       menu["name"],
				"category":   menu["subcategory"],
				"price":      price + priceTax,
				"price_base": price,
				"price_tax":  priceTax,
				"status":     status,
			})

			lastOutletId = utils.ToString(menu["outlet_fkid"])

			if index%100 == 0 {
				log.Info("sleep...")
				time.Sleep(2 * time.Second)
			}
		}

		outletInt, _ := strconv.Atoi(lastOutletId)
		data["store_code"] = fmt.Sprintf("%03d", outletInt)
		data["menu"] = dataMenu
		_, err := SendRequest(MENU, data)
		if utils.CheckErr(err) {
			errorCount += 1
		}

		for _, menu := range menuAvailability {
			_, err := SendRequest(MENU_AVAILIBITY, menu)
			if log.IfError(err) {
				errorCount += 1
			}
		}

		if errorCount == 0 {
			localDb.Put("last_sync_menu", lastQuery)
		}

	} else {
		//log.Info("all menu already synced")
	}
}

func SyncOutletWithBehave() {
	localDb := db.GetLocalDb()

	var lastSync string
	err := localDb.Get("last_sync_outlet", &lastSync)
	if err != nil {
		fmt.Println("Getting last sync outlet error - ", err)
		lastSync = ""
	}
	//fmt.Println("From DB, last sync outlet : ", lastSync)

	outlets, err := db.QueryArray("SELECT * FROM outlets WHERE admin_fkid = 1 AND data_modified > ?", lastSync)
	if len(outlets) > 0 {
		data := make(map[string]interface{})
		stores := make([]map[string]interface{}, 0)

		for _, outlet := range outlets {
			status := "Active"
			if utils.ToString(outlet["data_status"]) == "off" {
				status = "Inactive"
			}
			stores = append(stores, map[string]interface{}{
				"store_code":   fmt.Sprintf("%03d", outlet["outlet_id"]),
				"store_name":   outlet["name"],
				"store_status": status,
			})
		}
		data["store"] = stores
		_, err := SendRequest(OUTLET, data)
		if err == nil {
			lastDate, err := db.Query("SELECT MAX(data_modified) as lastDate FROM outlets WHERE admin_fkid=1 AND data_modified > ?", lastSync)
			utils.CheckErr(err)
			if lastDate["lastDate"] != nil {
				localDb.Put("last_sync_outlet", lastDate["lastDate"])
				fmt.Println("--- Last Sync Outlet : ", lastDate["lastDate"])
			} else {
				localDb.Put("last_sync_outlet", "")
			}
		}
	} else {
		//fmt.Println("All outlet data synced")
	}
}
