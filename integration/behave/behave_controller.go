package behave

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"io/ioutil"
	"net/http"
	net "net/url"
	"os"
	"time"
)

const (
	OUTLET        = "api/v1/pos/outlet/sync"
	MENU          = "api/v1/pos/menu"
	TRANSACTION   = "api/v1/pos/transaction"
	REFUND        = "api/v1/pos/transaction/refund"
	CHECK_MEMBER  = "api/v1/pos/check/member"
	CHECK_VOUCHER = "api/v1/pos/check/voucher"
	VOID_VOUCHER  = "api/v1/pos/voucher/void"
	ORDER_ACCEPT  = "api/v1/pos/order/accept"
	ORDER_READY   = "api/v1/pos/order/ready"
	ORDER_TAKEN   = "api/v1/pos/order/taken"
	ORDER_REJECT  = "api/v1/pos/order/reject"
	MENU_AVAILIBITY = "api/outletapp/product/sold-out"
)

type BehaveAuth struct {
	TokenType   string `json:"token_type"`
	ExpiresIn   int64  `json:"expires_in"`
	AccessToken string `json:"access_token"`
}

type BehaveResponse struct {
	Status   string   `json:"status"`
	Messages []string `json:"messages"`
}

func WelcomeBehave(ctx *fasthttp.RequestCtx) {
	ctx.SetStatusCode(fasthttp.StatusOK)
}

func UpdateOrderToBehave(order models.OrderSales){
	endPoint := ""
	if order.Status == "accept" {
		endPoint = ORDER_ACCEPT
	}else if order.Status == "reject" {
		endPoint = ORDER_REJECT
	}else if order.Status == "ready" {
		endPoint = ORDER_READY
	}else if order.Status == "taken" {
		endPoint = ORDER_TAKEN
	}

	data, err := db.Query("SELECT outlet_fkid, items FROM order_sales WHERE order_sales_id = ? LIMIT 1", order.OrderSalesId)
	if log.IfError(err) {
		return
	}

	var sales map[string]interface{}
	err = json.Unmarshal([]byte(utils.ToString(data["items"])), &sales)
	log.IfError(err)

	resp, err := SendRequest(endPoint, map[string]interface{}{
		"order_id": sales["order_id"],
		"store_code" : fmt.Sprintf("%03d", utils.ToInt(data["outlet_fkid"])),
		"reason": order.RejectReason,
	})

	log.IfError(err)
	log.Info("[behave] update order resp : %s", string(resp))
}

func RequestBehaveToken(ctx *fasthttp.RequestCtx) {
	user := string(ctx.PostArgs().Peek("client_id"))
	pass := string(ctx.PostArgs().Peek("client_secret"))

	if user == os.Getenv("behave_user") && pass == os.Getenv("behave_password") {
		token := auth.InitJWTAuth().GenerateBehaveToken()
		_ = json.NewEncoder(ctx).Encode(token)
		return
	}

	ctx.Response.Header.Set("WWW-Authenticate", "Basic realm=Restricted")
	ctx.Error(fasthttp.StatusMessage(fasthttp.StatusUnauthorized), fasthttp.StatusUnauthorized)
}

func CheckBehaveMember(ctx *fasthttp.RequestCtx) {
	storeCode := ctx.Request.PostArgs().Peek("store_code")
	uid := ctx.Request.PostArgs().Peek("uid")

	data := map[string]interface{}{
		"store_code": string(storeCode),
		"uid":        string(uid),
	}

	resp, err := SendRequest(CHECK_MEMBER, data)
	utils.CheckErr(err)

	behaveResp := make(map[string]interface{})
	json.Unmarshal(resp, &behaveResp)
	status := utils.ToString(behaveResp["status"]) != "fail"
	json.NewEncoder(ctx).Encode(models.ResponseAny{Status: status, Message: utils.ToString(behaveResp["messages"])})
}

func CheckVoucher(ctx *fasthttp.RequestCtx) {
	storeCode := ctx.Request.PostArgs().Peek("store_code")
	qrCode := ctx.Request.PostArgs().Peek("qr_code")

	data := map[string]interface{}{
		"store_code": string(storeCode),
		"qrcode":     string(qrCode),
	}

	resp, err := SendRequest(CHECK_VOUCHER, data)
	utils.CheckErr(err)

	behaveResp := make(map[string]interface{})
	json.Unmarshal(resp, &behaveResp)
	status := utils.ToString(behaveResp["status"]) != "fail"
	json.NewEncoder(ctx).Encode(models.ResponseAny{Status: status, Message: utils.ToString(behaveResp["messages"])})
}

func VoidVoucher(ctx *fasthttp.RequestCtx) {
	voucherCode := ctx.Request.PostArgs().Peek("voucher_code")
	storeCode := ctx.Request.PostArgs().Peek("store_code")

	go func() {
		_, err := db.GetDb().Exec("update promotion_voucher_code set used = used-1 where voucher_code = ?", string(voucherCode))
		log.IfError(err)
	}()

	res := VoidBehaveVoucher(string(voucherCode), utils.ToInt(string(storeCode)))
	json.NewEncoder(ctx).Encode(res)
}

func VoidBehaveVoucher(voucherCode string, storeCode int) models.ResponseAny  {
	//fmt.Sprintf("%03d", outlet),
	data := map[string]interface{}{
		"store_code":   fmt.Sprintf("%03d", storeCode),
		"voucher_code": string(voucherCode),
	}

	resp, err := SendRequest(VOID_VOUCHER, data)
	log.IfError(err)

	behaveResp := make(map[string]interface{})
	err = json.Unmarshal(resp, &behaveResp)
	log.IfError(err)

	status := utils.ToString(behaveResp["status"]) != "fail"
	return models.ResponseAny{Status: status, Message: utils.ToString(behaveResp["messages"])}
}

func PushRefundTransaction(sales models.Sale) {
	if sales.EmployeeName == "" {
		sales.EmployeeName = "Dodo"
	}
	data := map[string]interface{}{
		"store_code": fmt.Sprintf("%03d", sales.OutletID),
		"trx_id":     sales.DisplayNota,
		"cashier":    sales.EmployeeName,
		"reason":     "salah input",
	}

	resp, err := SendRequest(REFUND, data)
	utils.CheckErr(err)

	fmt.Println("push refund response : ", string(resp))
}

func PushTransactionToBehave(sales models.Sale) {
	timeOffset := int64(25200)
	taxTotal := 0
	for _, tax := range sales.Taxes {
		taxTotal += tax.Total
	}
	discTotal := sales.Discount.DiscountNominal + sales.Discount.VoucherNominal

	menu := make([]map[string]interface{}, 0)
	for _, order := range sales.OrderList {
		menu = append(menu, map[string]interface{}{
			"plu_id":   order.Product.ProductID,
			"name":     order.Product.Name,
			"price":    order.Product.PriceSell,
			"category": "uncategorized",
			"qty":      order.Qty,
		})
	}

	payments := make([]map[string]interface{}, 0)
	for _, pay := range sales.Payments {
		payName := "tunai"
		if pay.Bank.Name != "" {
			payName = pay.Bank.Name
		}
		payments = append(payments, map[string]interface{}{
			"type":    pay.Method,
			"name":    payName,
			"nominal": pay.Total,
		})
	}

	vouchers := make([]map[string]interface{}, 0)
	for _, promotion := range sales.Promotions {
		if promotion.Source == "behave" {
			vouchers = append(vouchers, map[string]interface{}{
				"voucher_code": promotion.Code,
			})
		}
	}

	transactions := make([]map[string]interface{}, 0)
	date, _ := utils.MillisToDateTime((sales.TimeCreated/1000) + timeOffset)
	log.Info("timeOffset : %v | timeCrated: %v", timeOffset, sales.TimeCreated)
	transactions = append(transactions, map[string]interface{}{
		"trx_id":      sales.DisplayNota,
		"cashier":     sales.EmployeeName,
		"service":     0,
		"tax":         taxTotal,
		"discount":    discTotal,
		"total":       sales.GrandTotal - discTotal - taxTotal,
		"grand_total": sales.GrandTotal,
		"date_time":   date,
		"menu":        menu,
		"payments":    payments,
		"member_uid":  sales.MemberId,
		"voucher":     vouchers,
	})

	data := map[string]interface{}{
		"store_code":   fmt.Sprintf("%03d", sales.OutletID),
		"transactions": transactions,
	}

	resp, err := SendRequest(TRANSACTION, data)
	utils.CheckErr(err)

	fmt.Println("push transaction response : ", string(resp))

	//insert into sales_response
	_,err = db.Insert("sales_response", map[string]interface{}{
		"sales_fkid": sales.NoNota,
		"source": "behave",
		"data_created":  time.Now().Unix() * 1000,
		"response": string(resp),
	})

	log.IfError(err)
}

func SendRequest(endPoint string, data map[string]interface{}) ([]byte, error) {
	if !utils.IsAllowedToIntegrateWithBehave() {
		log.Warn("Ops... we are not allowed to sync to behave")
		return nil, nil
	}

	url := os.Getenv("behave_base_url") + endPoint
	log.Info("\nURL >> %s", url)

	data["api_key"] = os.Getenv("behave_api_key")
	data["api_secret"] = os.Getenv("behave_api_secret")
	dataJson, err := json.Marshal(data)
	if err != nil {
		fmt.Println("Parse map to json error : ", err)
	}
	fmt.Println(string(dataJson))

	localDb := db.GetLocalDb()
	var authToken string
	localDb.Get("auth", &authToken)
	//fmt.Println("Auth from local db : ", authToken)

	//var jsonStr = []byte(dataJson)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(dataJson))
	req.Header.Set("Authorization", "Bearer "+authToken)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if utils.CheckErr(err) {
		return nil, err
	}
	defer resp.Body.Close()

	log.Info("Response Status : %s", resp.Status)

	if resp.StatusCode == 200 {
		bodyResp, err := ioutil.ReadAll(resp.Body)
		log.Info("Response Body : %s", string(bodyResp))
		return bodyResp, err
	} else if resp.StatusCode == 401 {
		fmt.Println("Request New Token...")
		params := net.Values{}
		params.Add("grant_type", "client_credentials")
		params.Add("client_id", "1")
		params.Add("client_secret", os.Getenv("behave_client_secret"))
		params.Add("scope", "pos")

		res, err := http.PostForm(os.Getenv("behave_base_url")+"oauth/token", params)
		utils.CheckErr(err)

		defer res.Body.Close()
		body, err := ioutil.ReadAll(res.Body)
		if err != nil {
			fmt.Println("Oauth request error - ", err)
		}

		var behaveAuth BehaveAuth
		err = json.Unmarshal(body, &behaveAuth)
		utils.CheckErr(err)
		fmt.Println("Oauth response : ", string(body))

		localDb.Put("auth", behaveAuth.AccessToken)

		//request again with new token
		req.Header.Set("Authorization", "Bearer "+behaveAuth.AccessToken)
		resp, err := client.Do(req)
		if utils.CheckErr(err) {
			return nil, err
		}
		defer resp.Body.Close()

		bodyResp, err := ioutil.ReadAll(resp.Body)
		fmt.Println("[Final Request] Response Status: ", resp.Status)

		if resp.StatusCode == 200 {
			return bodyResp, err
		}else{
			return nil, errors.New(resp.Status)
		}

	} else {
		return nil, errors.New(resp.Status)
	}
}
