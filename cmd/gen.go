package main

import (
	"bufio"
	"flag"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	. "github.com/dave/jennifer/jen"
)

func main() {
	featureName := flag.String("f", "", "feature name")
	delivery := flag.String("d", "http", "delivery protocol")
	repository := flag.String("r", "mysql", "database framework")

	flag.Parse()
	camelCase("MasterEmployee")

	if *featureName == "" {
		fmt.Println("feature name is required, you can run the program with: -f [FEATURE]")
		fmt.Print("what is the feature name: ")
		input := bufio.NewScanner(os.Stdin)
		input.Scan()
		*featureName = input.Text()
	}

	//read go mod
	mod, err := ioutil.ReadFile("go.mod")
	if err != nil {
		panic(err)
	}
	modules := regexp.MustCompile(`^module ([a-z\/\.-]+)`).FindStringSubmatch(string(mod))
	if len(modules) == 0 {
		panic("failed finding module name")
	}

	//cmd := exec.Command("go","list")
	moduleId := modules[1] + "/"
	*featureName = strings.Replace(*featureName, " ", "", -1)
	feature := camelCase(*featureName)
	featureFile := snakeCase(feature)

	domain := NewFile("domain")
	domain.Type().Id(strings.Title(feature)).Struct()
	// domain.Type().Id(fmt.Sprintf("%sUseCase", strings.Title(feature))).Interface()
	// domain.Type().Id(fmt.Sprintf("%sRepository", strings.Title(feature))).Interface()
	CreateFile(domain, fmt.Sprintf("domain/%s.go", featureFile))

	//1. generating deliver
	dl := NewFile(strings.ToLower(*delivery))
	dl.Type().Id(fmt.Sprintf("%sHandler", feature)).Struct(
		Id("uc").Qual(moduleId+"module/"+featureFile, "UseCase"),
	)

	dl.Func().Id(fmt.Sprintf("New%s%sHandler", strings.Title(*delivery), strings.Title(feature))).
		Params(
			Id("app").Op("*").Qual("github.com/buaazp/fasthttprouter", "Router"),
			Id("useCase").Qual(moduleId+"module/"+featureFile, "UseCase"),
		).
		Block(
			Id("handler").Op(":=").Op("&").Id(fmt.Sprintf("%sHandler", feature)).Values(Id("useCase")),
			Id(fmt.Sprint("app.GET(\"/module/"+feature+"\"")).Op(",").Id(fmt.Sprint(" handler.Sample)")),
		)
	//generate sample handler function
	dl.Func().Id(fmt.Sprintf("(h %sHandler) Sample", feature)).
		Params(Id("ctx").Op("*").Qual("github.com/valyala/fasthttp", "RequestCtx")).
		//dl.Func().Id(fmt.Sprintf("(h %sHandler) Sample(ctx *fasthttp.RequestCtx)", feature)).
		Block(
			//Id(fmt.Sprintf(`_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"message": "this is sample of %s feature"})`, feature)),
			Id("_").Op("=").Qual("encoding/json", "NewEncoder").Call(Id("ctx")).Dot("Encode").Call(Map(String()).Interface().Values(Dict{Lit("message"): Lit(fmt.Sprintf("this is sample of %s feature", feature))})),
		)

	deliveryPath := fmt.Sprintf("module/%s/delivery/%s", featureFile, strings.ToLower(*delivery))
	deliveryFilePath := fmt.Sprintf("%s/%s_handler.go", deliveryPath, featureFile)
	CreateFile(dl, deliveryFilePath)

	//creating interfaces
	usecaseInterface := NewFile(featureFile)
	usecaseInterface.Type().Id("UseCase").Interface()
	CreateFile(usecaseInterface, fmt.Sprintf("module/%s/usecase.go", featureFile))

	repoInterface := NewFile(featureFile)
	repoInterface.Type().Id("Repository").Interface()
	CreateFile(repoInterface, fmt.Sprintf("module/%s/repository.go", featureFile))

	//2. creating repo
	repo := NewFile(strings.ToLower(*repository))
	repo.Type().Id(fmt.Sprintf("%sRepository", feature)).Struct(
		Id("db").Qual(moduleId+"core/db", "Repository"),
	)

	repo.Func().Id(fmt.Sprintf("New%s%sRepository", strings.Title(*repository), strings.Title(feature))).
		Params(Id("conn").Op("*").Qual("database/sql", "DB")).
		Qual(moduleId+"module/"+featureFile, "Repository").
		Block(
			Return(Op("&").Id(fmt.Sprintf("%sRepository", feature)).Values(Qual(moduleId+"core/db", "Repository").Values(Dict{
				Id("Conn"): Id("conn"),
			}))),
		)

	repoPath := fmt.Sprintf("module/%s/repository/%s", featureFile, strings.ToLower(*repository))
	repoFilePath := fmt.Sprintf("%s/%s_repository.go", repoPath, featureFile)
	CreateFile(repo, repoFilePath)

	//3. generate use case
	uc := NewFile("usecase")
	uc.Type().Id(fmt.Sprintf("%sUseCase", feature)).Struct(Id("repo").Qual(moduleId+"module/"+feature, "Repository"))
	uc.Func().Id(fmt.Sprintf("New%sUseCase", strings.Title(feature))).
		Params(Id("repository").Qual(moduleId+"module/"+featureFile, "Repository")).
		Qual(moduleId+"module/"+featureFile, "UseCase").
		Block(Return(Op("&").Id(fmt.Sprintf("%sUseCase", feature))).Values(Id("repository")))

	useCasePath := fmt.Sprintf("module/%s/usecase", featureFile)
	err = os.MkdirAll(useCasePath, os.ModePerm)
	if err != nil {
		panic(err)
	}

	useCaseFilePath := fmt.Sprintf("%s/%s_usecase.go", useCasePath, featureFile)
	CreateFile(uc, useCaseFilePath)

	//fmt.Printf("%#v", domain)
	//fmt.Printf("%#v", dl)
	//fmt.Printf("%#v", repo)
	//fmt.Printf("%#v", uc)
	fmt.Println("all files generated successfully")
}

func CreateFile(file *File, path string) {
	mainDir := strings.Replace(path, filepath.Base(path), "", 1)
	err := os.MkdirAll(mainDir, os.ModePerm)
	if err != nil {
		panic(err)
	}

	isCreateFile := true
	if exists(path) {
		fmt.Printf("%s is already exist, replace it? y/n ", path)
		input := bufio.NewScanner(os.Stdin)
		input.Scan()
		isCreateFile = strings.ToLower(input.Text()) == "y"
	}

	if isCreateFile {
		fmt.Println("creating file: ", path)
		err = file.Save(path)
		if err != nil {
			panic(err)
		}
	}
}

var matchFirstCap = regexp.MustCompile("(.)([A-Z][a-z]+)")
var matchAllCap = regexp.MustCompile("([a-z0-9])([A-Z])")

func snakeCase(str string) string {
	snake := matchFirstCap.ReplaceAllString(str, "${1}_${2}")
	snake = matchAllCap.ReplaceAllString(snake, "${1}_${2}")
	return strings.ToLower(snake)
}

var matchUpper = regexp.MustCompile("^[A-Z]")
var matchUnder = regexp.MustCompile("(_)([a-zA-Z])")
var matchUnderAll = regexp.MustCompile("(_)([A-Z])")

func camelCase(str string) string {
	camel := matchUpper.ReplaceAllStringFunc(str, strings.ToLower)
	camel = matchUnder.ReplaceAllStringFunc(camel, strings.ToUpper)
	camel = matchUnderAll.ReplaceAllString(camel, strings.ToUpper("${2}"))
	return camel
}

func exists(name string) bool {
	if _, err := os.Stat(name); err != nil {
		if os.IsNotExist(err) {
			return false
		}
	}
	return true
}
