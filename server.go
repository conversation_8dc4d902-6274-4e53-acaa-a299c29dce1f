package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"cloud.google.com/go/profiler"
	_ "github.com/joho/godotenv/autoload"
	"github.com/onatm/clockwerk"
	"github.com/valyala/fasthttp"
	"github.com/valyala/fasthttp/pprofhandler"
	v1 "gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/gdrive"
	googlesdk "gitlab.com/uniqdev/backend/api-pos/core/google"
	log2 "gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/job"
	"gitlab.com/uniqdev/backend/api-pos/models"
	httpAuth "gitlab.com/uniqdev/backend/api-pos/module/auth/delivery/http"
	mysqlAuth "gitlab.com/uniqdev/backend/api-pos/module/auth/repository/mysql"
	usecaseAuth "gitlab.com/uniqdev/backend/api-pos/module/auth/usecase"
	httpBilling "gitlab.com/uniqdev/backend/api-pos/module/billing/delivery/http"
	mysqlBilling "gitlab.com/uniqdev/backend/api-pos/module/billing/repository/mysql"
	usecaseBilling "gitlab.com/uniqdev/backend/api-pos/module/billing/usecase"
	httpEmployee "gitlab.com/uniqdev/backend/api-pos/module/employee/delivery/http"
	mysqlEmployee "gitlab.com/uniqdev/backend/api-pos/module/employee/repository/mysql"
	usecaseEmployee "gitlab.com/uniqdev/backend/api-pos/module/employee/usecase"
	httpKitchen "gitlab.com/uniqdev/backend/api-pos/module/kitchen/delivery/http"
	mysqlKitchen "gitlab.com/uniqdev/backend/api-pos/module/kitchen/repository/mysql"
	usecaseKitchen "gitlab.com/uniqdev/backend/api-pos/module/kitchen/usecase"
	httpMember "gitlab.com/uniqdev/backend/api-pos/module/member/delivery/http"
	mysqlMember "gitlab.com/uniqdev/backend/api-pos/module/member/repository/mysql"
	usecaseMember "gitlab.com/uniqdev/backend/api-pos/module/member/usecase"
	httpOutlet "gitlab.com/uniqdev/backend/api-pos/module/outlet/delivery/http"
	mysqlOutlet "gitlab.com/uniqdev/backend/api-pos/module/outlet/repository/mysql"
	usecaseOutlet "gitlab.com/uniqdev/backend/api-pos/module/outlet/usecase"
	httpProduct "gitlab.com/uniqdev/backend/api-pos/module/product/delivery/http"
	mysqlProduct "gitlab.com/uniqdev/backend/api-pos/module/product/repository/mysql"
	ucProduct "gitlab.com/uniqdev/backend/api-pos/module/product/usecase"
	httpPromo "gitlab.com/uniqdev/backend/api-pos/module/promotion/delivery/http"
	mysqlPromo "gitlab.com/uniqdev/backend/api-pos/module/promotion/repository/mysql"
	usecasePromo "gitlab.com/uniqdev/backend/api-pos/module/promotion/usecase"
	httpSales "gitlab.com/uniqdev/backend/api-pos/module/sales/delivery/http"
	mysqlSales "gitlab.com/uniqdev/backend/api-pos/module/sales/repository/mysql"
	usecaseSales "gitlab.com/uniqdev/backend/api-pos/module/sales/usecase"
	httpSelfOrder "gitlab.com/uniqdev/backend/api-pos/module/self_order/delivery/http"
	mysqlSelfOrder "gitlab.com/uniqdev/backend/api-pos/module/self_order/repository/mysql"
	ucSelfOrder "gitlab.com/uniqdev/backend/api-pos/module/self_order/usecase"
	httpSetting "gitlab.com/uniqdev/backend/api-pos/module/setting/delivery/http"
	mysqlSetting "gitlab.com/uniqdev/backend/api-pos/module/setting/repository/mysql"
	usecaseSetting "gitlab.com/uniqdev/backend/api-pos/module/setting/usecase"
	httpSync "gitlab.com/uniqdev/backend/api-pos/module/sync/delivery/http"
	mysqlSync "gitlab.com/uniqdev/backend/api-pos/module/sync/repository/mysql"
	usecaseSync "gitlab.com/uniqdev/backend/api-pos/module/sync/usecase"
	"gitlab.com/uniqdev/backend/api-pos/routers"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func main() {
	//setup logger
	log2.AddHook(log2.SlackHook{
		HookUrl: os.Getenv("SLACK_WEBHOOK_URL"),
		Channel: os.Getenv("SLACK_CHANNEL"),
	})

	log2.AddHook(log2.BugsnagHook{APIKey: os.Getenv("BUGSNAG_KEY")})

	// log2.IfError(fmt.Errorf("app started..."))

	cfg := googlesdk.Profiler()
	if err := profiler.Start(cfg); err != nil {
		fmt.Println(">> start profiler err: ", err)
	}

	if utils.IsAllowedToIntegrateWithBehave() {
		scheduleBehaveJob()
	} else {
		fmt.Printf("This server '%s' is not allowed to integrate with behave... \n", os.Getenv("server"))
	}

	// log2.AddHook(log2.BugsnagHook{APIKey: os.Getenv("BUGSNAG_KEY")})

	job.PullPubSubMessages()

	//verify all  required files are exist
	if os.Getenv("ENV") != "localhost" {
		utils.MustHaveFiles("config/rsa/app.rsa.key", "config/rsa/app.rsa.pub.key")
	}

	go v1.LoadHelpFromFirebase()

	router := routers.CreateRoutes()
	s := &fasthttp.Server{
		Handler:            auth.CORS(router.Handler),
		MaxRequestBodySize: 100 << 20, // 100MB
	}

	if os.Getenv("ENV") == "localhost" || os.Getenv("ENABLE_PPROF") == "true" {
		router.GET("/debug/pprof/:profile", pprofhandler.PprofHandler)
	}

	// v1.GetCloseShiftAdminAndDate(1, "24-04-2024")

	repo := mysqlSelfOrder.NewMysqlSelfOrderRepository(db.GetDb())
	uc := ucSelfOrder.NewSelfOrderUseCase(repo)
	httpSelfOrder.NewHttpSelfOrderHandler(router, uc)

	repoProduct := mysqlProduct.NewMysqlProductRepository(db.GetDb())
	useCaseProduct := ucProduct.NewProductUseCase(repoProduct)
	httpProduct.NewHttpProductHandler(router, useCaseProduct)

	// Initialize setting module
	repoSetting := mysqlSetting.NewMysqlSettingRepository(db.GetDb())
	useCaseSetting := usecaseSetting.NewSettingUseCase(repoSetting)
	httpSetting.NewHttpSettingHandler(router, useCaseSetting)

	memberRepo := mysqlMember.NewMysqlMemberRepository(db.GetDb())
	memberUseCase := usecaseMember.NewMemberUseCase(memberRepo)
	httpMember.NewHttpMemberHandler(router, memberUseCase)

	employeeRepo := mysqlEmployee.NewMysqlEmployeeRepository(db.GetDb())
	employeeUseCase := usecaseEmployee.NewEmployeeUseCase(employeeRepo)
	httpEmployee.NewHttpEmployeeHandler(router, employeeUseCase)

	authRepo := mysqlAuth.NewMysqlAuthRepository(db.GetDb())
	authUseCase := usecaseAuth.NewAuthUseCase(authRepo)
	httpAuth.NewHttpAuthHandler(router, authUseCase)

	outletRepo := mysqlOutlet.NewMysqlOutletRepository(db.GetDb())
	outletUseCase := usecaseOutlet.NewOutletUseCase(outletRepo)
	httpOutlet.NewHttpOutletHandler(router, outletUseCase)

	promoRepo := mysqlPromo.NewMysqlPromotionRepository(db.GetDb())
	promoUseCase := usecasePromo.NewPromotionUseCase(promoRepo)
	httpPromo.NewHttpPromotionHandler(router, promoUseCase)

	salesRepo := mysqlSales.NewMysqlSalesRepository(db.GetDb())
	salesUseCase := usecaseSales.NewSalesUseCase(salesRepo, repoProduct, outletRepo)
	httpSales.NewHttpSalesHandler(router, salesUseCase)

	billingRepo := mysqlBilling.NewMysqlBillingRepository(db.GetDb())
	billingUseCase := usecaseBilling.NewBillingUseCase(billingRepo)
	httpBilling.NewHttpBillingHandler(router, billingUseCase)

	syncRepo := mysqlSync.NewMysqlSyncRepository(db.GetDb())
	syncUseCase := usecaseSync.NewSyncUseCase(syncRepo)
	httpSync.NewHttpSyncHandler(router, syncUseCase)

	//kitchen display module
	kitchenRepo := mysqlKitchen.NewMysqlKitchenRepository(db.GetDb())
	kitchenUseCase := usecaseKitchen.NewKitchenUseCase(kitchenRepo)
	httpKitchen.NewHttpKitchenHandler(router, kitchenUseCase)

	client := db.MongoDbClient()
	if client != nil {
		collection := client.Database("products").Collection("notes")
		indexModel := mongo.IndexModel{
			Keys:    bson.D{{Key: "created_at", Value: 1}},
			Options: options.Index().SetExpireAfterSeconds(60 * 60 * 24 * 7),
		}
		_, err := collection.Indexes().CreateOne(context.Background(), indexModel)
		fmt.Println("---- SetExpireAfterSeconds Err: ", err)
	} else {
		fmt.Println("mongoDb client not initialized...")
	}

	//log.Fatal(fasthttp.ListenAndServe(":8000", router.Handler))

	env := os.Getenv("server")
	//check if program run with --test para given
	if env == "localhost" && len(os.Args) > 1 && os.Args[1] == "--test" {
		if len(os.Args) > 2 {
			switch os.Args[2] {
			case "receipt":
				salesJson := `{"customer":"Yuniq","customersQty":1,"discount":{"discount":0,"discountInfo":"","discountNominal":0,"discountType":"","voucher":0,"voucherInfo":"","voucherNominal":0,"voucherType":""},"displayNota":"NNN202412209","employeeID":43,"employeeName":"Feni Rosyanaa","grandTotal":30000,"memberDetail":{"member_id":"21626","name":"Yuniq","phone":"6285742257881","source":"uniq","type_fkid":149,"type_name":"Bronze"},"memberId":"21626","noNota":"17O46L2O79603","openShiftId":512,"orderList":[{"discount":{"discount":0,"discountInfo":"","discountNominal":0,"discountType":"","voucher":0,"voucherInfo":"","voucherNominal":0,"voucherType":""},"employeeId":0,"extra":[],"holdQty":0,"isHold":false,"isItemVoid":false,"price":7500,"priceAdd":0,"printed":0,"product":{"active":"on_all","catalogue_type":"product","data_modified":1734080649705,"data_status":"on","discount":"on","isShowStock":false,"name":"Almondchips M","outlet_fkid":665,"photo":"","price_buy":0,"price_buy_start":0,"price_sell":7500,"product_category_fkid":252,"product_detail_id":39988,"product_id":10184,"product_subcategory_fkid":375,"product_type_fkid":403,"purchase_report_category_fkid":42,"stock":"available","stock_management":0,"stock_qty":-2,"sync":true,"unit_fkid":110,"voucher":"on"},"qty":1,"subTotal":7500,"taxes":[],"tmpId":1734692359045,"voidEmployeeAuthId":0,"voidParentId":0,"voidedQty":0},{"discount":{"discount":0,"discountInfo":"","discountNominal":0,"discountType":"","voucher":0,"voucherInfo":"","voucherNominal":0,"voucherType":""},"employeeId":0,"extra":[],"holdQty":0,"isHold":false,"isItemVoid":false,"price":3500,"priceAdd":0,"printed":0,"product":{"active":"on_all","catalogue_type":"product","data_modified":1734691223000,"data_status":"on","discount":"on","isShowStock":false,"name":"Bun Oreo C","outlet_fkid":665,"photo":"","price_buy":0,"price_buy_start":0,"price_sell":3500,"product_category_fkid":252,"product_detail_id":39987,"product_id":10183,"product_subcategory_fkid":375,"product_type_fkid":403,"purchase_report_category_fkid":42,"stock":"available","stock_management":0,"stock_qty":0,"sync":true,"unit_fkid":110,"voucher":"on"},"qty":1,"subTotal":3500,"taxes":[],"tmpId":1734692359410,"voidEmployeeAuthId":0,"voidParentId":0,"voidedQty":0},{"discount":{"discount":0,"discountInfo":"","discountNominal":0,"discountType":"","voucher":0,"voucherInfo":"","voucherNominal":0,"voucherType":""},"employeeId":0,"extra":[],"holdQty":0,"isHold":false,"isItemVoid":false,"price":19000,"priceAdd":0,"printed":0,"product":{"active":"on_all","catalogue_type":"product","data_modified":1734346180000,"data_status":"on","discount":"on","isShowStock":false,"name":"Chiffon Keju","outlet_fkid":665,"photo":"","price_buy":0,"price_buy_start":0,"price_sell":19000,"product_category_fkid":252,"product_detail_id":39989,"product_id":10185,"product_subcategory_fkid":375,"product_type_fkid":403,"purchase_report_category_fkid":42,"stock":"available","stock_management":0,"stock_qty":-2,"sync":true,"unit_fkid":110,"voucher":"on"},"qty":1,"subTotal":19000,"taxes":[],"tmpId":1734692359699,"voidEmployeeAuthId":0,"voidParentId":0,"voidedQty":0}],"outletID":665,"outletName":"Outlet Bakery","payment":"CASH","payments":[{"due_date":0,"method":"CASH","pay":30000,"total":30000}],"promotions":[],"receiptReceiver":"6285742257881","status":"Success","synced":false,"table":"","taxes":[],"timeCreated":1734692381813,"timeModified":1734692379626,"timePrediction":0}`
				var sale models.Sale
				if err := json.Unmarshal([]byte(salesJson), &sale); err != nil {
					fmt.Println("Error: ", err)
				}
				v1.SendReceipt(sale)
			case "notification":
				notifDataNewOrder := map[string]string{
					"title":   "New Order",
					"message": "Confirm Order",
					"type":    "order_sales",
					"id":      fmt.Sprintf("%d", 5),
				}
				//modify for sales
				notifDataNewOrder["id"] = "INV-OM2025092619"
				notifDataNewOrder["type"] = "payment"

				googlesdk.SendNotification(googlesdk.NotificationData{
					Token: "d6xaWW8rTt6QbEQjZ4DxFJ:APA91bGp8gZvXEni98uqMLiiJD2_2y7x20FOCrJPlSjnpu9zMEcu2CB9kCGNKSJk30NxoGQXp6ESe3cgjvUnW4ADDpfPnTZ0ne_ln_mnFaLtn3gTTN9bLVM",
					Data:  notifDataNewOrder,
				})
			case "gdrive":
				fmt.Println("============== generate new gdrive client ==============")
				// removing current token at config/credentials/gdrive_token.json
				os.Remove("config/credentials/gdrive_token.json")
				// read README.md for more information
				_, _ = gdrive.GetDriveClient()

			case "clean-gdrive":
				fmt.Println("============== clean gdrive ==============")
				gdrive.RemoveOldLogs()
			case "drive-upload":
				fmt.Println("============== drive upload ==============")
				gdrive.UploadToGoogleDriveV2(gdrive.GDriveUploader{
					OriginPath:      "/Users/<USER>/Documents/Work/UNIQ/api-pos/README.md",
					DestinationPath: "UNIQ/TEST-UPLOAD/",
					SubParentId:     "DEVICE-ID",
				})
			case "fix-sales":
				v1.FixPromoDiscNominal()
			}

		}

		fmt.Println("exit for testing....")
		panic("exit")
	}

	if env != "localhost" {
		job.InitTaskSchedule()
		_, _ = gdrive.GetDriveClient()
	}

	defer fmt.Println("\n------- SERVICE ENDED ---------")
	errs := make(chan error)

	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
		errs <- fmt.Errorf("%s", <-c)
	}()

	go func() {
		port := os.Getenv("PORT")
		if port == "" {
			port = "8000"
		}
		fmt.Printf("\n\n|----------- API POS -----------|\n"+
			"port  : %s\n"+
			"env   : %s\n"+
			"|-------------------------------|\n\n", port, env)

		errs <- s.ListenAndServe(":" + port)
	}()

	log2.Info("exit: %v", <-errs)
}

func scheduleBehaveJob() {
	fmt.Println("Behave integration scheduled!")
	var behaveJob ScheduleJob
	c := clockwerk.New()
	c.Every(90 * time.Second).Do(&behaveJob)
	c.Start()
}

func (j *ScheduleJob) Run() {
	if !j.IsInProcess {
		j.IsInProcess = true

		//behave.SyncOutletWithBehave()
		//behave.SyncMenuWithBehave()
		//behave.SyncTransactionWithBehave()

		j.IsInProcess = false
	} else {
		log2.Info("previous job is still running...")
	}
}

type ScheduleJob struct {
	IsInProcess bool
}
