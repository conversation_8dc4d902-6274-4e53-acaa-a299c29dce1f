db_user = your_db_user
db_password = your_db_pass
db_name = your_db_name
db_host = ************ #fill with ip or db instance connection name (for unix)

mongo_db_user=mongo_username
mongo_db_password=mongo_password
mongo_db_name=mongo_database_name
mongo_db_host=xxxx.7xxxx.mongodb.net

#can be : tcp or ip (default)
db_communication=tcp

server = localhost
base_url = https://client.uniq.id
port = 8000

#optional, by default will use notif-server channel
SLACK_WEBHOOK_URL=http://url_slack_for_sending_notification

wa_unofficial = https://localhost:1719
messager_gateway = https://apimessenger.uniq.id
disable_cron = true

#behave env
behave_integration=enable  #or: disable
token = xxxx
behave_base_url = xxx
behave_api_key = xxx
behave_api_secret = xxx
behave_user = xxxxx
behave_password = xxxxx
behave_client_secret = xxxxx
#admin to sync with behave
behave_admin_id = 1

FIREBASE_CRM_KEY=AIzaxxxxxxxxxxxxx
FIREBASE_POS_KEY=AIzzxxxxxxxxxxxxx
FIREBASE_DYNAMIC_LINK=xxx

#BILLING
XENDIT_SECRET_KEY=xnd_development_xxxxxx
API_BILLING=http://service-billing:3000

BUGSNAG_KEY=xxxxxxxxx

# AI NLP Service
API_AI_NLP_BASE_URL=https://api-ai-nlp.uniq.id

# AI Service Configuration
API_AI_NLP_BASE_URL=https://api-ai-nlp.uniq.id # AI service endpoint for generating product images