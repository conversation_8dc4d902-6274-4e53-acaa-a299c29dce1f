### Send Request Example With Curl

```
curl -X 'POST' \
  'https://live-mt-server.wati.io/1041438/api/v1/sendTemplateMessage?whatsappNumber=6285742257881' \
  -H 'accept: */*' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NOxZz01ICiIXvIYo4xDMrGvZ0NJugbYMsRfi5w_XSq8' \
  -H 'Content-Type: application/json-patch+json' \
  -d '{
  "template_name": "close_shift",
  "broadcast_name": "close_shift_1",
  "parameters": [
  {
    "name": "outlet",
    "value": "Your Outlet Name"
  },
  {
    "name": "cashier",
    "value": "Your Cashier Name"
  },
  {
    "name": "shift",
    "value": "Pagi"
  },
  {
    "name": "date",
    "value": "25-12-2023"
  },
  {
    "name": "qty_item_sales",
    "value": "185"
  },
  {
    "name": "nominal_item_sales",
    "value": "2.237.421"
  },
  {
    "name": "discount_sales",
    "value": "91.721"
  },
  {
    "name": "net_sales",
    "value": "2.145.700"
  },
  {
    "name": "media_cash",
    "value": "867.000"
  },
  {
    "name": "media_card",
    "value": "1.278.700"
  },
  {
    "name": "card_detail",
    "value": "-"
  },
  {
    "name": "piutang",
    "value": "0"
  },
  {
    "name": "total_media",
    "value": "2.145.700"
  },
  {
    "name": "total_refund",
    "value": "0"
  },
  {
    "name": "total_void",
    "value": "0"
  },
  {
    "name": "total_pax",
    "value": "60"
  },
  {
    "name": "avg_pax",
    "value": "35.761"
  },
  {
    "name": "total_bill",
    "value": "39"
  },
  {
    "name": "avg_bill",
    "value": "55.017"
  },
  {
    "name": "aktual_cash",
    "value": "867.000"
  },
  {
    "name": "aktual_card",
    "value": "1.278.700"
  },
  {
    "name": "total_aktual",
    "value": "2.145.700"
  },
  {
    "name": "selisih",
    "value": "0"
  }
],
  "channel_number": "15558566814"
}'
```

### WhatsApp Template (WA Official)

```
#Outlet  : {{outlet}}
#Kasir   : {{cashier}}
#Shift   : {{shift}}
#Tanggal : {{date}}

========== SALES ==========
QTY Item Sales     : {{qty_item_sales}}
Nominal Item Sales : {{nominal_item_sales}}
Discount Sales     : {{discount_sales}}
========================== +
NET SALES            {{net_sales}}

========== MEDIA ==========
Cash     : {{media_cash}}
Card     : {{media_card}}
{{card_detail}}
Piutang  : {{piutang}}
========================== +
TOTAL      {{total_media}}

Total Refund : {{total_refund}}
Total Void   : {{total_void}}
Total Pax    : {{total_pax}}
AVG Pax      : {{avg_pax}}
Total Bill   : {{total_bill}}
AVG Bill     : {{avg_bill}}

========== AKTUAL =========
CASH : {{aktual_cash}}
CARD : {{aktual_card}}
========================== +
TOTAL  {{total_aktual}}
selisih {{selisih}}

__detail: y.uniq.id/r-closing__
```