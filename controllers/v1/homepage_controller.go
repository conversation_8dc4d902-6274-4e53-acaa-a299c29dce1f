package v1

import (
	"encoding/json"
	"fmt"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

func Join(ctx *fasthttp.RequestCtx) {
	name := string(ctx.FormValue("name"))
	email := string(ctx.FormValue("email"))
	phone := string(ctx.FormValue("phone"))
	company := string(ctx.FormValue("company"))
	campaign := string(ctx.FormValue("campaign"))
	realAddress := string(ctx.FormValue("address"))

	reqLog := fmt.Sprintf("Name : %s\nEmail : %s\nPhone : %s\nCompany : %s\nCampaign : %s\nAddress : %s", name, email, phone, company, campaign, realAddress)

	if len(phone) < 11 {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		utils.SendMessageToSlack("someone request quotation with invalid phone number : \n" + reqLog)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "invalid phone number"})
		return
	}
	if !(strings.HasPrefix(phone, "08") || strings.HasPrefix(phone, "62")) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		utils.SendMessageToSlack("someone request quotation with invalid phone number format : \n" + reqLog)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "invalid phone number format!"})
		return
	}

	message := "Hello " + string(name) + ", Terima Kasih dan Selamat Bergabung Bersama UNIQ. Marketing kami akan menghubungi anda secepatnya. \n" +
		"Jika ada yang ingin anda tanyakan, bisa langsung menghubungi kami di nomor ini\n" +
		"Atau via Email : <EMAIL>\n\n" +
		"Download aplikasi UNIQ di Playstore : https://play.google.com/store/apps/details?id=id.uniq.uniqpos"
	go utils.SendWhatsAppMessage(message, "employee", "92", phone)

	dateStr, _ := utils.MillisToDateTime(time.Now().Unix() + 25200)

	ipAddress := utils.FromRequest(ctx)
	ipGeolocation := ""
	address := ""

	request := utils.HttpRequest{}
	request.Method = "GET"
	request.Url = fmt.Sprintf("http://api.ipstack.com/%s?access_key=********************************", ipAddress)
	body, err := request.Execute()
	if !utils.CheckErr(err) {
		var geolocation models.IPGeolocation
		err = json.Unmarshal(body, &geolocation)
		if err == nil {
			ipGeolocation = string(body)
			address = fmt.Sprintf("%s, %s", geolocation.City, geolocation.CountryName)
		} else {
			utils.SendMessageToSlack("converting json from api.ipstack.com error. Json : " + string(body) + "\nErr " + err.Error())
		}
	} else {
		utils.SendMessageToSlack("Getting IP Geolocation Error. Json : " + string(body) + "\nErr " + err.Error())
	}

	data := map[string]interface{}{
		"name":           name,
		"email":          email,
		"phone":          phone,
		"company_name":   company,
		"data_created":   strconv.FormatInt(time.Now().Unix()*1000, 10),
		"register_date":  dateStr,
		"ip_address":     ipAddress,
		"ip_geolocation": ipGeolocation,
		"address":        address,
		"campaign":       campaign,
	}

	_, err = db.Insert("quotation", data)
	if err != nil {
		fmt.Println("Sign up Error : ", err)
	} else {
		message = fmt.Sprintf("*%s* ingin bergabung dengan UNIQ. \n#BISNIS : *%s* \n#Alamat (IP based) : *%s* \n", strings.ToUpper(name), company, address)
		if realAddress != ""{
			message += fmt.Sprintf("#Alamat : %s\n", realAddress)
		}
		if campaign != "" {
			message += "#Campaign : " + campaign
		}

		go utils.SendWhatsAppMessage(message, "group", "uniq", "6287838362747-1495704774")
		go SendToMarketing(name, email, phone, company)
	}

	//if utils.CheckError(ctx, err) {
	//	return
	//}

	ctx.SetContentType("application/json")
	ctx.Response.Header.Set("Access-Control-Allow-Origin", "*")
	json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "Data successfully inserted!"})
}

func SendToMarketing(name string, email string, phone string, business string) {
	//list all marketers
	marketers, err := db.QueryArray("SELECT agent_id, name, phone from agent where user_type = 'marketing' and active = 1 order by agent_id")
	if utils.CheckErr(err) {
		return
	}

	dbJson := db.GetDbJson()
	var lastMarketingId int
	if err = dbJson.Read("last_id", "marketing", &lastMarketingId); err != nil {

	}

	var selectedMarketing map[string]interface{}
	var firstMarketing map[string]interface{}
	for index, marketing := range marketers {
		if index == 0 {
			firstMarketing = marketing
			//fmt.Println("first marketing set ", firstMarketing)
		}
		if utils.ToInt(marketing["agent_id"]) > lastMarketingId {
			selectedMarketing = marketing
			break
		}
	}

	if len(selectedMarketing) == 0 {
		selectedMarketing = firstMarketing
	}

	err = dbJson.Write("last_id", "marketing", utils.ToInt(selectedMarketing["agent_id"]))
	utils.CheckErr(err)

	if strings.HasPrefix(phone, "08") {
		phone = "62" + phone[1:]
	}
	message := fmt.Sprintf("Hey *%s*, ada Customer yang ingin menggunakan UNIQ. Silahkan di followup, berikut data detail Customer : \n"+
		"Nama   : %s \n"+
		"HP     : %s \n"+
		"Email  : %s \n"+
		"Bisnis : %s \n\n"+
		"_Kirim pesan instant via Whatsapp, klik :_ https://api.whatsapp.com/send?phone=%s \n\n"+
		"_sent by system_",
		selectedMarketing["name"], name, phone, email, business, phone)

	fmt.Println(message)
	_ = utils.SendWhatsAppMessage(message, "employee", "92", utils.ToString(selectedMarketing["phone"]))
}

func sendSMS(name string, email string, phone string, company string) {
	message := strings.ToUpper(name) + " ingin bergabung dengan UNIQ. Email : " + email + " # HP : " + phone + " # BISNIS : " + company

	var Url *url.URL
	Url, err := url.Parse("https://reguler.zenziva.net")
	utils.CheckErr(err)

	Url.Path += "/apps/smsapi.php"
	parameters := url.Values{}
	parameters.Add("userkey", "qpsire")
	parameters.Add("passkey", "kejala2017")
	parameters.Add("nohp", "085742257881")
	parameters.Add("pesan", message)
	Url.RawQuery = parameters.Encode()

	res, err := http.Get(Url.String())
	utils.CheckErr(err)

	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	fmt.Println(">>> RESPONSE ZENZIVA : ", string(body))
}

func SendEmail(ctx *fasthttp.RequestCtx) {
	email := ctx.FormValue("email")
	message := ctx.FormValue("message")
	utils.SendEmail([]string{string(email)}, string(message))
}

func Test(ctx *fasthttp.RequestCtx) {
	fmt.Println(">>> WOKKKEEEEE.....")
}

func ReceiveWA(ctx *fasthttp.RequestCtx) {
	contactPhone := ctx.FormValue("contact[uid]")
	contactName := ctx.FormValue("contact[name]")
	msg := ctx.FormValue("message[body][text]")

	fmt.Println("Phone : ", string(contactPhone), "\nName : ", string(contactName), "\nMessage : ", string(msg))
}

////sensor?device_id=fishgator&ph=6&turbidity=10&temperature=16
func Fishgator(ctx *fasthttp.RequestCtx) {
	id := ctx.UserValue("id")
	ph := ctx.UserValue("ph")
	turbidity := ctx.UserValue("turbidity")
	temperature := ctx.UserValue("temperature")
	do := ctx.UserValue("do")
	feeder := ctx.UserValue("feeder")

	date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
	fmt.Println(date, " --> PH : ", ph, " - turbidity : ", turbidity, " - temperature : ", temperature, " - DO : ", do, " - Feeder : ", feeder)

	url := fmt.Sprintf("https://us-central1-fishgator-3db50.cloudfunctions.net/sensor?id=%s&ph=%s&trb=%s&temp=%s&feeder=%s", id, ph, turbidity, temperature, feeder)
	_, err := http.Get(url)
	if err != nil {
		ctx.Write([]byte(fmt.Sprintf("Error request to firebase %s", err)))
		return
	}

	url = fmt.Sprintf("http://ronny2802.me.student.pens.ac.id/fishgator/on-admin/add.php?suhu=%s&td=%s&ph=%s&do=%s&fd=%s", temperature, turbidity, ph, do, feeder)
	_, err = http.Get(url)
	if err != nil {
		ctx.Write([]byte(fmt.Sprintf("Error %s", err)))
		return
	}

	ctx.Write([]byte(url))
	ctx.SetStatusCode(fasthttp.StatusOK)
}

func TrelloWebhook(ctx *fasthttp.RequestCtx) {
	ctx.SetStatusCode(fasthttp.StatusOK)
}

func GetIPAddress(ctx *fasthttp.RequestCtx) {
	ipAddress := utils.FromRequest(ctx)
	localIP := utils.GetOutboundIP()
	data := map[string]interface{}{
		"ip_address": ipAddress,
		"local_ip":   localIP.String(),
	}

	json.NewEncoder(ctx).Encode(data)
}

func GetDetailPost(ctx *fasthttp.RequestCtx) {
	header := ctx.Request.Header.Peek("Authorization")
	data := map[string]interface{}{
		"header": string(header),
		"method": string(ctx.Method()),
	}
	json.NewEncoder(ctx).Encode(data)
}

func BenchmarkInsert(ctx *fasthttp.RequestCtx) {
	start := time.Now()
	response := "success"
	max := 0
	data, err := db.Query("SELECT COUNT(*) as total FROM test_benchmark")
	if err == nil {
		max = utils.ToInt(data["total"])
	} else {
		response = "get count error: " + err.Error()
		fmt.Println("get count error ", err)
	}
	_, err = db.Insert("test_benchmark", map[string]interface{}{
		"category":      "Startup",
		"news_title":    fmt.Sprintf("Chapter %d : With zero funding raised, car-sharing marketplace Tribecar is profitable and growing", max+1),
		"news_content":  "Owning a car is a luxury in Singapore: just ask actor Vin Diesel. During a press tour for the 2013 summer blockbuster a reporter asked Diesel to guess the prices of various cars in Singapore. The actor was shocked by the going prices at the time. “US$154,000 for a Prius?” he exclaimed. Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
		"source_url":    "https://www.techinasia.com/funding-raised-carsharing-marketplace-tribecar-profitable-growing",
		"jurnalis_name": "John Snow",
		"created_at":    "2019-07-19 18:30",
	})

	if err != nil {
		response += " - insert FAIL : " + err.Error()
		fmt.Println("insert error ", err)
	} else {
		response += fmt.Sprintf(" - insert success. count data : %d", max+1)
	}

	end := time.Now()
	elapsed := end.Sub(start)
	fmt.Println("--> Request POST took : ", elapsed.String())

	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
		"status":  response == "success",
		"message": response,
	})
}

func BenchmarkGet(ctx *fasthttp.RequestCtx) {
	start := time.Now()
	count := utils.ToInt(ctx.UserValue("count"))
	message := ""
	data, err := db.QueryArray("SELECT * FROM test_benchmark LIMIT ?", count)
	if err != nil {
		fmt.Println("Query error ", err)
		message = err.Error()
	}

	end := time.Now()
	elapsed := end.Sub(start)
	fmt.Println("--> Request GET took : ", elapsed.String())

	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
		"status":  err == nil,
		"message": message,
		"data":    data,
	})
}
