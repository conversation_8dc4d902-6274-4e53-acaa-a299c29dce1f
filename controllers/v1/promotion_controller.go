package v1

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/integration/behave"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

var errVoucherNotFound = errors.New("voucher tidak ditemukan")
var errVoucherExceedLimit = errors.New("voucher melebihi batas penggunaan")

func GetPromotionByOutlet(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")
	adminId := ctx.Request.Header.Peek("admin_id")

	var wg sync.WaitGroup
	promotionChan := make(chan []map[string]interface{}, 1)
	promotionTypeChan := make(chan []map[string]interface{}, 1)
	promotionProductChan := make(chan []map[string]interface{}, 1)

	sql := `
select p.*, group_concat(pmt.member_type_id) as member_type_ids, group_concat(p2.name) as member_type_names
from promotions p
         left join promotion_member_type pmt on p.promotion_id = pmt.promotion_id
         left join members_type mt on mt.type_id = pmt.member_type_id
         left join products p2 on mt.product_fkid = p2.product_id
where p.modified >= ?
  and p.promotion_id in (select promotion_outlets.promotion_id from promotion_outlets where outlet_id = ?)
group by p.promotion_id `
	wg.Add(1)
	go db.QueryArrayGo(&wg, promotionChan, sql, lastSync, outletId)

	sql = `select * from promotion_types`
	wg.Add(1)
	go db.QueryArrayGo(&wg, promotionTypeChan, sql)

	sql = `
select pp.*
from promotion_products pp
         join products_detail pd on pp.product_detail_fkid = pd.product_detail_id
where pd.outlet_fkid = ? `
	wg.Add(1)
	go db.QueryArrayGo(&wg, promotionProductChan, sql, outletId)

	wg.Wait()

	//TODO: remove once the discount nota is ready in POS Mobile
	//getting promo_id with disc nota
	sql = `SELECT promotion_id from promotions p where p.discount_type='nota' 
	and p.modified >= ?
  and p.promotion_id in (select promotion_outlets.promotion_id from promotion_outlets where outlet_id = ?)`
	promoIdDiscNota, err := db.QueryArray(sql, lastSync, outletId)
	log.IfError(err)

	//getting products for the current outlet
	productsTmp := make([]map[string]interface{}, 0)
	if len(promoIdDiscNota) > 0 {
		sql = `SELECT pd.product_detail_id as product_detail_fkid from products_detail pd 
		JOIN products p ON pd.product_fkid = p.product_id
		 where pd.outlet_fkid= ?
		 and p.admin_fkid = ?
		and pd.data_status='on' and p.data_status='on'
		and (pd.active = 'on_all' or pd.active='on_sales')
		and p.catalogue_type='product' `
		productsTmp, err = db.QueryArray(sql, outletId, adminId)
		log.IfError(err)
	}

	log.Info("promo Disc Nota: %v | prods:; %v | %v", len(promoIdDiscNota), len(productsTmp), promoIdDiscNota)

	promotions := <-promotionChan
	promotionTypes := <-promotionTypeChan
	promotionProducts := <-promotionProductChan

	promotionParent := utils.ConvertToSingleMap(promotionTypes, "promotion_type_id", "name")

	for _, promotion := range promotions {
		for _, typeMap := range promotionTypes {
			if utils.ToString(typeMap["promotion_type_id"]) == utils.ToString(promotion["promotion_type_id"]) {
				parent := utils.ToString(promotionParent[typeMap["parent_id"]])
				parent = strings.Replace(parent, " ", "", -1)
				promotion["promotion_type"] = fmt.Sprintf("%s_%s", parent, strings.Replace(utils.ToString(typeMap["name"]), " ", "", -1))
			}
		}

		products := make([]map[string]interface{}, 0)
		if utils.ToString(promotion["discount_type"]) == "nota" && os.Getenv("server") != "development" {
			products = productsTmp
		} else {
			for _, product := range promotionProducts {
				if product["promotion_id"] == promotion["promotion_id"] {
					products = append(products, product)
				}
			}
		}

		memberIds := make([]int, 0)
		memberTypes := make([]map[string]interface{}, 0)
		types := strings.Split(utils.ToString(promotion["member_type_names"]), ",")
		ids := strings.Split(utils.ToString(promotion["member_type_ids"]), ",")
		if len(types) != len(ids) {
			log.Error("length member ids and name not same, outlet id : %d | %d VS %d \nnames : %v\nids : %v", outletId, len(types), len(ids), promotion["member_type_names"], promotion["member_type_ids"])
		}

		for i, id := range ids {
			if id == "" {
				continue
			}
			memberIds = append(memberIds, utils.ToInt(id))
			memberTypeName := "[unknown]"
			if i < len(types) {
				memberTypeName = types[i]
			}
			memberTypes = append(memberTypes, map[string]interface{}{
				"type_id": utils.ToInt(id),
				"name":    memberTypeName,
			})
		}

		promotion["promotion_product"] = products
		//promotion["member_type_id"] = promotion["member_type_ids"]
		promotion["member_type_ids"] = memberIds
		promotion["member_type"] = memberTypes
	}

	//log.Info("result --> %s", utils.SimplyToJson(promotions))
	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: promotions})
}

func CheckVoucherCode(ctx *fasthttp.RequestCtx) {
	outletId := ctx.UserValue("outletId")
	qrCode := string(ctx.QueryArgs().Peek("qr_code"))
	adminId := ctx.Request.Header.Peek("admin_id")
	device := ctx.Request.Header.Peek("Device")
	result := models.ResponseAny{}

	timeOffset := int64(25200)

	if qrCode == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "qr_code is required"})
		return
	}

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	var logs strings.Builder
	logs.WriteString(fmt.Sprintf("[voucher-count] check voucher : %s | at: %v | by: %s", qrCode, outletId, string(device)))

	//handle secret code promotion_buy
	if utils.IsNumber(qrCode) {
		promotionBuy, err := db.Query("SELECT pb.promotion_buy_id  from promotion_buy pb where secret_code = ? and secret_code_expired > UNIX_TIMESTAMP()*1000", qrCode)
		if err == nil && len(promotionBuy) > 0 {
			qrCode = utils.EncryptWithKey(utils.ToString(promotionBuy["promotion_buy_id"]), utils.KEY_PROMOTION_BARCODE)
		}
		logs.WriteString(fmt.Sprintf("checking secret code: %v | %v", promotionBuy, qrCode))
	}

	//check if voucher code can be decrypted to integer, to check if this is promotion_buy id
	promoId := utils.DecryptWithKey(qrCode, utils.KEY_PROMOTION_BARCODE)
	logs.WriteString(fmt.Sprintf("after decrypt : %s", promoId))

	//temporary fixing... if decrypt failed, try to reverse lowercase to uppercase, and vice versa
	if !utils.IsNumber(promoId) {
		tmpPromoId := utils.DecryptWithKey(utils.ReverseString(qrCode), utils.KEY_PROMOTION_BARCODE)
		if utils.IsNumber(tmpPromoId) {
			promoId = tmpPromoId
		}
	}

	log.Info(logs.String())
	if utils.IsNumber(promoId) {
		timeNow := time.Unix(time.Now().Unix()+timeOffset, 0).Format("15:04:05")

		sql := `
 SELECT p.name, p.maximum_redeem_period_days, p.maximum_redeem_period,
	   min(pb.promo_nominal)                       as value_old,
	   min(p.promo_nominal) as value,
       promo_type                                  as type,
	   p.member_maximum_redeem,
	   p.terms_multiples_apply,
	   p.term,
       p.promo_discount_type,
       p.promo_discount_maximum,
	   p.max_qty_promo,
       min(promotion_type_fkid)                    as promotion_type_fkid,
       pb.promotion_fkid,
       min(pb.status)                              as status,
       p.min_order,
       start_promotion_date,
       end_promotion_date,
       if(from_unixtime(start_promotion_date / 1000 + ?, '%Y-%m-%d') > from_unixtime(unix_timestamp() + ?, '%Y-%m-%d'),
          'no', 'yes')                             as date_has_started,
       if(from_unixtime(end_promotion_date / 1000 + ?, '%Y-%m-%d') >= from_unixtime(unix_timestamp() + ?, '%Y-%m-%d'),
          'no', 'yes')                             as date_has_expired,
       if(p.start_promotion_time > ?, 'no', 'yes') as has_started,
       if(p.end_promotion_time > ?, 'no', 'yes')   as has_expired,
       sunday,
       monday,
       tuesday,
       wednesday,
       thursday,
       friday,
       saturday,
       group_concat(po.outlet_id)                  as outlets,
       min(pb.member_fkid) as member_fkid,
	   p.promotion_id
FROM promotion_buy pb
         JOIN promotions p ON p.promotion_id = pb.promotion_fkid
         join promotion_outlets po on p.promotion_id = po.promotion_id
where promotion_buy_id = ?
group by p.promotion_id `

		promo, err := db.Query(sql, timeOffset, timeOffset, timeOffset, timeOffset, timeNow, timeNow, promoId)
		if log.IfErrorSetStatus(ctx, err) {
			return
		}

		result.Message = "promotion not found"
		if len(promo) > 0 {
			weekday := strings.ToLower(time.Unix(time.Now().Unix()+timeOffset, 0).Weekday().String())
			//startDate := utils.ToInt64(promo["start_promotion_date"])
			//endData := utils.ToInt64(promo["end_promotion_date"])
			outlets := strings.Split(utils.ToString(promo["outlets"]), ",")
			log.Info("promo with id '%s' : %v", promoId, utils.SimplyToJson(promo))

			if utils.ToString(promo[weekday]) == "0" { //check if active at current day
				result.Message = "voucher can not be used on this day"
			} else if !utils.HasAny(outlets, utils.ToString(outletId)) { //check if active at current outlet
				result.Message = "voucher can not be used on this store"
			} else if promo["date_has_started"] == "no" {
				result.Message = "voucher can not be used yet"
			} else if promo["date_has_expired"] == "yes" {
				result.Message = "voucher is expired"
			} else if promo["has_started"] == "no" {
				result.Message = "voucher can not be used at this time"
			} else if promo["has_expired"] == "yes" {
				result.Message = "voucher can not be used at this time"
			} else if !validateMaxRedeemPeriod(promo) {
				result.Message = "voucher exceeded the maximum redeem period"
			} else if valid, _ := validateMaxRedeemMember(promo); !valid {
				result.Message = fmt.Sprintf("voucher exceeded the maximum usage, per member can use only %v time", promo["member_maximum_redeem"])
				promo["usage"] = getPromotionUsage(utils.ToInt(promo["promotion_fkid"]), utils.ToInt(promo["member_fkid"]))
			} else if promo["status"] == "available" {
				log.Info("promo %v status: '%v'", promoId, promo["status"])
				go func() {
					_, err := db.Update("promotion_buy", map[string]interface{}{"status": "redeem"}, "promotion_buy_id = ?", promoId)
					log.IfError(err)
				}()

				sql := `SELECT member_id, m.name, phone, type_fkid, p.name as type_name
FROM members m
         join members_detail md on m.member_id = md.member_fkid
         join members_type mt on md.type_fkid = mt.type_id
         join products p on mt.product_fkid = p.product_id
WHERE member_id = ?
  and md.admin_fkid = ? `
				memberDetail, err := db.Query(sql, promo["member_fkid"], string(adminId))
				if log.IfErrorSetStatus(ctx, err) {
					return
				}

				result.Status = true
				result.Message = "success"
				promo["code"] = promoId
				promo["source"] = "uniq"
				promo["promo_type"] = "promo_buy"
				promo["type_id"] = promo["promotion_type_fkid"]
				promo["member_detail"] = memberDetail

				//if not promo nominal, it might be special price,
				//then get special price detail
				// if promo["type"] == "special_price" {
				// } else {
				// }

				promotionProduct, err := getDetailPromotion(promo["promotion_fkid"], outletId)
				if err != nil {
					result.Status = false
					result.Message = err.Error()
				} else {
					promo["promo"] = map[string]interface{}{
						"products": promotionProduct["products"],
					}
				}
			} else {
				promo["usage"] = getPromotionUsage(utils.ToInt(promo["promotion_fkid"]), utils.ToInt(promo["member_fkid"]))
				result.Message = "voucher already used"
			}
		}

		result.Data = promo
	} else {
		//then check from voucher code
		promo, err := getDetailVoucher(qrCode, outletId, adminId)
		if err != errVoucherNotFound { //if error is because voucher not found in uniq DB, then check to behave (if allowed)
			if err == nil {
				//update used count
				go func(qrCode string, promoId interface{}) {
					_, err := db.GetDb().Exec("update promotion_voucher_code set used = used+1 where voucher_code = ? and promotion_id = ?", qrCode, promoId)
					log.IfError(err)
					log.Info("[voucher-count] update total used of voucher_code: %v | promoId: %v", qrCode, promoId)

					//check anomaly
					// CheckVoucherAnomaly()
				}(qrCode, promo["promotion_id"])

				//data := map[string]interface{}{
				//	"code":                qrCode,
				//	"source":              "uniq",
				//	"type":                promo["promo_type"],
				//	"type_id":             promo["promo_type_id"],
				//	"promo":               promo,
				//	"name":                promo["name"],
				//	"min_order":           promo["min_order"],
				//	"discount_type":       promo["discount_type"],
				//	"value":               promo["value"],
				//	"promo_discount_type": promo["promo_discount_type"],
				//}
				result.Status = true
				result.Data = promo
			} else {
				result.Status = false
				result.Message = err.Error()
			}
		} else if utils.IsAllowedToIntegrateWithBehave() {
			outlet, _ := strconv.Atoi(utils.ToString(outletId))
			resp, err := behave.SendRequest(behave.CHECK_VOUCHER, map[string]interface{}{
				"store_code": fmt.Sprintf("%03d", outlet),
				"qrcode":     qrCode,
			})

			log.IfError(err)

			var behaveResp models.BehaveVoucherResp
			log.IfError(json.Unmarshal(resp, &behaveResp))

			if behaveResp.Status == "success" {
				behaveResp.Result.Voucher["source"] = "behave"
				behaveResp.Result.Voucher["name"] = behaveResp.Result.Voucher["type"]
				log.Info("promo type : %v", behaveResp.Result.Voucher["type"])
				if behaveResp.Result.Voucher["type"] == "voucher" {
					behaveResp.Result.Voucher["type"] = "nominal"
				} else {
					promo, err := getDetailVoucher(behaveResp.Result.Voucher["value"], outletId, adminId)
					if log.IfError(err) {
						//first void the voucher
						go func() {
							_, err := behave.SendRequest(behave.VOID_VOUCHER, map[string]interface{}{
								"store_code":   fmt.Sprintf("%03d", utils.ToInt(outletId)),
								"voucher_code": behaveResp.Result.Voucher["code"],
							})
							log.IfError(err)
						}()

						_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: err.Error()})
						return
					} else {
						behaveResp.Result.Voucher["type"] = promo["promo_type"]
						behaveResp.Result.Voucher["type_id"] = promo["promo_type_id"]
						behaveResp.Result.Voucher["promo"] = promo
						behaveResp.Result.Voucher["min_order"] = promo["min_order"]

						//update used count
						go func() {
							log.Info("[voucher-count] voucher behave update, '%v'", behaveResp.Result.Voucher["value"])
							_, err := db.GetDb().Exec("update promotion_voucher_code set used = used+1 where voucher_code = ? and admin_fkid = ?", behaveResp.Result.Voucher["value"], adminId)
							log.IfError(err)
						}()
					}
				}
				result.Status = true
				result.Data = behaveResp.Result.Voucher
			} else {
				result.Status = false
				//result.Message = fmt.Sprintf("%s, %s", behaveResp.Status, behaveResp.Messages)
				result.Message = "invalid voucher code"
			}
		} else {
			result.Status = false
			result.Message = "voucher code not found!"
		}
	}

	log.Info("(%v) promo with code : '%v' | status : '%s' | promo data --> %s ", outletId, qrCode, result.Message, utils.TakeMax(utils.SimplyToJson(result.Data), 25))
	_ = json.NewEncoder(ctx).Encode(result)
}

func getPromotionUsage(promoId, memberId int) []map[string]interface{} {
	sql := `SELECT
	o.name as outlet_name, s.time_created, s.display_nota, 
	FROM_UNIXTIME(s.time_created/1000+25200, '%d/%m/%Y %H:%m') as time_created_format
FROM
	sales_promotion sp
	JOIN sales s ON s.sales_id = sp.sales_fkid
	JOIN outlets o ON o.outlet_id = s.outlet_fkid
WHERE
	s.status = 'Success'
	AND sp.promotion_fkid = ?
	AND s.member_fkid = ? `

	promotionUsages, err := db.QueryArray(sql, promoId, memberId)
	log.IfError(err)
	log.Info("total usage of promo: %v by %v is %v", promoId, memberId, len(promotionUsages))
	return promotionUsages
}

// return true if valid
func validateMaxRedeemMember(promo map[string]interface{}) (bool, []map[string]interface{}) {
	if utils.ToInt(promo["member_maximum_redeem"]) == 0 {
		return true, nil
	}

	sql := `SELECT count(*) as total_usage from sales_promotion sp
	join sales s on s.sales_id=sp.sales_fkid
	 where promotion_fkid=? and s.member_fkid=? and s.status='Success' `
	data, err := db.Query(sql, promo["promotion_fkid"], promo["member_fkid"])
	if log.IfError(err) {
		return true, nil
	}

	return utils.ToInt(data["total_usage"]) < utils.ToInt(promo["member_maximum_redeem"]), nil

	// 	sql := `SELECT
	// 	o.name as outlet_name, s.time_created,
	// 	FROM_UNIXTIME(s.time_created/1000+25200, '%d/%m/%Y %H:%m') as time_created_format
	// FROM
	// 	sales_promotion sp
	// 	JOIN sales s ON s.sales_id = sp.sales_fkid
	// 	JOIN outlets o ON o.outlet_id = s.outlet_fkid
	// WHERE
	// 	s.status = 'Success'
	// 	AND sp.promotion_fkid = ?
	// 	AND s.member_fkid = ? `

	// 	promotionUsages, err := db.QueryArray(sql, promo["promotion_fkid"], promo["member_fkid"])
	// 	if log.IfError(err) {
	// 		return true, nil
	// 	}

	// 	if len(promotionUsages) < utils.ToInt(promo["member_maximum_redeem"]) {
	// 		return true, nil
	// 	}

	// return false, promotionUsages
}

// return true if valid
func validateMaxRedeemPeriod(promo map[string]interface{}) bool {
	//if not set, its valid
	maxRedeemDay := utils.ToInt(promo["maximum_redeem_period_days"])
	if maxRedeemDay == 0 || utils.ToInt(promo["maximum_redeem_period"]) == 0 {
		return true
	}

	sql := `SELECT s.sales_id, s.time_created, COALESCE(any_value(sp.promotion_fkid), any_value(sdp.promotion_fkid)) as promo_id  
	from sales s 
	left join sales_detail_promotion sdp on sdp.sales_fkid=s.sales_id
	left join sales_promotion sp on sp.sales_fkid=s.sales_id
	where s.member_fkid= ? and COALESCE(sp.promotion_fkid, sdp.promotion_fkid) = ?
	and s.time_created > CAST(UNIX_TIMESTAMP(CURTIME(3) + INTERVAL ? DAY) * 1000 AS unsigned)
	group by s.sales_id`

	sql = `SELECT s.sales_id, s.time_created, COALESCE(any_value(sp.promotion_fkid), any_value(sdp.promotion_fkid)) as promo_id  
	from sales s 
	left join sales_detail_promotion sdp on sdp.sales_fkid=s.sales_id
	left join sales_promotion sp on sp.sales_fkid=s.sales_id
	where s.member_fkid= ? and COALESCE(sp.promotion_fkid, sdp.promotion_fkid) = ?
	and FROM_UNIXTIME(s.time_created/1000+25200, '%Y-%m-%d') > ?
	group by s.sales_id`

	timeCompare := time.Now().AddDate(0, 0, maxRedeemDay*-1).Format("2006-01-02")

	// data, err := db.QueryArray(sql, promo["member_fkid"], promo["promotion_fkid"], maxRedeemDay*-1)
	data, err := db.QueryArray(sql, promo["member_fkid"], promo["promotion_fkid"], timeCompare)
	log.IfError(err)

	if len(data) >= utils.ToInt(promo["maximum_redeem_period"]) {
		fmt.Println("promo already used: ", data)
		log.IfError(fmt.Errorf("promo reach maxRedeemPeriod (%v), promoId: %v | memberId: %v | maxRedeemDay: %v | %v | time: %v ", promo["maximum_redeem_period"], promo["promotion_fkid"], promo["member_fkid"], maxRedeemDay, data, timeCompare))
		return false
	}

	return true
}

func CheckVoucherAnomaly() {
	sql := `SELECT
	*
FROM
	promotion_voucher_code pvc
	JOIN (
		SELECT
			count(*) AS total_used,
			sales_promotion.voucher_code,
			GROUP_CONCAT(DISTINCT promotion_fkid) AS promotion_fkid
		FROM
			sales_promotion
			JOIN sales ON sales_promotion.sales_fkid = sales.sales_id
		WHERE
			sales_promotion.voucher_code IS NOT NULL
			AND sales.status = 'Success'
		GROUP BY
			sales_promotion.voucher_code,
			promotion_fkid) sp ON sp.voucher_code = pvc.voucher_code
	AND sp.promotion_fkid = pvc.promotion_id
HAVING
	pvc.used != sp.total_used
	OR pvc.used > pvc.max_redeem`
	data, err := db.QueryArray(sql)
	log.IfError(err)

	if len(data) > 0 {
		log.Info("anomaly voucher found: %v", utils.SimplyToJson(data))
		log.IfError(fmt.Errorf("%d anomaly VOUCHER found: %v", len(data), utils.SimplyToJson(data)))
	}

	sql = `SELECT promotion_buy_id from promotion_buy 
	where status='redeem' and promotion_buy_id not in 
	(SELECT promotion_buy_fkid from sales_promotion where promotion_buy_fkid is not null)`
	data, err = db.QueryArray(sql)
	log.IfError(err)

	if len(data) > 0 {
		log.Info("anomaly DEALS found: %v", utils.SimplyToJson(data))
		log.IfError(fmt.Errorf("%d anomaly deals found: %v", len(data), utils.SimplyToJson(data)))
	}
}

func getDetailPromotion(promoId, outletId interface{}) (map[string]interface{}, error) {
	timeOffset := int64(25200)
	sql := `select p.*,
case (select dayofweek(from_unixtime((select unix_timestamp(now())) + ? , '%Y-%m-%d')))
           when 1 then p.sunday
           when 2 then p.monday
           when 3 then p.tuesday
           when 4 then p.wednesday
           when 5 then p.thursday
           when 6 then p.friday
           when 7 then p.saturday
           else 0
           end                  as active_today
from promotions p
where promotion_id = ?`

	data, err := db.Query(sql, timeOffset, promoId)
	if log.IfError(err) {
		return nil, errors.New("failed to get promo")
	}

	if len(data) == 0 {
		log.Info("cat not get promo with id : %v", promoId)
		return nil, errors.New("promo not found")
	}

	//timeNow := time.Now().Unix()*1000 + timeOffset
	//if utils.ToInt64(data["end_promotion_date"]) < timeNow {
	//	return nil, errors.New("voucher expired")
	//}
	//
	//if utils.ToInt64(data["start_promotion_date"]) > timeNow {
	//	return nil, errors.New("voucher belum berlaku")
	//}
	//
	//if utils.ToInt(data["active_today"]) == 0 {
	//	return nil, errors.New("voucher tidak dapat digunakan hari ini")
	//}

	//hourMinuteNow := utils.ToInt(time.Unix(timeNow, 0).Format("150405"))
	//timeStart := utils.ToInt(strings.Replace(utils.ToString(data["start_promotion_time"]), ":", "", -1))
	//timeEnd := utils.ToInt(strings.Replace(utils.ToString(data["end_promotion_time"]), ":", "", -1))
	//
	//if hourMinuteNow < timeStart || hourMinuteNow > timeEnd {
	//	log.Info("now : %d | start : %d | end : %d", hourMinuteNow, timeStart, timeEnd)
	//	return nil, errors.New("voucher tidak berlaku pada jam ini")
	//}

	result := make(map[string]interface{})
	// if data["promo_type"] == "special_price" {
	// 	specialPrice, err := db.QueryArray("select product_detail_fkid, price, type, qty from promotion_products pp "+
	// 		"join products_detail pd on pp.product_detail_fkid = pd.product_detail_id "+
	// 		"where pd.outlet_fkid = ? and promotion_id = ?", outletId, promoId)
	// 	if log.IfError(err) {
	// 		return nil, errors.New("can not get special price item")
	// 	}
	// 	result["special_price"] = specialPrice
	// }

	products, err := db.QueryArray("select product_detail_fkid, price, type, qty from promotion_products pp "+
		"join products_detail pd on pp.product_detail_fkid = pd.product_detail_id "+
		"where pd.outlet_fkid = ? and promotion_id = ?", outletId, promoId)
	if log.IfError(err) {
		return nil, errors.New("can not get special price item")
	}
	result["products"] = products

	return result, nil
}

func getDetailVoucher(voucher, outletId, adminId interface{}) (map[string]interface{}, error) {
	timeOffset := int64(25200)
	//assuming the voucher is free item
	sql := `
	select p.name, pvc.used, pvc.id as promotion_voucher_code_id, p.term, p.max_qty_promo,
       pvc.max_redeem, pvc.max_redeem_period, pvc.max_redeem_period_value,
       pp.variant_fkid,
       pp.product_detail_fkid,
       pp.price,
       pp.qty,
       pp.type,
	pp.ammount as amount,
	pp.is_percent as is_percent_product,
       p.promotion_id,
       pt.promotion_type_id,
       pt.name as promo_type,
       p.start_promotion_date,
       p.end_promotion_date,
       p.start_promotion_time,
       p.end_promotion_time,
		p.min_order,
       p.discount_type, p.ammount as value, p.is_percent,
		if(p.promo_discount_maximum>0, p.promo_discount_maximum, p.maximum_discount_nominal) as promo_discount_maximum, 
       case (select dayofweek(from_unixtime((select unix_timestamp(now())) + $time_offset , '%Y-%m-%d')))
           when 1 then p.sunday
           when 2 then p.monday
           when 3 then p.tuesday
           when 4 then p.wednesday
           when 5 then p.thursday 
           when 6 then p.friday 
           when 7 then p.saturday 
           else 0
           end                  as active_today,
		if(from_unixtime(start_promotion_date / 1000 + $time_offset, '%Y-%m-%d') > from_unixtime(unix_timestamp() + $time_offset, '%Y-%m-%d'),
          'no', 'yes')                             as date_has_started,
       if(from_unixtime(end_promotion_date / 1000 + $time_offset, '%Y-%m-%d') >= from_unixtime(unix_timestamp() + $time_offset, '%Y-%m-%d'),
          'no', 'yes')                             as date_has_expired,
       if(p.start_promotion_time > $time_now, 'no', 'yes') as has_started,
       if(p.end_promotion_time > $time_now, 'no','yes') as has_expired
from promotion_voucher_code pvc
         join promotions p on pvc.promotion_id = p.promotion_id
         join promotion_types pt on p.promotion_type_id = pt.promotion_type_id
         left join promotion_products pp on p.promotion_id = pp.promotion_id
		 left join products_detail pd on pp.product_detail_fkid = pd.product_detail_id
where pvc.voucher_code = $promo_code
  and p.admin_fkid = $admin_id 
	and (pd.outlet_fkid = $outlet_id OR pd.outlet_fkid is NULL)
 and p.active = 1 
  and p.promotion_id in (select promotion_id from promotion_outlets where outlet_id = $outlet_id )
`
	data, err := db.QueryArrayFun(sql, map[string]interface{}{
		"outlet_id":   outletId,
		"promo_code":  voucher,
		"admin_id":    adminId,
		"time_offset": timeOffset,
		"time_now":    time.Unix(time.Now().Unix()+timeOffset, 0).Format("15:04:05"),
	})

	if log.IfError(err) {
		return nil, errors.New("internal server error")
	}

	if len(data) == 0 {
		return nil, errVoucherNotFound
	}

	if utils.ToInt(data[0]["used"]) >= utils.ToInt(data[0]["max_redeem"]) {
		sql := `select count(*) as cnt from sales_promotion sp  where voucher_code = ?`
		totalUsed, err := db.Query(sql, voucher)
		log.IfError(err)
		if utils.ToInt(totalUsed["cnt"]) < utils.ToInt(data[0]["used"]) {
			log.IfError(fmt.Errorf("voucher: '%s' used: %d - in sales: %d", voucher, data[0]["used"], totalUsed["cnt"]))
		} else {
			return nil, errVoucherExceedLimit
		}
	}

	//timeNow := time.Now().Unix()*1000 + timeOffset
	//if utils.ToInt64(data[0]["end_promotion_date"]) < timeNow {
	//	return nil, errors.New("voucher expired")
	//}
	//
	//if utils.ToInt64(data[0]["start_promotion_date"]) > timeNow {
	//	return nil, errors.New("voucher belum berlaku")
	//}
	//
	if utils.ToInt(data[0]["active_today"]) == 0 {
		return nil, errors.New("voucher tidak dapat digunakan hari ini")
	}
	//
	//hourMinuteNow := utils.ToInt(time.Unix(timeNow, 0).Format("150405"))
	//timeStart := utils.ToInt(strings.Replace(utils.ToString(data[0]["start_promotion_time"]), ":", "", -1))
	//timeEnd := utils.ToInt(strings.Replace(utils.ToString(data[0]["end_promotion_time"]), ":", "", -1))
	//
	//if hourMinuteNow < timeStart || hourMinuteNow > timeEnd {
	//	log.Info("now : %d | start : %d | end : %d", hourMinuteNow, timeStart, timeEnd)
	//	return nil, errors.New("voucher tidak berlaku pada jam ini")
	//}

	if data[0]["date_has_started"] == "no" {
		return nil, errors.New("voucher can not be used yet")
	} else if data[0]["date_has_expired"] == "yes" {
		return nil, errors.New("voucher is expired")
	} else if data[0]["has_started"] == "no" {
		return nil, errors.New("voucher can not be used at this time")
	} else if data[0]["has_expired"] == "yes" {
		return nil, errors.New("voucher can not be used at this time")
	}

	//check if max redeem period set
	maxRedemPeriodValue := utils.ToInt(data[0]["max_redeem_period_value"])
	if data[0]["max_redeem_period"] != nil && maxRedemPeriodValue > 0 {
		//periods: 'monthly', 'weekly', 'yearly', 'daily'
		used, err := getVoucherUsedByPeriod(utils.ToString(data[0]["max_redeem_period"]), utils.ToString(voucher), utils.ToInt(data[0]["promotion_id"]))
		if !log.IfError(err) && used >= maxRedemPeriodValue {
			log.Info("voucher '%s' exceed limit period, max %v | used %v", voucher, maxRedemPeriodValue, used)
			return nil, errVoucherExceedLimit
		}
	}

	//check if activate only for specific outlet
	sql = "select outlet_fkid from promotion_voucher_code_outlets where voucher_fkid = ? and outlet_fkid != 0"
	voucherOutlets, err := db.QueryArray(sql, data[0]["promotion_voucher_code_id"])
	if !log.IfError(err) && len(voucherOutlets) > 0 {
		isActive := false
		for _, voucher := range voucherOutlets {
			if utils.ToInt(voucher["outlet_fkid"]) == utils.ToInt(outletId) {
				isActive = true
			}
		}
		if !isActive {
			return nil, errors.New("voucher tidak dapat digunakan di outlet ini")
		}
	}

	// sql = `SELECT pd.product_detail_id as product_detail_fkid from products_detail pd
	// 	JOIN products p ON pd.product_fkid = p.product_id
	// 	 where pd.outlet_fkid= ?
	// 	 and p.admin_fkid = ?
	// 	and pd.data_status='on' and p.data_status='on'
	// 	and (pd.active = 'on_all' or pd.active='on_sales')
	// 	and p.catalogue_type='product' `
	// productsTmp, err: = db.QueryArray(sql, outletId, adminId)
	// log.IfError(err)

	products := make([]map[string]interface{}, 0)
	productsFree := make([]map[string]interface{}, 0)
	freeIds := make(map[interface{}]bool)
	buyIds := make(map[interface{}]bool)
	for _, promo := range data {
		if promo["type"] == "free_menu" {
			if freeIds[promo["product_detail_fkid"]] == false {
				productsFree = append(productsFree, map[string]interface{}{
					"product_detail_fkid": promo["product_detail_fkid"],
					"qty":                 promo["qty"],
				})
			}
			freeIds[promo["product_detail_fkid"]] = true
		} else {
			if buyIds[promo["product_detail_fkid"]] == false {
				promoFilter := utils.TakesOnly(promo, "product_detail_fkid", "qty", "price", "amount", "type")
				promoFilter["is_percent"] = promo["is_percent_product"]
				products = append(products, promoFilter)
			}
			buyIds[promo["product_detail_fkid"]] = true
		}
	}

	result := map[string]interface{}{
		"code":                   voucher,
		"source":                 "uniq",
		"term":                   data[0]["term"],
		"type":                   formatPromoType(utils.ToString(data[0]["promo_type"])),
		"type_id":                data[0]["promotion_type_id"],
		"name":                   data[0]["name"],
		"min_order":              data[0]["min_order"],
		"discount_type":          data[0]["discount_type"],
		"value":                  data[0]["value"],
		"promo_discount_type":    "percent",
		"promo_discount_maximum": data[0]["promo_discount_maximum"],
		"promotion_id":           data[0]["promotion_id"],
		"max_qty_promo":          data[0]["max_qty_promo"],
		"promo": map[string]interface{}{
			"products":      products,
			"products_free": productsFree,
		},
	}
	if utils.ToInt(data[0]["is_percent"]) == 0 {
		result["promo_discount_type"] = "nominal"
	}

	result["max_redeem"] = data[0]["max_redeem"]
	result["used"] = data[0]["used"]

	//result["promo_type"] = formatPromoType(utils.ToString(data[0]["promo_type"]))
	//result["promo_type_id"] = data[0]["promotion_type_id"]
	//result["name"] = data[0]["name"]
	//result["min_order"] = data[0]["min_order"]
	//result["promotion_id"] = data[0]["promotion_id"]
	//result["discount_type"] = data[0]["discount_type"]
	//result["value"] = data[0]["value"]
	//result["promo_discount_type"] = "percent"
	//result["products"] = products
	//result["products_free"] = productsFree

	//first, check in case some bad thing happen
	errorCollection := ""
	if result["type"] == nil {
		errorCollection += "promo type is NULL \n"
	}
	if len(products) == 0 {
		errorCollection += "products is empty"
	}

	if errorCollection != "" {
		log.Error("getting detail voucher of '%s' is error : %s", voucher, errorCollection)
	}

	return result, nil
}

func getVoucherUsedByPeriod(periodUnit string, voucherCode string, promotionId int) (int, error) {
	formats := map[string]string{
		"yearly":  "%Y",
		"monthly": "%m/%Y",
		"weekly":  "%v/%Y",
		"daily":   "%d/%m/%Y",
	}

	sql := `SELECT count(*) as total from sales_promotion sp 
	join sales s on s.sales_id =sp.sales_fkid 
	where s.status = 'Success'
	and FROM_UNIXTIME(s.time_created/1000, '$format') = FROM_UNIXTIME(UNIX_TIMESTAMP() , '$format')
	and sp.voucher_code =? and sp.promotion_fkid =? `
	sql = strings.ReplaceAll(sql, "$format", formats[periodUnit])

	data, err := db.Query(sql, voucherCode, promotionId)
	return utils.ToInt(data["total"]), err
}

// format promo type from database to convenience type
func formatPromoType(promoType string) string {
	return strings.Replace(strings.ToLower(promoType), " ", "_", -1)
}

// voucher can be : deals (promotion buy), voucher, external source (like behave)
func VoidPromotion(ctx *fasthttp.RequestCtx) {
	voucherCode := string(ctx.Request.PostArgs().Peek("voucher_code"))
	if strings.TrimSpace(voucherCode) == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{
			Status:  false,
			Message: "voucher code is required",
		})
		return
	}

	outletId := ctx.Request.PostArgs().Peek("outlet_id")
	source := ctx.Request.PostArgs().Peek("source")
	result := models.ResponseAny{Status: true}
	device := ctx.Request.Header.Peek("Device")

	if source == nil || strings.TrimSpace(string(source)) == "" {
		source = []byte("uniq")
	}

	headers := make(map[string]string)
	ctx.Request.Header.VisitAll(func(key, value []byte) {
		headers[string(key)] = string(value)
	})
	log.Info("%s, header: %s", voucherCode, utils.SimplyToJson(headers))
	log.Info("[voucher-count] request void voucher : '%s' - source : %s, device: %s", voucherCode, source, device)

	isPromotionBuy := utils.IsNumber(voucherCode)
	promoId := utils.DecryptWithKey(voucherCode, utils.KEY_PROMOTION_BARCODE)
	isNumber := utils.IsNumber(promoId)
	log.Info("%s -- after decrypt : %s -- is decrypted value number: %v", voucherCode, promoId, isNumber)
	if promoId != "" && isNumber {
		voucherCode = promoId
		isPromotionBuy = true
	}

	result.Status = removePromo(voucherCode, promoId, utils.ToString(source), utils.ToInt(outletId), isPromotionBuy)

	_ = json.NewEncoder(ctx).Encode(result)
}

func updatePromotionBuyStatus(promotionBuyId interface{}) bool {
	resp, err := db.Update("promotion_buy", map[string]interface{}{"status": "available"}, "promotion_buy_id = ?", promotionBuyId)
	if log.IfError(err) {
		return false
	}
	if count, _ := resp.RowsAffected(); err == nil {
		log.Warn("void voucher (promotion_buy), %d rows effected for this update. PromotionBuyId : %v", count, promotionBuyId)
	}
	return true
}

func removePromo(code, promoId, source string, outletId int, isPromotionBuy bool) bool {
	log.Info("[voucher-count] void voucher : '%s' - source : %s, outlet: %v", code, source, outletId)
	if source == "behave" {
		_ = behave.VoidBehaveVoucher(code, utils.ToInt(outletId))
	}

	if utils.IsNumber(code) && isPromotionBuy {
		if updatePromotionBuyStatus(code) {
			return true
		}
	}

	sql := "update promotion_voucher_code set used = used-1 where voucher_code = ? "
	params := make([]interface{}, 0)
	params = append(params, code)

	if strings.TrimSpace(promoId) != "" {
		sql += " and promotion_id = ? "
		params = append(params, promoId)
	} else {
		sql += " and admin_fkid = (select admin_fkid from outlets where outlet_id = ?) "
		params = append(params, outletId)
	}

	resp, err := db.GetDb().Exec(sql, params...)
	if log.IfError(err) {
		return false
	}

	totalRemoved := int64(0)
	if totalRemoved, _ = resp.RowsAffected(); err == nil && totalRemoved <= 0 {
		log.Warn("void voucher (promotion_voucher_code), no row effected for this update. Voucher Code : %v", code)
	}

	if totalRemoved == 0 && utils.IsNumber(code) {
		return updatePromotionBuyStatus(code)
	}

	return true
}
