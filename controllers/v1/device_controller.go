package v1

import (
	"encoding/json"
	"fmt"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"time"
)

func RequestDeviceToLogout(ctx *fasthttp.RequestCtx) {
	deviceId := ctx.QueryArgs().Peek("device_id")
	sql := `
select firebase_token
from devices
where device_id = ? limit 1`
	device, err := db.Query(sql, deviceId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	fmt.Println("token: ", device)

	response := models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000}
	_ = json.NewEncoder(ctx).Encode(response)
}
