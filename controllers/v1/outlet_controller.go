package v1

import (
	"bytes"
	"encoding/json"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

func GetOutletByAdmin(ctx *fasthttp.RequestCtx) {
	outletIds := ctx.Request.Header.Peek("outlet")
	adminId := ctx.Request.Header.Peek("admin_id")
	role := ctx.Request.Header.Peek("role")
	log.Info("request outlet with admin : %v | %v", string(adminId), string(role))
	ctx.Request.Header.VisitAll(func(key, value []byte) {
		log.Info("[header] %v: %v", string(key), string(value))
	})

	outlets, err := db.QueryArray("SELECT outlets.*, trim(name) as name, trim(receipt_note) as receipt_note FROM outlets WHERE admin_fkid=? AND data_status='on' ORDER BY name", adminId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	isRequestOutOfPermission := false
	unAuthOutlet := ""
	var data []map[string]interface{}
	for _, outlet := range outlets {
		if found := Any(strings.Split(string(outletIds), ","), utils.ToString(outlet["outlet_id"])); string(role) == utils.ROLE_OWNER || found {
			if os.Getenv("server") == "staging" || os.Getenv("server") == "demo" {
				outlet["receipt_logo"] = ""
			}
			data = append(data, outlet)
		} else {
			isRequestOutOfPermission = true
			unAuthOutlet += utils.ToString(outlet["outlet_id"])
		}
	}

	if isRequestOutOfPermission {
		log.Error("adminId %s request out of permission. unAuthOutlet : %s - allowed outlet : %s", adminId, unAuthOutlet, outletIds)
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: time.Now().Unix() * 1000, Data: data})
}

func GetOutletDetail(ctx *fasthttp.RequestCtx) {
	adminId := ctx.Request.Header.Peek("admin_id")
	outletId := ctx.UserValue("outletId")

	outlets, err := db.Query("SELECT outlets.*, trim(name) as name, trim(receipt_note) as receipt_note FROM outlets WHERE admin_fkid=? AND outlet_id=? ", adminId, outletId)
	if !log.IfError(err) {
		if len(outlets) > 0 {
			// if outlets["receipt_logo"] != nil && !strings.HasPrefix(utils.ToString(outlets["receipt_logo"]), "https://") {
			// 	//outlets["receipt_logo"] = fmt.Sprintf("%s/assets/images/outlets/%s/%s", os.Getenv("base_url"), string(adminId), utils.ToString(outlets["receipt_logo"]))
			// }
			//40:Chapter JCM - 41:Chapter Ambarrukmo - 42:Chapter Jakal Atas - 43:Chapter Megatruh - 44:Chapter Cokroaminoto - 45:Chapter Godean - 46:Chapter Lippo Mall - 47:Chapter Hartono Mall - 50:Panda Bazar - 51:Chapter Babarsari - 52:Chapter Tamansiswa - 59:Chapter Tamansiswa ed - 61:Chapter Tamansiswa 10 - 62:Chapter Kridosono - 65:Panda Training - 69:Chapter Demangan - 80:Panda Bar & Dimsum - 81:Chapter Monjali - 121:Yamie Ekspresss - 124:Chapter Tembalang - 164:Chapter Pleburan - 166:Chapter Cipinang - 167:Chapter Tawang - 178:Office BOEN BROTHERS - 179:ALIVE Timoho ( Task ) - 186:Chapter Paragon Mall
			env := os.Getenv("server")
			if env == "staging" || env == "demo" || env == "development" {
				outletId := utils.ToInt(outlets["outlet_id"])
				if outletId == 1 || string(adminId) == "17" || env == "development" {
					outlets["experiment_config"] = utils.SimplyToJson(map[string]bool{
						"printer": true,
					})
					// log.Info("outlet of %v : %v", outletId, utils.SimplyToJson(outlets))
				}
				// else if outletId != 52 && outletId != 69 {
				// 	outlets["receipt_logo"] = ""
				// }
			} else if env == "production" && (string(adminId) == "143000" || string(adminId) == "491" || string(adminId) == "525") || string(adminId) == "572" { //143: rudi, 491:rememberMe, 525: Kedai Padma
				outlets["experiment_config"] = utils.SimplyToJson(map[string]bool{
					"printer": true,
				})
				// log.Info("outlet of %v : %v", outletId, utils.SimplyToJson(outlets))
			}
		} else {
			log.Warn("request outlet %s for admin %s not found! error might be happen", outletId, adminId)
		}
	}

	//for instant payment
	paymentConf, err := db.Query("select value from order_configuration where admin_id = ?", adminId)
	log.IfError(err)
	outlets["payment_instant"] = 0
	if len(paymentConf) > 0 {
		outlets["payment_instant"] = 1
	}

	_ = json.NewEncoder(ctx).Encode(models.Response{Status: true, Millis: time.Now().Unix() * 1000, Data: outlets})
}

func GetEmployeeByOutlet(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	//this is only for temporary, if all users updated to version min 1.30, this would be unnecessary anymore
	//because, at previous version, if server return only data greater than last sync, in android, all data will replaced with that new data
	//if no data return, it's mean in android, all data is lost
	byLastSync := ctx.UserValue("byLastSync")
	if byLastSync == nil {
		lastSync = 0
	}

	outlet := ctx.Request.Header.Peek("outlet")
	if found := Any(strings.Split(string(outlet), ","), outletId.(string)); !found {
		ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		return
	}

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	data, err := db.QueryArray("SELECT e.* FROM employee_outlet eo JOIN "+
		"employee e ON eo.employee_fkid=e.employee_id WHERE e.access_status_mobile='activated' "+
		"AND eo.outlet_fkid=? AND e.data_modified >= ? AND data_status = 'on' ORDER BY e.name", outletId.(string), lastSync)
	log.IfError(err)

	ctx.SetContentType("application/json")
	log.IfError(json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Code: 0, Millis: millis, Data: data}))
}

func GetMobileRole(ctx *fasthttp.RequestCtx) {
	defer utils.Cleanup(ctx)
	response := models.ResponseString{Status: true, Millis: time.Now().Unix() * 1000}
	employeeId := ctx.UserValue("employeeId")
	lastSync := ctx.UserValue("lastSync")

	//this is only for temporary, if all users updated to version min 1.30, this would be unnecessary anymore
	//because, at previous version, if server return only data greater than last sync, in android, all data will replaced with that new data
	//if no data return, it's mean in android, all data is lost
	byLastSync := ctx.UserValue("byLastSync")
	if byLastSync == nil {
		lastSync = 0
	}

	data, err := db.Query("SELECT role_mobile FROM employee WHERE employee_id=? and data_modified >= ?", employeeId, lastSync)
	log.IfError(err)

	if data["role_mobile"] == nil {
		response.Status = false
		response.Message = "Employee not found!"
	} else {
		response.Data = data["role_mobile"].(string)
	}

	_ = json.NewEncoder(ctx).Encode(response)
}

func GetShiftByAdmin(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	adminId := ctx.Request.Header.Peek("admin_id")
	lastSync := ctx.UserValue("lastSync")

	if lastSync == nil {
		lastSync = 0
	}

	data, err := db.QueryArray("SELECT * FROM shift WHERE admin_fkid=? AND data_modified >= ?", string(adminId), lastSync)
	log.IfError(err)

	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func AddTable(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	var table models.DiningTable
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&table)
	log.IfError(err)

	if !auth.ValidateOutletId(ctx, table.OutletFkid) {
		return
	}

	data, err := db.QueryArray("SELECT dining_table_id FROM dining_table WHERE dining_table_id=?", table.DiningTableID)
	log.IfError(err)

	response := models.Response{Status: true, Millis: millis}
	if len(data) > 0 {
		//limit table name
		if len(table.TableName) > 15 {
			table.TableName = table.TableName[:15]
		}
		res, err := db.GetDb().Exec("UPDATE dining_table SET status=? , table_name=?, time_modified=? WHERE dining_table_id=?",
			table.Status, table.TableName, time.Now().Unix()*1000, table.DiningTableID)
		if !log.IfError(err) {
			row, _ := res.RowsAffected()
			response.Message = "Data updated!"
			if row <= 0 {
				response.Message = "UpdateDb Fail!"
			}
		} else {
			response.Status = false
			response.Message = err.Error()
		}
	} else {
		res, err := db.GetDb().Exec("INSERT INTO dining_table (dining_table_id, outlet_fkid, status, table_name, time_created, time_modified) "+
			"VALUES(?,?,?,?,?,?)", table.DiningTableID, table.OutletFkid, table.Status, table.TableName, table.TimeCreated, time.Now().Unix()*1000)
		if err != nil {
			response.Status = false
			response.Message = "Insert Fail. " + err.Error()
		} else {
			row, _ := res.RowsAffected()
			response.Message = "Data inserted!"
			if row <= 0 {
				response.Message = "Insert Fail!"
			}
		}
	}

	_ = json.NewEncoder(ctx).Encode(response)
}

func UpdateTable(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	var table models.DiningTable
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&table)
	log.IfError(err)

	response := models.Response{Status: true, Millis: millis}
	res, err := db.GetDb().Exec("UPDATE dining_table SET status=? , table_name=?, time_modified=? WHERE dining_table_id=?",
		table.Status, table.TableName, time.Now().Unix()*1000, table.DiningTableID)
	log.IfError(err)

	row, _ := res.RowsAffected()
	response.Message = "Data updated!"
	if row <= 0 {
		response.Message = "No UpdateDb Effected"
	}

	_ = json.NewEncoder(ctx).Encode(response)
}

func GetTable(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	data, err := db.QueryArray("SELECT * FROM dining_table WHERE outlet_fkid=? AND time_modified >= ?", outletId, lastSync)
	log.IfError(err)

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func DeleteTable(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	tableId := string(ctx.Request.PostArgs().Peek("dining_table_id"))

	data, err := db.Query("SELECT  * FROM dining_table WHERE dining_table_id = ?", tableId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	outletId, _ := strconv.Atoi(utils.ToString(data["outlet_fkid"]))
	if !auth.ValidateOutletId(ctx, outletId) {
		return
	}

	_, err = db.GetDb().Exec("DELETE FROM dining_table WHERE dining_table_id = ?", tableId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: millis, Data: data})
}

func GetDeleteHistory(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	adminId := ctx.Request.Header.Peek("admin_id")
	lastSync := ctx.UserValue("lastSync")
	outletId := ctx.QueryArgs().Peek("outlet_id")

	data, err := db.QueryArray("SELECT * FROM history_delete WHERE (admin_fkid = ? OR outlet_fkid = ?) AND date_time >= ?", adminId, outletId, lastSync)
	log.IfError(err)

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func AddPrinter(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	printer := models.Printer{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&printer)
	if log.IfError(err) {
		log.Info("invalid body: %s", ctx.PostBody())
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"error": err})
		return
	}

	log.Info("add new printer : %s", utils.SimplyToJson(printer))

	if printer.AdminFkid == 0 && (printer.OutletFkid == 380 || printer.OutletFkid == 379) {
		printer.AdminFkid = 209
	}

	if printer.AdminFkid == 0 && (printer.OutletFkid == 376 || printer.OutletFkid == 377 || printer.OutletFkid == 378) {
		printer.AdminFkid = 208
	}

	adminId := utils.ToInt(ctx.Request.Header.Peek("admin_id"))
	if adminId != printer.AdminFkid {
		//ctx.SetStatusCode(fasthttp.StatusForbidden)
		//log.Error("adding printer failed because admin-id doesn't match. current adminId : %d | from client : %v", adminId, printer.AdminFkid)
		//return
		printer.AdminFkid = adminId
	}

	response := models.ResponseAny{Status: true, Millis: millis}

	if printer.SettingPrintreceipt == "" {
		printer.SettingPrintreceipt = "0"
	}
	if printer.SettingClosingshift == "" {
		printer.SettingClosingshift = "0"
	}
	if printer.SettingPrintorder == "" {
		printer.SettingPrintorder = "0"
	}
	if printer.SettingPrintreceiptJumlah == "" {
		printer.SettingPrintreceiptJumlah = "0"
	}

	data, err := db.Query("SELECT printer_setting_id FROM setting_printer WHERE outlet_fkid=? AND mac_address=? LIMIT 1", printer.OutletFkid, printer.MacAddress)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(data) > 0 {
		printerData := map[string]interface{}{
			"setting_printpapersize":      printer.SettingPrintpapersize,
			"setting_printreceipt":        printer.SettingPrintreceipt,
			"setting_printorder":          printer.SettingPrintorder,
			"setting_closingshift":        printer.SettingClosingshift,
			"setting_printreceipt_jumlah": printer.SettingPrintreceiptJumlah,
			"time_modified":               time.Now().Unix() * 1000,
		}

		if printer.PrinterName != "" {
			printerData["printer_name"] = printer.PrinterName
		}

		_, err := db.UpdateDb("setting_printer", printerData, map[string]interface{}{"printer_setting_id": data["printer_setting_id"]})
		log.IfError(err)

		response.Data = utils.ToInt64(data["printer_setting_id"])
		response.Message = "Printer updated successfully"
		response.Status = false
	} else {
		res, err := db.Insert("setting_printer", map[string]interface{}{
			"mac_address":                 printer.MacAddress,
			"printer_name":                printer.PrinterName,
			"outlet_fkid":                 printer.OutletFkid,
			"admin_fkid":                  printer.AdminFkid,
			"type":                        printer.Type,
			"setting_printpapersize":      printer.SettingPrintpapersize,
			"setting_printreceipt":        printer.SettingPrintreceipt,
			"setting_printorder":          printer.SettingPrintorder,
			"setting_closingshift":        printer.SettingClosingshift,
			"setting_printreceipt_jumlah": printer.SettingPrintreceiptJumlah,
			"time_created":                printer.TimeCreated,
			"time_modified":               time.Now().Unix() * 1000,
		})
		if log.IfErrorSetStatus(ctx, err) {
			_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"error": err})
			return
		}
		id, _ := res.LastInsertId()
		response.Data = id
		response.Message = "Successfully inserted!"
	}

	log.Info("add print -> %v", response)
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetPrinter(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	if lastSync == nil {
		lastSync = 0
	}

	data, err := db.QueryArray("SELECT * FROM setting_printer WHERE outlet_fkid=? AND time_modified >= ?", outletId, lastSync)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func DeletePrinter(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	printer := models.Printer{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&printer)
	utils.CheckErr(err)

	adminId := ctx.Request.Header.Peek("admin_id")
	adminIdInt, _ := strconv.Atoi(string(adminId))
	if adminIdInt != printer.AdminFkid {
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return
	}

	res, err := db.GetDb().Exec("DELETE FROM setting_printer WHERE mac_address = ? AND outlet_fkid=?", printer.MacAddress, printer.OutletFkid)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	response := models.ResponseString{Millis: millis, Status: true, Message: "Successfully deleted!"}
	row, err := res.RowsAffected()
	if row <= 0 {
		response.Status = false
		response.Message = "No row effected!"
	}

	_ = json.NewEncoder(ctx).Encode(response)
}

func GetPrinterClosingShift(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	if lastSync == nil {
		lastSync = 0
	}

	data, err := db.QueryArray("SELECT spc.* FROM setting_printer_closingshift spc JOIN setting_printer sp "+
		"ON sp.printer_setting_id=spc.printer_setting_fkid WHERE outlet_fkid=? and spc.data_modified >= ?", ctx.UserValue("outletId"), lastSync)
	log.IfError(err)

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func GetPrinterTicket(ctx *fasthttp.RequestCtx) {
	response := models.ResponseArray{Millis: time.Now().Unix() * 1000, Status: true}
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	data, err := db.QueryArray("SELECT spt.*, spts.* FROM setting_printer_ticket spt JOIN setting_printer sp "+
		"ON spt.printer_setting_fkid = sp.printer_setting_id JOIN setting_printer_ticket_subcategory spts "+
		"ON spt.printersetting_ticket_id = spts.printersetting_ticket_fkid WHERE sp.outlet_fkid=? "+
		"AND (spt.data_modified >= ? OR spts.data_modified >= ?) ORDER BY spt.printersetting_ticket_id", ctx.UserValue("outletId"), lastSync, lastSync)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	lastId := int64(0)
	ticketColumns := []string{"printersetting_ticket_id", "name", "setting_type", "printer_setting_fkid", "data_created", "data_modified"}
	detailColumns := []string{"printersetting_ticket_fkid", "product_subcategory_fkid", "data_modified"}
	tickets := make([]map[string]interface{}, 0)
	ticket := make(map[string]interface{})
	details := make([]map[string]interface{}, 0)

	for index, printer := range data {
		if printer["printersetting_ticket_id"] != lastId {
			lastId = printer["printersetting_ticket_id"].(int64)

			details = make([]map[string]interface{}, 0)
			ticket = make(map[string]interface{})
			for _, col := range ticketColumns {
				ticket[col] = printer[col]
			}
		}

		detail := make(map[string]interface{})
		for _, col := range detailColumns {
			detail[col] = printer[col]
		}
		details = append(details, detail)

		if index+1 == len(data) || data[index+1]["printersetting_ticket_id"] != lastId {
			ticket["detail"] = details
			tickets = append(tickets, ticket)
		}
	}
	response.Data = tickets
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetShiftAvailable(ctx *fasthttp.RequestCtx) {
	response := models.ResponseArray{Status: true, Millis: time.Now().Unix() * 1000}
	adminId := ctx.Request.Header.Peek("admin_id")
	outletId := ctx.UserValue("outletId")
	timeOffset := ctx.UserValue("timeOffset")

	log.Info("get shift available: %s - timeOffset: %s", outletId, timeOffset)
	if valid := auth.ValidateOutlet(ctx); !valid {
		log.Info("validation outlet failed!")
		return
	}

	//tmp fix
	if outletId == 435 {
		timeOffset = 28800
	}

	sql := `
SELECT *
FROM shift
WHERE admin_fkid = ?
  AND shift_id NOT IN
      (SELECT shift_fkid
       FROM open_shift
       WHERE DATE(FROM_UNIXTIME(time_open / 1000 + ?)) = DATE(FROM_UNIXTIME(UNIX_TIMESTAMP() + ?))
         AND outlet_fkid = ?)
  AND shift_id in (select shift_fkid from shift_outlet where admin_fkid = ? and outlet_fkid = ?) `

	data, err := db.QueryArray(sql, string(adminId), timeOffset, timeOffset, outletId, string(adminId), outletId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	log.Info("shift for %s are: %s", outletId, utils.SimplyToJson(data))
	response.Data = data
	log.IfError(json.NewEncoder(ctx).Encode(response))
}

func GetActiveShift(ctx *fasthttp.RequestCtx) {
	outletId := ctx.UserValue("outletId")
	deviceId := ctx.Request.Header.Peek("Device")

	sql := `
select *
from open_shift
where time_close is null
  and outlet_fkid = ?
order by time_open
limit 1 `
	data, err := db.Query(sql, outletId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(data) > 0 {
		go func(data map[string]interface{}, deviceId string) {
			_, err = db.Update("devices", map[string]interface{}{
				"device_status":   "on",
				"open_shift_fkid": data["open_shift_id"],
			}, "imei = ?", deviceId)
		}(data, string(deviceId))
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Data: data})
}

func GetActiveDevices(ctx *fasthttp.RequestCtx) {
	response := models.ResponseArray{Status: true, Millis: time.Now().Unix() * 1000}
	openShiftId := ctx.UserValue("openShiftId")
	deviceId := ctx.UserValue("deviceId")

	data, err := db.QueryArray("SELECT d.*, d.name AS device_name FROM devices d WHERE open_shift_fkid=? AND imei !=? AND device_status='on'", openShiftId, deviceId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	log.Info("checking active device for openShiftId: %v | checker deviceId: %v | active devices: %v", openShiftId, deviceId, data)

	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetDeviceStatus(ctx *fasthttp.RequestCtx) {
	deviceId := ctx.UserValue("deviceId")
	//adminId := ctx.Request.Header.Peek("admin_id")

	data, err := db.Query("SELECT * FROM devices WHERE imei=? LIMIT 1", deviceId)
	log.IfError(err)

	go func() {
		_, err = db.Update("devices", map[string]interface{}{
			"last_sync": time.Now().Unix() * 1000,
		}, "imei = ? ", deviceId)
		log.IfError(err)
	}()

	_ = json.NewEncoder(ctx).Encode(models.Response{Status: true, Data: data, Millis: time.Now().Unix() * 1000})
}

func GetDevicesByStatus(ctx *fasthttp.RequestCtx) {
	status := ctx.UserValue("status")
	outletId := ctx.UserValue("outletId")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	data, err := db.QueryArray("SELECT * FROM devices WHERE outlet_fkid = ? AND device_status = ?", outletId, status)
	utils.CheckErr(err)

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: time.Now().Unix() * 1000, Data: data})
}

func UpdateLastSyncDevice(ctx *fasthttp.RequestCtx) {
	deviceId := string(ctx.Request.Header.Peek("Device"))
	_, err := db.UpdateDb("devices", map[string]interface{}{"last_sync": time.Now().Unix() * 1000}, map[string]interface{}{"imei": deviceId})
	log.IfError(err)
	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: time.Now().Unix() * 1000})
}

func GetBank(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")
	adminId := ctx.Request.Header.Peek("admin_id")

	if lastSync == nil {
		lastSync = 0
	}

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	sql := `
SELECT pmb.*, 
if (pmb.data_status = 'on', pmbd.data_status, pmb.data_status) as data_status 
FROM payment_media_bank_detail pmbd 
JOIN payment_media_bank pmb  
ON pmb.bank_id=pmbd.bank_fkid WHERE outlet_fkid=? AND admin_fkid=?
AND provider is NULL
ORDER BY pmb.name
`
	data, err := db.QueryArray(sql, outletId, adminId)
	log.IfError(err)

	// data = append(data, map[string]interface{}{
	// 	"bank_id":       9999,
	// 	"name":          "",
	// 	"no_rekening":   "1",
	// 	"owner":         "a",
	// 	"admin_fkid":    0,
	// 	"data_created":  time.Now(),
	// 	"data_modified": time.Now(),
	// 	"data_status":   "off",
	// })

	// var logs strings.Builder
	// for _, row := range data {
	// 	logs.WriteString(fmt.Sprintf("%v:%v, ", row["bank_id"], row["name"]))
	// }
	// log.Info("%v bankList: %v", outletId, logs.String())

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func Any(vs []string, t string) bool {
	for _, v := range vs {
		if v == t {
			return true
		}
	}
	return false
}
