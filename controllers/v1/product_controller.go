package v1

import (
	"bytes"
	"encoding/json"
	"fmt"
	"image/jpeg"
	"io"
	"io/ioutil"
	"mime"
	"mime/multipart"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

func GetProduct(ctx *fasthttp.RequestCtx) {
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")
	millis := time.Now().Unix() * 1000

	if valid := auth.ValidateOutlet(ctx); !valid {
		log.Info("request product with unaut outletId... %v", outletId)
		return
	}

	//minus by 3 seconds
	// lastSync = utils.ToInt64(lastSync) - 3000
	if utils.ToInt64(lastSync) < 0 {
		lastSync = 0
	}

	// 	//first lets do check the data
	// 	sql := `select count(*) as cnt
	// 	FROM products p
	//          JOIN products_detail pd ON p.product_id = pd.product_fkid
	// WHERE pd.outlet_fkid = ?
	//   AND (p.data_modified >= ? OR pd.data_modified >= ? )`

	// 	count, err := db.Query(sql, outletId, lastSync, lastSync)
	// 	if log.IfErrorSetStatus(ctx, err) {
	// 		return
	// 	}

	// 	log.Info("total product: %v | outlet: %v | lastSnc: %v", count["cnt"], outletId, lastSync)
	// if utils.ToInt(count["cnt"]) == 0 {
	// 	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: utils.ToInt64(lastSync)})
	// 	return
	// }

	sql := `SELECT *,
		if(p.data_status = 'off', p.data_status, pd.data_status) as data_status_old,
		if(p.data_status = 'off', p.data_status, if(pd.active = 'off', pd.active, pd.data_status)) as data_status,
		coalesce(pdv.variant_sku, p.sku)                         as sku,
		coalesce(pdv.variant_barcode, p.barcode)                 as barcode,
		GREATEST(p.data_modified, pd.data_modified) as data_modified
	FROM products p
			JOIN products_detail pd ON p.product_id = pd.product_fkid
			left join products_detail_variant pdv on pd.variant_fkid = pdv.variant_id
	WHERE outlet_fkid = ?
  AND (p.data_modified >= ? OR pd.data_modified >= ? )`

	// repo := db.Repository{Conn: db.GetDb()}
	// var products []models.ProductWithDetailAndVariant
	// err = repo.Prepare(sql, outletId, lastSync, lastSync).Get(&products)

	products, err := db.QueryArray(sql, outletId, lastSync, lastSync)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	// millis := time.Now().Unix() * 1000

	//millis return in the last product modified
	if cast.ToInt(outletId) != 51 { //51: babarsari for testing
		millis = int64(0)
		for _, p := range products {
			if lastModified := utils.ToInt64(p["data_modified"]); lastModified > millis {
				millis = lastModified
			}
		}
	}

	log.Info("%v | product last sync: %v | total: %v", outletId, millis, len(products))
	if millis == 0 && len(products) > 0 {
		log.Info("top product: %v", utils.SimplyToJson(products[0]))
	}
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: millis, Data: products})
}

func UpdateProduct(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: true}
	product := models.ProductAndDetail{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&product)
	log.IfError(err)

	if !auth.ValidateOutletId(ctx, product.OutletFkid) {
		return
	}

	res, err := db.Update("products_detail", map[string]interface{}{
		"stock":         product.Stock,
		"data_modified": time.Now().Unix() * 1000,
		"price_sell":    product.PriceSell,
	}, "product_detail_id=?", product.ProductDetailID)
	//res, err := db.GetDb().Exec("UPDATE products_detail SET stock=? , data_modified=? WHERE product_detail_id=?", product.Stock, time.Now().Unix()*1000, product.ProductDetailID)
	if !log.IfError(err) {
		row, _ := res.RowsAffected()
		if row > 0 {
			response.Message = "product detail successfully updated!"
		} else {
			response.Message = "update not effected any data (product_detail)"
			log.Debug("update product not effected any data. product_detail_id = %d", product.ProductDetailID)
		}
	}

	_ = json.NewEncoder(ctx).Encode(response)
}

func AddProduct(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	adminId := ctx.Request.Header.Peek("admin_id")
	product := models.Product{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&product)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	if !auth.ValidateOutletId(ctx, product.OutletFkid) {
		return
	}

	data := map[string]interface{}{
		"name":                          product.Name,
		"catalogue_type":                "product",
		"product_type_fkid":             product.ProductTypeFkid,
		"product_category_fkid":         product.ProductCategoryFkid,
		"product_subcategory_fkid":      product.ProductSubcategoryFkid,
		"purchase_report_category_fkid": product.PurchaseReportCategoryFkid,
		"unit_fkid":                     product.UnitFkid,
		"stock_management":              product.StockManagement,
		"barcode":                       product.Barcode,
		"sku":                           product.Sku,
		"photo":                         "",
		"admin_fkid":                    adminId,
		"data_created":                  product.DataCreated,
		"data_modified":                 time.Now().Unix() * 1000,
		"data_status":                   "on",
	}

	res, err := db.Insert("products", data)
	if log.IfError(err) {
		fmt.Println("Insert into product with name : ", product.Name, " FAILED. - ", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}

	productId, _ := res.LastInsertId()

	data = map[string]interface{}{
		"product_fkid":         productId,
		"outlet_fkid":          product.OutletFkid,
		"price_buy_start":      product.PriceBuyStart,
		"price_buy":            product.PriceBuy,
		"price_sell":           product.PriceSell,
		"voucher":              "on",
		"discount":             "on",
		"active":               product.Active,
		"transfer_markup_type": "nominal",
		"transfer_markup":      0,
		"data_modified":        time.Now().Unix() * 1000,
		"stock":                "available",
		"data_status":          "on",
	}

	res, err = db.Insert("products_detail", data)
	if log.IfErrorSetStatus(ctx, err) {
		log.Debug("Insert into product detail with name : ", product.Name, " FAILED. - ", err)
		return
	}

	productDetailId, _ := res.LastInsertId()

	products, err := db.Query("SELECT * FROM products_detail pd JOIN products p "+
		" ON pd.product_fkid=p.product_id WHERE pd.product_detail_id = ?", productDetailId)
	if log.IfErrorSetStatus(ctx, err) {
		log.Debug("Get product record (after insert) with name : ", product.Name, " FAILED. - ", err)
		return
	}

	//if len(product.Photo) > 10 {
	//	go base64ToImage(product.Photo, "assets/product/" + string(adminId) + "/test.jpg")
	//}

	//Upload Image
	//uploadImage(ctx, "")

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: millis, Data: products})
}

func AddProductMultipart(ctx *fasthttp.RequestCtx) {
	timeStart := time.Now()
	millis := time.Now().Unix() * 1000
	adminId := ctx.Request.Header.Peek("admin_id")

	//formValue := GetFormValue(ctx, []string{"outlet_fkid", "name"})
	formValue := GetFormValueMap(ctx)
	form, _ := ctx.MultipartForm()
	log.Debug(utils.SimplyToJson(formValue))
	log.Info("product name : %s", form.Value["name"][0])
	log.Info("adding product: %v", utils.SimplyToJson(formValue))

	outletId := utils.StringToInt(formValue["outlet_fkid"])
	if outletId == 0 {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Millis: millis, Message: "outlet_fkid can not be empty"})
		return
	}

	if !auth.ValidateOutletId(ctx, outletId) {
		return
	}

	//validate input
	inputKeys := map[string]string{
		"product_category_fkid": "product_category_name",
		"unit_fkid":             "unit_name",
	}

	for keyId, keyName := range inputKeys {
		if cast.ToInt(formValue[keyId]) == 0 && formValue[keyName] == "" {
			ctx.SetStatusCode(fasthttp.StatusBadRequest)
			_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Millis: millis, Message: fmt.Sprintf("'%v' or '%v' can not be empty", keyId, keyName)})
			return
		}
	}

	imgFileName := ""
	if len(formValue["image"]) > 0 {
		imgFileName = utils.RandStringBytes(16) + "_" + string(adminId) + strconv.FormatInt(millis, 10) + ".jpg"
	}

	productTypeIdChan := make(chan int64)
	categoryIdChan := make(chan int64)
	subCategoryIdChan := make(chan int64)
	purchaseRepCatIdChan := make(chan int64)
	unitIdIdChan := make(chan int64)

	defaultCategoryName := formValue["product_category_name"]

	if utils.ToInt(formValue["product_type_fkid"]) == 0 {
		if formValue["product_type_name"] == "" {
			formValue["product_type_name"] = defaultCategoryName
		}
		go addNewItemWithCheck(productTypeIdChan, string(adminId), formValue["product_type_name"], "products_type", "products_type_id", nil)
	} else {
		go setValueToChan(productTypeIdChan, utils.ToInt64(formValue["product_type_fkid"]))
	}

	if utils.ToInt(formValue["product_category_fkid"]) == 0 {
		go addNewItemWithCheck(categoryIdChan, string(adminId), formValue["product_category_name"], "products_category", "product_category_id", map[string]interface{}{"data_type": "product"})
	} else {
		go setValueToChan(categoryIdChan, utils.ToInt64(formValue["product_category_fkid"]))
	}

	if utils.ToInt(formValue["product_subcategory_fkid"]) == 0 {
		//if sub category not sent, use default
		if formValue["product_subcategory_name"] == "" {
			formValue["product_subcategory_name"] = defaultCategoryName
		}
		go addNewItemWithCheck(subCategoryIdChan, string(adminId), formValue["product_subcategory_name"], "products_subcategory", "product_subcategory_id", map[string]interface{}{"data_type": "product"})
	} else {
		go setValueToChan(subCategoryIdChan, utils.ToInt64(formValue["product_subcategory_fkid"]))
	}

	if utils.ToInt(formValue["purchase_report_category_fkid"]) == 0 {
		if formValue["purchase_report_category_name"] == "" {
			formValue["purchase_report_category_name"] = defaultCategoryName //if user not define the data, take value from category
		}
		go addNewItemWithCheck(purchaseRepCatIdChan, string(adminId), formValue["purchase_report_category_name"], "purchase_report_category", "purchase_report_category_id", map[string]interface{}{
			"data_created":  time.Now(),
			"data_modified": time.Now(),
		})
	} else {
		go setValueToChan(purchaseRepCatIdChan, utils.ToInt64(formValue["purchase_report_category_fkid"]))
	}

	if utils.ToInt(formValue["unit_fkid"]) == 0 {
		go addNewItemWithCheck(unitIdIdChan, string(adminId), formValue["unit_name"], "unit", "unit_id", map[string]interface{}{
			"data_created":  time.Now(),
			"data_modified": time.Now(),
			"description":   "",
		})
	} else {
		go setValueToChan(unitIdIdChan, utils.ToInt64(formValue["unit_fkid"]))
	}

	productTypeId := utils.ToString(<-productTypeIdChan)
	categoryId := utils.ToString(<-categoryIdChan)
	subcategoryId := utils.ToString(<-subCategoryIdChan)
	purchaseRepCat := utils.ToString(<-purchaseRepCatIdChan)
	unitId := utils.ToString(<-unitIdIdChan)

	if cast.ToInt64(formValue["data_created"]) == 0 {
		formValue["data_created"] = cast.ToString(utils.CurrentMillis())
	}
	if formValue["stock_management"] == "" {
		formValue["stock_management"] = "0"
	}

	data := map[string]interface{}{
		"name":                          formValue["name"],
		"catalogue_type":                "product",
		"product_type_fkid":             productTypeId,
		"product_category_fkid":         categoryId,
		"product_subcategory_fkid":      subcategoryId,
		"purchase_report_category_fkid": purchaseRepCat,
		"unit_fkid":                     unitId,
		"stock_management":              formValue["stock_management"],
		"photo":                         imgFileName,
		"admin_fkid":                    adminId,
		"data_created":                  formValue["data_created"],
		"data_modified":                 time.Now().Unix() * 1000,
		"data_status":                   "on",
	}

	if formValue["barcode"] != "" {
		data["barcode"] = formValue["barcode"]
	}
	if formValue["sku"] != "" {
		data["sku"] = formValue["sku"]
	}
	if formValue["price_buy"] == "" {
		formValue["price_buy"] = "0"
	}
	if formValue["active"] == "" {
		formValue["active"] = "on_sales"
	}

	res, err := db.Insert("products", data)
	if log.IfErrorSetStatus(ctx, err) {
		log.Debug("Insert into product with name : ", formValue["name"], " FAILED. - ", err)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Millis: millis, Message: err.Error()})
		return
	}

	productId, _ := res.LastInsertId()

	data = map[string]interface{}{
		"product_fkid":         productId,
		"outlet_fkid":          formValue["outlet_fkid"],
		"price_buy_start":      formValue["price_buy"],
		"price_buy":            formValue["price_buy"],
		"price_sell":           formValue["price_sell"],
		"voucher":              "on",
		"discount":             "on",
		"active":               formValue["active"],
		"transfer_markup_type": "nominal",
		"transfer_markup":      0,
		"data_modified":        time.Now().Unix() * 1000,
		"stock":                "available",
		"data_status":          "on",
	}

	res, err = db.Insert("products_detail", data)
	if log.IfErrorSetStatus(ctx, err) {
		fmt.Println("Insert into product detail with name : ", formValue["name"], " FAILED. - ", err)
		_, err = db.GetDb().Exec("DELETE FROM products WHERE product_id=?", productId)
		log.IfError(err)
		return
	}

	productDetailId, _ := res.LastInsertId()

	products, err := db.Query("SELECT * FROM products_detail pd JOIN products p "+
		" ON pd.product_fkid=p.product_id WHERE pd.product_detail_id = ?", productDetailId)
	if log.IfErrorSetStatus(ctx, err) {
		fmt.Println("Get product record (after insert) with name : ", formValue["name"], " FAILED. - ", err)
		return
	}

	//if len(product.Photo) > 10 {
	//	go base64ToImage(product.Photo, "assets/product/" + string(adminId) + "/test.jpg")
	//}

	//Upload Image
	//filePath := os.Getenv("base_path") + "/assets/images/products/" + string(adminId) + "/" + imgFileName
	if imgFileName != "" {
		header, err := ctx.FormFile("image")
		log.IfError(err)

		tmpFileName := fmt.Sprintf("%s/%s", utils.GetTmpDir("product"), imgFileName)
		err = fasthttp.SaveMultipartFile(header, tmpFileName)
		if !log.IfError(err) {
			go func(originFile string, productId int64) {
				uploadStart := time.Now()
				file, err := os.Open(originFile)
				if !log.IfError(err) {
					defer file.Close()

					filePath := os.Getenv("server") + "/products/" + string(adminId) + "/" + imgFileName
					imgUrl, err := google.UploadFile(file, filePath, true)
					if err == nil {
						log.Debug("product image of %d is %s", productId, imgUrl)
						_, err = db.Update("products", map[string]interface{}{
							"photo": imgUrl,
						}, "product_id = ?", productId)
						log.IfError(err)
					}
				}
				if err = os.Remove(originFile); err != nil {
					fmt.Println("Removing tmp file : ", originFile, " - error : ", err)
				}
				log.Info("upload image took : %v", time.Since(uploadStart))
			}(tmpFileName, productId)
		}

		//filePath := os.Getenv("server") + "/products/" + string(adminId) + "/" + imgFileName
		//uploadImage(ctx, filePath, productId)
	} else {
		log.Debug("user not upload any image. product '%d' will have no image", productId)
	}

	log.Info("add product took : %v", time.Since(timeStart))
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: millis, Data: products})
}

func setValueToChan(paramChan chan int64, paramValue int64) {
	paramChan <- paramValue
}

func addNewItemWithCheck(idChan chan int64, adminId, name, tableName, colId string, dataAdd map[string]interface{}) {
	result := int64(0)
	defer func() {
		log.Info("%s : %d", tableName, result)
		idChan <- result
	}()

	log.Info("getting id from %s | %s", tableName, name)

	oldData, err := db.Query("SELECT "+colId+" FROM "+tableName+" WHERE name=? AND admin_fkid=?", name, adminId)
	if log.IfError(err) {
		return
	}

	if len(oldData) > 0 {
		result = oldData[colId].(int64)
		return
	}

	data := map[string]interface{}{
		"name":          name,
		"admin_fkid":    adminId,
		"data_created":  time.Now().Unix() * 1000,
		"data_modified": time.Now().Unix() * 1000,
		"data_status":   "on",
	}

	for key, value := range dataAdd {
		data[key] = value
	}

	res, err := db.Insert(tableName, data)
	if log.IfError(err) {
		return
	}

	resp, err := res.LastInsertId()
	if log.IfError(err) {
		return
	}

	result = resp
}

func GetFormValueMap(request *fasthttp.RequestCtx) map[string]string {
	values := make(map[string]string)
	mediaType, params, err := mime.ParseMediaType(string(request.Request.Header.Peek("Content-Type")))
	if err != nil || !strings.HasPrefix(mediaType, "multipart/") {
		//for i := range keys {
		//	values[keys[i]] = string(request.FormValue(keys[i]))
		//}
		fmt.Println("Request is not Multipart")
	} else { // multi form
		buf, _ := ioutil.ReadAll(bytes.NewReader(request.PostBody()))
		//origBody := ioutil.NopCloser(bytes.NewBuffer(buf))
		var rdr = multipart.NewReader(bytes.NewBuffer(buf), params["boundary"])
		for {
			part, err_part := rdr.NextPart()
			if err_part == io.EOF {
				break
			}

			buf := new(bytes.Buffer)
			buf.ReadFrom(part)
			values[part.FormName()] = strings.TrimSpace(buf.String())
		}

		//request.body = origBody
	}
	return values
}

func GetFormValue(request *fasthttp.RequestCtx, keys []string) map[string]string {
	values := make(map[string]string)
	mediaType, params, err := mime.ParseMediaType(string(request.Request.Header.Peek("Content-Type")))
	if err != nil || !strings.HasPrefix(mediaType, "multipart/") {
		for i := range keys {
			values[keys[i]] = string(request.FormValue(keys[i]))
		}
	} else { // multi form
		buf, _ := ioutil.ReadAll(bytes.NewReader(request.PostBody()))
		//origBody := ioutil.NopCloser(bytes.NewBuffer(buf))
		var rdr = multipart.NewReader(bytes.NewBuffer(buf), params["boundary"])
		for len(values) < len(keys) {
			part, err_part := rdr.NextPart()
			if err_part == io.EOF {
				break
			}
			for i := range keys {
				if part.FormName() == keys[i] {
					buf := new(bytes.Buffer)
					buf.ReadFrom(part)
					values[keys[i]] = buf.String()
				}
			}
		}

		//request.body = origBody
	}
	if len(values) == len(keys) {
		fmt.Println("GetFormValue | Value length not same...")
		return values
	} else {
		return nil
	}
}

func GetFormValues(request *fasthttp.RequestCtx, keys []string) []string {
	var values []string
	mediaType, params, err := mime.ParseMediaType(string(request.Request.Header.Peek("Content-Type")))
	if err != nil || !strings.HasPrefix(mediaType, "multipart/") {
		for i := range keys {
			values = append(values, string(request.FormValue(keys[i])))
		}
	} else { // multi form
		buf, _ := ioutil.ReadAll(bytes.NewReader(request.PostBody()))
		//origBody := ioutil.NopCloser(bytes.NewBuffer(buf))
		var rdr = multipart.NewReader(bytes.NewBuffer(buf), params["boundary"])
		for len(values) < len(keys) {
			part, err_part := rdr.NextPart()
			if err_part == io.EOF {
				break
			}
			for i := range keys {
				if part.FormName() == keys[i] {
					buf := new(bytes.Buffer)
					buf.ReadFrom(part)
					values = append(values, buf.String())
				}
			}
		}

		//request.body = origBody
	}
	if len(values) == len(keys) {
		return values
	} else {
		return nil
	}
}

func uploadImage(ctx *fasthttp.RequestCtx, destPath string, productId int64) {
	fileHeader, err := ctx.FormFile("image")
	if log.IfError(err) {
		return
	}

	file, err := fileHeader.Open()
	if log.IfError(err) {
		return
	}

	defer file.Close()
	fmt.Printf("%v", fileHeader.Header)

	imgUrl, err := google.UploadFile(file, destPath, true)
	if err == nil {
		log.Debug("product image of %d is %s", productId, imgUrl)
		_, err = db.Update("products", map[string]interface{}{
			"photo": imgUrl,
		}, "product_id = ?", productId)
		log.IfError(err)
	}

	//f, err := os.OpenFile(destPath, os.O_WRONLY|os.O_CREATE, 0777)
	//if err != nil {
	//	fmt.Println("openfile Error : ", err)
	//	return
	//}
	//
	//fmt.Println("File size : ", fileHeader.Size)
	//
	//defer f.Close()
	//_, err = io.Copy(f, file)
	//utils.CheckErr(err)
}

func base64ToImage(base64, destPath string) {
	jpg, err := jpeg.Decode(bytes.NewReader([]byte(base64)))
	if utils.CheckErr(err) {
		return
	}

	f, err := os.OpenFile(destPath, os.O_WRONLY|os.O_CREATE, 0777)
	if log.IfError(err) {
		return
	}

	defer f.Close()

	err = jpeg.Encode(f, jpg, &jpeg.Options{Quality: 75})
	if utils.CheckErr(err) {

	}
}

func GetCategory(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	adminId := ctx.Request.Header.Peek("admin_id")
	lastSync := ctx.UserValue("lastSync")

	data, err := db.QueryArray("SELECT * FROM products_category WHERE admin_fkid=? AND data_modified >= ?",
		adminId, lastSync)
	utils.CheckErr(err)

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func GetSubCategory(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	adminId := ctx.Request.Header.Peek("admin_id")
	lastSync := ctx.UserValue("lastSync")

	data, err := db.QueryArray("SELECT * FROM products_subcategory WHERE admin_fkid=? AND data_modified >= ?",
		adminId, lastSync)
	utils.CheckErr(err)

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func GetTax(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	data, err := db.QueryArray("SELECT pdt.* FROM products_detail_taxdetail pdt JOIN products_detail pd "+
		"ON pdt.product_detail_fkid=pd.product_detail_id WHERE outlet_fkid=? AND pdt.data_modified >= ?", outletId, lastSync)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func GetGratuity(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	adminId := ctx.Request.Header.Peek("admin_id")
	lastSync := ctx.UserValue("lastSync")

	data, err := db.QueryArray("SELECT * FROM gratuity WHERE admin_fkid=? AND data_modified >= ?", adminId, lastSync)
	utils.CheckErr(err)

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func GetProductDescription(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	lastSyncDate, err := date(lastSync.(string))
	if utils.CheckErr(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	data, err := db.QueryArray("SELECT * FROM products_description WHERE outlet_fkid=? AND data_created >= ?", outletId, lastSyncDate)
	utils.CheckErr(err)

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func GetMultiplePriceProduct(ctx *fasthttp.RequestCtx) {
	response := models.ResponseArray{Millis: time.Now().Unix() * 1000, Status: true}
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	data, err := db.QueryArray("SELECT * FROM products_detail_multipleprice WHERE product_detail_fkid IN (SELECT product_detail_id FROM products_detail WHERE outlet_fkid=?) AND data_modified >= ?", outletId, lastSync)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetLinkMenu(ctx *fasthttp.RequestCtx) {
	response := models.ResponseArray{Millis: time.Now().Unix() * 1000, Status: true}
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	data, err := db.QueryArray("SELECT plm.* FROM products_linkmenu plm JOIN products_linkmenu_detail pld "+
		"ON plm.linkmenu_id = pld.linkmenu_fkid WHERE plm.outlet_fkid=? AND (plm.data_modified >= ? OR pld.data_modified >= ?)", outletId, lastSync, lastSync)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	linkMenuIds := make([]interface{}, 0)
	for _, linkMenu := range data {
		linkMenuIds = append(linkMenuIds, linkMenu["linkmenu_id"])
	}

	if len(linkMenuIds) > 0 {
		linkMenuDetails, err := db.QueryArray("SELECT * FROM products_linkmenu_detail WHERE linkmenu_fkid IN ("+strings.Repeat("?,", len(linkMenuIds)-1)+"?)", linkMenuIds...)
		utils.CheckErr(err)

		for _, linkMenu := range data {
			detail := make([]map[string]interface{}, 0)
			for _, linkMenuDetail := range linkMenuDetails {
				if linkMenu["linkmenu_id"] == linkMenuDetail["linkmenu_fkid"] {
					detail = append(detail, linkMenuDetail)
				}
			}
			linkMenu["detail"] = detail
		}
	}

	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetUnit(ctx *fasthttp.RequestCtx) {
	response := models.ResponseArray{Millis: time.Now().Unix() * 1000, Status: true}
	//lastSync := ctx.UserValue("lastSync")
	adminId := ctx.Request.Header.Peek("admin_id")

	data, err := db.QueryArray("SELECT * FROM unit WHERE admin_fkid=?", adminId)
	utils.CheckErr(err)

	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetProductType(ctx *fasthttp.RequestCtx) {
	response := models.ResponseArray{Status: true, Millis: time.Now().Unix() * 1000}
	adminId := ctx.Request.Header.Peek("admin_id")
	lastSync := ctx.UserValue("lastSync")

	data, err := db.QueryArray("SELECT * FROM products_type WHERE admin_fkid=? AND data_modified>=?", adminId, lastSync)
	utils.CheckErr(err)

	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetProductVariant(ctx *fasthttp.RequestCtx) {
	response := models.ResponseArray{Status: true, Millis: time.Now().Unix() * 1000}
	adminId := ctx.Request.Header.Peek("admin_id")
	lastSync := ctx.UserValue("lastSync")

	data, err := db.QueryArray("SELECT * FROM products_detail_variant WHERE admin_fkid=? AND data_modified>=?", adminId, lastSync)
	utils.CheckErr(err)

	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}

func TestUpload(ctx *fasthttp.RequestCtx) {
	fmt.Println("Uploading file...")
	fileHeader, err := ctx.FormFile("file")
	if err != nil {
		fmt.Println("Error : ", err)
		return
	}

	file, err := fileHeader.Open()
	if err != nil {
		fmt.Println("fileHeader Error : ", err)
	}

	defer file.Close()
	fmt.Fprintf(ctx, "%v", fileHeader.Header)

	//utils.UploadFileHeaderToDrive(*fileHeader)
	f, err := os.OpenFile("temp/logs/"+fileHeader.Filename, os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		fmt.Println("openfile Error : ", err)
		return
	}

	fmt.Println("File size : ", fileHeader.Size)

	defer f.Close()
	io.Copy(f, file)
}

func date(lastSync string) (string, error) {
	timeMillis, err := strconv.ParseInt(lastSync, 10, 64)
	if err != nil {
		return "", err
	}
	t := time.Unix(timeMillis, 0)
	return t.Format("2006-01-02 15:04:05"), nil
}

func millisToDate(timeMillis int64) (string, error) {
	t := time.Unix(timeMillis, 0)
	return t.Format("2006-01-02 15:04:05"), nil
}
