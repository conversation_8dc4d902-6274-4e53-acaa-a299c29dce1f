package v1

import (
	"fmt"
	"net/url"
	"strings"
	"testing"

	"gitlab.com/uniqdev/backend/api-pos/core/log"
)

func Test_decryptBarcode(t *testing.T) {
	barcode := "https://quickchart.io/qr?text=9yEmhRrvnD7GPtCev-Wa"
	if strings.HasPrefix(strings.TrimSpace(strings.ToLower(barcode)), "https") {
		fmt.Println("its a https")
		u, err := url.Parse(barcode)
		log.IfError(err)
		if err == nil {
			text := u.Query().Get("text")
			log.Info("get barcode from url, value: %v, origin: %s", text, barcode)
			if text != "" {
				barcode = text
			}
		}
	} else {
		fmt.Println("is not https....")
	}
	fmt.Println("barcode: ", barcode)
}

func Test_validateMaxRedeemPeriod(t *testing.T) {
	type args struct {
		promo map[string]interface{}
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			"already-used",
			args{map[string]interface{}{
				"promotion_fkid":             1043,
				"member_fkid":                193,
				"maximum_redeem_period_days": 30,
			}},
			false,
		},
		{
			"alreadyUsedLongTimeAgo",
			args{map[string]interface{}{
				"promotion_fkid":             1043,
				"member_fkid":                193,
				"maximum_redeem_period_days": 10,
			}},
			true,
		},
		{
			"maxRedeemNotSet",
			args{map[string]interface{}{
				"promotion_fkid": 1043,
				"member_fkid":    193,
			}},
			true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := validateMaxRedeemPeriod(tt.args.promo); got != tt.want {
				t.Errorf("validateMaxRedeemPeriod() = %v, want %v", got, tt.want)
			}
		})
	}
}
