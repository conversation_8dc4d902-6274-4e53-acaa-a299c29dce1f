package v1

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"html/template"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/format"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	aes256 "gitlab.com/uniqdev/backend/api-pos/core/security"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/core/utils/file"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

var muBilling sync.Mutex

func ReceiveVACreated(ctx *fasthttp.RequestCtx) {
	log.Info("payment created: %s", string(ctx.Request.Body()))
}

func ReceiveVAPaid(ctx *fasthttp.RequestCtx) {
	log.Info("payment paid: %s", string(ctx.Request.Body()))
	//{
	//	"amount": ********,
	//	"callback_virtual_account_id": "608a40b0064c9228ac566f58",
	//	"payment_id": "*************",
	//	"external_id": "002/POS/V/2021",
	//	"account_number": "**********",
	//	"merchant_code": "10766",
	//	"bank_code": "BCA",
	//	"transaction_timestamp": "2021-04-29T05:35:17.231Z",
	//	"currency": "IDR",
	//	"created": "2021-04-29T05:35:17.349Z",
	//	"updated": "2021-04-29T05:35:17.729Z",
	//	"id": "608a45956fb5d43258c468ae",
	//	"owner_id": "5fe30568fff3745477572973"
	//}

	var payment map[string]interface{}
	err := json.Unmarshal(ctx.Request.Body(), &payment)
	log.IfError(err)

	// Publish payment webhook to PubSub for staging environment
	// This is needed because:
	// 1. Production and staging share the same payment gateway
	// 2. Payment gateway only sends webhooks to production
	// 3. Publishing to PubSub allows staging to also receive payment notifications
	err = google.PublishMessage(map[string]interface{}{
		//"payment_gateway": paymentGateway,
		"payload": payment,
	}, fmt.Sprintf("billing_webhook_%s", os.Getenv("server")))
	log.IfError(err)

	err = HandlePaymentWebhook(payment)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
	} else {
		ctx.SetStatusCode(fasthttp.StatusOK)
	}
}

func HandlePaymentWebhook(payment map[string]interface{}) error {
	muBilling.Lock()
	defer muBilling.Unlock()

	//check billing
	sql := `select *
from system_billing sb
         join system_billing_detail sbd on sb.billing_id = sbd.billing_fkid
		join system_service ss on sbd.sys_service_fkid = ss.sys_service_id
where invoice = ? and billing_status != 'success' order by sbd.service_type `
	billing, err := db.QueryArray(sql, payment["external_id"])
	log.IfError(err)

	if len(billing) == 0 {
		//_, err = db.Insert("system_billing_failed", map[string]interface{}{
		//	"json_received": string(ctx.Request.Body()),
		//	"info":          "billing_id not found",
		//	"data_created":  time.Now().Unix() * 1000,
		//})
		//log.IfError(err)
		log.Error("Payment Callback Error, no billing found or its already paid, id: %s", payment["external_id"])
		log.Info("[BILLING FAILED] %v", payment)
		return nil
	}

	paymentId := utils.ToString(payment["id"])
	paidAmount := utils.ToInt(payment["paid_amount"])
	if utils.ToString(payment["callback_virtual_account_id"]) != "" {
		paymentId = utils.ToString(payment["callback_virtual_account_id"])
		paidAmount = utils.ToInt(payment["amount"])
	}

	//compare id (generated by payment gateway)
	if billing[0]["payment_result"] != nil {
		var paymentResult map[string]interface{}
		err = json.Unmarshal([]byte(utils.ToString(billing[0]["payment_result"])), &paymentResult)
		if !log.IfError(err) && utils.ToString(paymentResult["id"]) != paymentId {
			log.IfError(fmt.Errorf("id didn't match of %s -> %v VS %v", payment["external_id"], paymentResult["id"], payment["id"]))
			return nil
		}
	} else {
		log.IfError(fmt.Errorf("payment_result is empty for id: %s", payment["external_id"]))
	}

	//compare paid nominal, should be mach with what on system_billing (col: detail_total)
	if utils.ToInt(billing[0]["detail_total"]) != paidAmount {
		log.IfError(fmt.Errorf("total paid not match of id %s, paid: %v should be: %v", payment["external_id"], payment["paid_amount"], billing[0]["detail_total"]))
		return nil
	}

	log.Info("billing of '%v' is valid...", payment["external_id"])

	userSubscriptions, err := db.QueryArray("select * from system_subscribe where admin_fkid = ? and service_time_expired > ? order by service_time_expired ",
		billing[0]["admin_fkid"],
		billing[0]["time_order"])
	log.IfError(err)

	log.Info("subscriptions of user %v: %v", billing[0]["admin_fkid"], cast.ToJson(userSubscriptions))

	err = db.WithTransaction(func(transaction db.Transaction) error {
		//update billing
		_, err = transaction.Update("system_billing", map[string]interface{}{
			"time_confirm":   time.Now().Unix() * 1000,
			"billing_status": "success",
		}, "billing_id = ?", billing[0]["billing_id"])

		//update subscription
		for _, service := range billing {
			log.Info("service: %v", service)
			//if service is crm or finance, then set service_type to extend
			if utils.ToString(service["service_feature"]) == "crm" || utils.ToString(service["service_feature"]) == "finance" {
				service["service_type"] = "extend"
			}
			periodDays := utils.ToInt(service["service_period"]) * utils.ToInt(service["service_length_day"])
			if utils.ToString(service["service_type"]) == "new" {
				for i := 0; i < utils.ToInt(service["qty"]); i++ {
					resp, err := transaction.Insert("system_subscribe", map[string]interface{}{
						"billing_fkid":         service["billing_id"],
						"billing_detail_fkid":  service["billing_detail_id"],
						"feature":              service["service_feature"],
						"sys_service_fkid":     service["sys_service_fkid"],
						"service_period":       periodDays,
						"admin_fkid":           service["admin_fkid"],
						"service_time_start":   time.Now().Unix() * 1000,
						"service_time_expired": time.Now().AddDate(0, 0, periodDays).Unix() * 1000,
					})
					if !log.IfError(err) {
						id, _ := resp.LastInsertId()
						log.Info("insert new system_subscribe: %v (%v)", id, service["service_feature"])
					}
				}
			} else if utils.ToString(service["service_type"]) == "extend" {
				extendedCount := 0
				for _, subscription := range userSubscriptions {
					if utils.ToString(service["service_feature"]) == utils.ToString(subscription["feature"]) {
						extendedCount += 1
						_, err = transaction.Update("system_subscribe", map[string]interface{}{
							"billing_fkid":         service["billing_id"],
							"billing_detail_fkid":  service["billing_detail_id"],
							"feature":              service["service_feature"],
							"service_period":       periodDays,
							"service_time_expired": time.Unix(utils.ToInt64(subscription["service_time_expired"])/1000, 0).AddDate(0, 0, periodDays).Unix() * 1000,
						}, "id = ?", subscription["id"])
						if !log.IfError(err) {
							log.Info("extend system_subscribe: %v (%v)", subscription["id"], service["service_feature"])
						}
						if extendedCount == utils.ToInt(service["qty"]) {
							log.Info("extended %v slot for %v", extendedCount, service["service_feature"])
							break
						}
					}
				}

				//if total slot extended is bigger than available slot in db, then insert new
				for i := extendedCount; i < utils.ToInt(service["qty"]); i++ {
					resp, err := transaction.Insert("system_subscribe", map[string]interface{}{
						"billing_fkid":         service["billing_id"],
						"billing_detail_fkid":  service["billing_detail_id"],
						"feature":              service["service_feature"],
						"sys_service_fkid":     service["sys_service_fkid"],
						"service_period":       periodDays,
						"admin_fkid":           service["admin_fkid"],
						"service_time_start":   time.Now().Unix() * 1000,
						"service_time_expired": time.Now().AddDate(0, 0, periodDays).Unix() * 1000,
					})
					if !log.IfError(err) {
						id, _ := resp.LastInsertId()
						log.Info("(slot full) insert new system_subscribe: %v (%v)", id, service["service_feature"])
					}
				}
			}
		}

		jsonResult, _ := json.Marshal(payment)
		//record history
		_, err = transaction.Insert("system_billing_payment_history", map[string]interface{}{
			"json_result":  string(jsonResult),
			"billing_fkid": billing[0]["billing_id"],
			"data_created": time.Now().Unix() * 1000,
		})
		return nil
	})

	if log.IfError(err) {
		log.Info("[BILLING FAILED] %v", payment)
	}

	if err == nil {
		//notify to user
		admin, err := db.Query("select * from admin where admin_id = ? ", billing[0]["admin_fkid"])
		log.IfError(err)

		message := `
*[INVOICE PAYMENT CONFIRMATION]*

Dear *%s*,
Pembayaran untuk invoice *%s*
untuk bisnis %s
telah *BERHASIL*

Terima Kasih telah memilih UNIQ untuk kemudahan bisnis Anda.
www.uniq.id
`
		if strings.ToLower(utils.ToString(admin["register_ref_code"])) == "wolvem2022" {
			admin["phone"] = "*************"
		}

		invoiceUrl := ""
		billingInfo, err := getDetailBilling(cast.ToInt64(billing[0]["billing_id"]))
		if !log.IfError(err) {
			docFilePath := generateInvoiceDocument(*billingInfo)
			invoiceUrl = uploadInvoiceDoc(docFilePath, billingInfo.AdminFkid)
		}

		message = fmt.Sprintf(message, admin["name"], billing[0]["invoice"], admin["business_name"])
		scheduleMsg := map[string]interface{}{
			"title":        "INVOICE PAYMENT CONFIRMATION",
			"message":      message,
			"time_deliver": time.Now().Unix() * 1000,
			"data_created": time.Now().Unix() * 1000,
			"media":        "whatsapp",
			"receiver":     admin["phone"],
		}

		if invoiceUrl != "" {
			scheduleMsg["attachments"] = cast.ToJson([]string{invoiceUrl})
		}

		err = google.PublishMessage(scheduleMsg, "messaging-gateway-production")
		if err != nil {
			_, err = db.Insert("scheduled_message", scheduleMsg)
		}
		log.IfError(err)

		//to to other recipient
		sql = `SELECT rrt.report_type_fkid, 
		COALESCE(e.phone, if(rra.media='whatsapp', rra.address, '')) as phone,
		COALESCE(e.email, if(rra.media='email', rra.address, '')) as email, 
		rra.media, rra.address
		from report_recipient_type rrt 
		join report_type rt on rt.report_type_id=rrt.report_type_fkid
		left join report_recipient rr on rr.report_recipient_id=rrt.report_recipient_fkid
		left join report_recipient_address rra on rra.report_recipient_fkid=rr.report_recipient_id
		left join employee e on e.employee_id=rr.employee_fkid
		where rr.admin_fkid= ? AND rrt.report_type_fkid = 'invoice' `

		recipients, err := db.QueryArray(sql, admin["admin_id"])
		log.IfError(err)

		for _, recipeint := range recipients {
			phone := utils.ToString(recipeint["phone"])
			if phone == "" {
				continue
			}

			phone = "62" + strings.TrimLeft(phone, "0")
			scheduleMsg["receiver"] = phone
			err = google.PublishMessage(scheduleMsg, "messaging-gateway-production")
			if err != nil {
				_, err = db.Insert("scheduled_message", scheduleMsg)
			}
			log.IfError(err)
		}

		//ctx.SetStatusCode(fasthttp.StatusOK)
		return nil
	} else {
		return err
	}
}

func getDetailBilling(billingId int64) (*models.BillingWithUser, error) {
	repo := db.Repository{Conn: db.GetDb()}

	sql := `SELECT * from system_billing where billing_id=?`
	var billing models.Billing
	err := repo.Set(sql, billingId).Get(&billing)
	if log.IfError(err) {
		return nil, err
	}

	if billing.TimeConfirm > 0 {
		billing.TimeConfirm = billing.TimeConfirm + (25200 * 1000)
	}

	sql = `SELECT * from system_billing_detail sbd 
	join system_service ss on ss.sys_service_id=sbd.sys_service_fkid
	 where billing_fkid=?`
	var billingDetail []models.BillingDetail
	err = repo.Set(sql, billingId).Get(&billingDetail)
	if log.IfError(err) {
		return nil, err
	}

	billing.Detail = billingDetail

	sql = `SELECT admin_id,name, business_name, email, phone from admin where admin_id=?`
	var admin models.BillingAdmin
	err = repo.Set(sql, billing.AdminFkid).Get(&admin)
	if log.IfError(err) {
		return nil, err
	}

	return &models.BillingWithUser{
		Billing: billing,
		Admin:   admin,
	}, nil
}

func uploadInvoiceDoc(invoiceDocPath string, adminId int) string {
	if invoiceDocPath == "" {
		return ""
	}
	//convert to pdf
	invoicePdfPath, err := file.HtmlToPdf(invoiceDocPath)
	log.IfError(err)

	//upload to storage
	file, err := os.Open(invoicePdfPath)
	if !log.IfError(err) {
		invoiceDocUrl, err := google.UploadFile(file, fmt.Sprintf("%v/billing/%v/%v", os.Getenv("ENV"), adminId, filepath.Base(invoicePdfPath)), true)
		log.IfError(err)
		log.Info("invoice doc url: %v", invoiceDocUrl)
		return invoiceDocUrl
	}
	return ""
}

func generateInvoiceDocument(billing models.BillingWithUser) string {
	templateFilePath := os.Getenv("INVOICE_TEMPLATE_PATH")
	if templateFilePath == "" {
		templateFilePath = "config/template/invoice.tmpl"
	}

	fmt.Println("generate doc... template: ", templateFilePath)
	// fmt.Println("data: ", cast.ToJson(billing))

	if _, err := os.Stat(templateFilePath); errors.Is(err, os.ErrNotExist) {
		log.Info("template is not exist..")
		return ""
	}

	funcMap := template.FuncMap{
		"multiply": multiply,
		"add": func(a, b int) int {
			return a + b
		},
		"unixDate": func(a int64) string {
			return time.Unix(a*1000, 0).Format("02-01-2006") //2006-01-02 15:04:05
		},
		"dateNow": func() string {
			return time.Now().Format("02-01-2006") //2006-01-02 15:04:05
		},
		"uppercase": strings.ToUpper,
		"toLower":   strings.ToLower,
		"periodConversion": func(period int) string {
			if period <= 30 {
				return "bulan"
			} else if period <= 365 {
				return "tahun"
			}
			return "hari"
		},
		"currency": func(nominal int) string {
			return "IDR " + format.Currency(nominal)
		},
	}

	// invTmpl := template.Must(template.New("invoice.tmpl").Funcs(funcMap).ParseFiles(templateFilePath))
	invTmpl, err := template.New("invoice.tmpl").Funcs(funcMap).ParseFiles(templateFilePath)
	// invTmpl, err := template.ParseFiles(templateFilePath)
	log.IfError(err)

	fileName := fmt.Sprintf("invoice_%s_%s.html", billing.BillingStatus, billing.Invoice)
	fileName = strings.ReplaceAll(fileName, "/", "-")
	invoiceFile, err := os.Create(fileName)
	log.IfError(err)
	invoiceFile.Name()
	err = invTmpl.Execute(invoiceFile, billing)
	// var result bytes.Buffer
	// err = invTmpl.Execute(&result, billing)
	// err = invTmpl.ExecuteTemplate(invoiceFile, "invoice", billing)

	// fmt.Println(">>> ", result.String())

	log.IfError(err)
	log.Info("invoice generated! path: %v", fileName)
	return fileName
}

func multiply(a, b int) int {
	return a * b
}

func GetServices(ctx *fasthttp.RequestCtx) {
	sql := "select * from system_service where price > 0"
	data, err := db.QueryArray(sql)
	log.IfError(err)
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Data: data})
}

func GetInvoiceWeb(ctx *fasthttp.RequestCtx) {
	id := ctx.UserValue("id")
	invoice := string(ctx.QueryArgs().Peek("invoice"))
	var reqBody map[string]interface{}
	err := json.Unmarshal(ctx.PostBody(), &reqBody)
	log.IfError(err)

	fmt.Println("id --> ", id, "invoice --> ", invoice)
	fmt.Println("body --> ", string(ctx.PostBody()))
	fmt.Println("auth --> ", string(ctx.Request.Header.Peek("Authorization")))

	req := utils.HttpRequest{
		Method: "POST",
		Url:    fmt.Sprintf("%s/api/v1/billing/%v/invoice?invoice=%s", os.Getenv("API_BILLING"), id, invoice),
		Header: map[string]interface{}{
			"Authorization": string(ctx.Request.Header.Peek("Authorization")),
		},
		PostRequest: utils.PostRequest{
			Body: reqBody,
		},
	}
	resp, err := req.Execute()
	log.IfError(err)

	log.Info("get invoice result of  %s -> %s", invoice, string(resp))

	var respBody map[string]interface{}
	err = json.Unmarshal(resp, &respBody)
	log.IfError(err)

	fmt.Println(string(resp))
	_ = json.NewEncoder(ctx).Encode(respBody)
}

func SubscriptionWebPos(ctx *fasthttp.RequestCtx) {
	//request
	//{"billing":{"detail_total":149000,"admin_fkid":"1","discount_percent":false,"kode_unik":0,"generated_by":"user","billing_notes":null,"invoice_type":"POS"},"detail":[{"billing_fkid":null,"sys_service_fkid":"1","qty":1,"service_length_day":"30","service_period":"1","service_feature":"device","service_type":"new","price":"149000","discount":0}]}

	//response
	//{"status":"SUCCESS","msg":"SUCCESS TO ADD BILLING","billing_id":1175,"data":{"admin_id":1,"business_name":"UNIQ The String Business","name":"UNIQ DEV","email":"<EMAIL>","data_created":1515662698343,"data_modified":1612853397512,"phone":"085742257881","business_type":"restaurant","billing":{"admin_fkid":"1","invoice":"168/POS/V/2021","unique_code":168,"time_order":1620113793603,"time_order_expired":1620124593603,"time_confirm":0,"paid_status":0,"billing_status":"billing","detail_total":149000,"sysuser_fkid":null,"status_info":"","data_created":1620113793603,"data_updated":0,"is_percent":false,"generated_by":"user","billing_id":1175,"detail":[{"billing_fkid":1175,"sys_service_fkid":"1","qty":1,"service_length_day":"30","service_period":"1","service_type":"new","price":"149000","discount":0,"billing_detail_id":754,"service":{"sys_service_id":1,"name":"Device Subscription Monthly","price":149000,"discount":0,"service_feature":"device","service_length_day":30,"service_period_max":12,"service_type":"new","service_sortorder":1,"data_created":1517238895987,"data_modified":1517477074664,"data_status":1}}]}}}

	fmt.Println("subscription web, auth: ", string(ctx.Request.Header.Peek("Authorization")))
	fmt.Println("subscription web, body: ", string(ctx.PostBody()))
	ctx.SetStatusCode(200)

	//validate auth key
	var billingAuth models.BillingAuth
	authKey := aes256.Decrypt(string(ctx.Request.Header.Peek("Authorization")), utils.KEY_BILLING_AUTH)
	if len(authKey) > 0 {
		err := json.Unmarshal([]byte(authKey), &billingAuth)
		log.Info("auth key decrypted: %s", authKey)
		log.IfError(err)
	}

	var request models.RequestSubscription
	err := json.Unmarshal(ctx.PostBody(), &request)
	if log.IfError(err) {
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
			"status": "ERROR",
			"msg":    "invalid request sent to server",
		})
		return
	}

	services := make([]models.SubscriptionService, 0)
	for _, billing := range request.Detail {
		services = append(services, models.SubscriptionService{
			ServiceID: utils.ToInt(billing.SysServiceFkid),
			Period:    utils.ToInt(billing.ServicePeriod),
			Qty:       billing.Qty,
		})
	}

	subscription := models.SubscriptionRequest{
		PaymentMethod: "BCA",
		Services:      services,
	}

	if utils.ToInt(request.Billing.Discount) > 0 && billingAuth.EnableDiscount {
		subscription.Discount = utils.ToInt(request.Billing.Discount)
		subscription.DiscountNote = request.Billing.DiscountNote
	}

	data, err := createMultipleService(subscription, request.Billing.AdminFkid)

	response := make(map[string]interface{})
	response["status"] = "SUCCESS"
	response["billing_id"] = data["billing_id"]
	if err != nil {
		response["status"] = "ERROR"
		response["msg"] = err.Error()
	}

	log.Info("create billing from web pos: %v", response)
	_ = json.NewEncoder(ctx).Encode(response)
}

func SubscribeToMultipleService(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("admin_id"))
	var subscription models.SubscriptionRequest
	err := json.Unmarshal(ctx.PostBody(), &subscription)
	log.IfError(err)

	data, err := createMultipleService(subscription, adminId)
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Data: data})
}

func createMultipleService(subscription models.SubscriptionRequest, adminId string) (map[string]interface{}, error) {
	serviceIds := make([]interface{}, 0)
	for _, service := range subscription.Services {
		serviceIds = append(serviceIds, service.ServiceID)
	}

	sql := fmt.Sprintf("select * from system_service where sys_service_id in (%s?)", strings.Repeat("?,", len(serviceIds)-1))
	billingServices, err := db.QueryArray(sql, serviceIds...)
	log.IfError(err)

	subtotal := 0
	for i, service := range subscription.Services {
		found := false
		for _, billing := range billingServices {
			if service.ServiceID == utils.ToInt(billing["sys_service_id"]) {
				subscription.Services[i].ServiceDetail = billing
				subtotal += utils.ToInt(service.Qty) * utils.ToInt(billing["price"]) * utils.ToInt(service.Period)
				found = true
				break
			}
		}
		if !found {
			return nil, fmt.Errorf("service_id not found: %d", service.ServiceID)
		}
	}

	if subscription.Discount > 0 && subscription.DiscountNote != "" {
		subtotal -= subscription.Discount
	} else {
		subscription.Discount = 0
		subscription.DiscountNote = ""
	}

	if subtotal >= 1000000000 {
		return nil, fmt.Errorf("total exceed limit")
	}

	//generate unique invoice id
	sql = `select invoice
from system_billing
where invoice like ?
order by billing_id desc
limit 1 `

	monthsInRome := [12]string{"I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X", "XI", "XII"}
	invcFormat := "%" + fmt.Sprintf("/POS/%s/%d", monthsInRome[time.Now().Month()], time.Now().Year())
	log.Info("invoice format: %s", invcFormat)

	lastBilling, err := db.Query(sql, invcFormat)
	if err != nil {
		return nil, err
	}
	fmt.Println("last invoice number: ", lastBilling["invoice"])
	number := 1
	if len(lastBilling) > 0 {
		idx := strings.Index(utils.ToString(lastBilling["invoice"]), "/")
		number = utils.ToInt(utils.ToString(lastBilling["invoice"])[:idx]) + 1
	}

	invoiceId := fmt.Sprintf("%03d/POS/%s/%d", number, monthsInRome[time.Now().Month()], time.Now().Year())
	log.Info("new invoice: %s", invoiceId)

	admin, err := db.Query("select business_name, name, email, phone,register_ref_code from admin where admin_id = ?", adminId)
	if log.IfError(err) {
		return nil, err
	}

	businessName := utils.ToString(admin["business_name"])
	rReplaceUnwantedChar := regexp.MustCompile(`[^a-zA-Z]`)
	rRemoveAbundantSpace := regexp.MustCompile(`\s+`)
	res := rReplaceUnwantedChar.ReplaceAllString(businessName, " ")
	businessName = rRemoveAbundantSpace.ReplaceAllString(res, " ")

	var paymentCallBack map[string]interface{}
	if subscription.PaymentMethod != "" {
		//create request to payment gateway
		req := utils.HttpRequest{
			Method: "POST",
			Url:    "https://api.xendit.co/callback_virtual_accounts",
			Header: map[string]interface{}{
				"Authorization": "Basic " + base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:", os.Getenv("XENDIT_SECRET_KEY")))),
			},
			PostRequest: utils.PostRequest{Body: map[string]interface{}{
				"external_id":     invoiceId,
				"bank_code":       subscription.PaymentMethod,
				"name":            businessName,
				"is_closed":       true,
				"expected_amount": subtotal,
				"expiration_date": time.Now().AddDate(0, 0, 3).Format(time.RFC3339),
			}},
		}
		resp, err := req.Execute()
		log.Info("payment gateway response: %s - err: %v", string(resp), err)
		if err != nil {
			return nil, err
		}
		err = json.Unmarshal(resp, &paymentCallBack)
		log.IfError(err)
	}

	var billingId int64
	err = db.WithTransaction(func(transaction db.Transaction) error {
		resp, _ := transaction.Insert("system_billing", map[string]interface{}{
			"invoice":            invoiceId,
			"unique_code":        "0",
			"admin_fkid":         adminId,
			"detail_total":       subtotal,
			"time_order":         time.Now().Unix() * 1000,
			"time_order_expired": time.Now().AddDate(0, 0, 3).Unix() * 1000,
			"payment_code":       paymentCallBack["account_number"],
			"payment_type":       subscription.PaymentMethod + " virtual account",
			"payment_result":     utils.SimplyToJson(paymentCallBack),
			"billing_status":     "pending",
			"data_created":       time.Now().Unix() * 1000,
			"discount":           subscription.Discount,
			"discount_note":      subscription.DiscountNote,
		})
		billingId, _ = resp.LastInsertId()
		fmt.Println("system_billing id: ", billingId)
		for _, service := range subscription.Services {
			_, _ = transaction.Insert("system_billing_detail", map[string]interface{}{
				"billing_fkid":       billingId,
				"sys_service_fkid":   service.ServiceID,
				"qty":                service.Qty,
				"service_length_day": service.ServiceDetail["service_length_day"],
				"service_period":     service.Period,
				"price":              service.ServiceDetail["price"],
				"service_type":       service.ServiceDetail["service_type"],
			})
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	if strings.ToLower(utils.ToString(admin["register_ref_code"])) == "wolvem2022" {
		admin["phone"] = "*************"
	}

	//inform owner
	go func(admin, payment map[string]interface{}) {
		message := `
*[CUSTOMER INVOICE]*
*Dear %s*,
Kami informasikan bahwa Invoice telah dibuat pada tanggal %s dengan nomor Invoice *%s*

*STATUS*
PENDING

*TOTAL TAGIHAN*
Rp%s

*METODE PEMBAYARAN*
%s

*KODE VIRTUAL ACCOUNT*
%s

detail invoice: _%s_

Terima Kasih telah mimilih UNIQ untuk kemudahan bisnis Anda.
www.uniq.id
`

		baseUrl := utils.BaseUrl()
		invoiceUrl := fmt.Sprintf("%s//invoice?id=2&invoice=%s", baseUrl, paymentCallBack["external_id"])
		if os.Getenv("server") == "production" {
			invoiceUrl = utils.ShortUniq(utils.ShortUrlModel{
				LongUrl: invoiceUrl,
				Title:   "Invoice",
			})
		}

		message = fmt.Sprintf(message,
			utils.ToString(admin["name"]),
			time.Now().Format("02/01/2006"),
			paymentCallBack["external_id"],
			utils.CurrencyFormat(utils.ToInt(paymentCallBack["expected_amount"])),
			utils.ToString(paymentCallBack["bank_code"])+" Virtual Account",
			paymentCallBack["account_number"], invoiceUrl)
		_, err = db.Insert("scheduled_message", map[string]interface{}{
			"title":        "Customer Invoice",
			"message":      message,
			"time_deliver": time.Now().Unix() * 1000,
			"data_created": time.Now().Unix() * 1000,
			"media":        "whatsapp",
			"receiver":     admin["phone"],
		})
		log.IfError(err)
	}(admin, paymentCallBack)

	paymentCallBack["billing_id"] = billingId
	return paymentCallBack, nil
}

func CreateSubscription(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("admin_id"))
	serviceId := ctx.PostArgs().Peek("service_id")
	period := ctx.PostArgs().Peek("period")
	paymentMethod := string(ctx.PostArgs().Peek("payment_method"))
	slot := ctx.PostArgs().Peek("slot")

	log.Info("create billing: %s", ctx.PostArgs().String())

	billingService, err := db.Query("select * from system_service where sys_service_id = ?", serviceId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	log.Info("service detail: %s", utils.SimplyToJson(billingService))
	subtotal := utils.ToInt(slot) * utils.ToInt(billingService["price"]) * utils.ToInt(period)

	if subtotal >= 1000000000 {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "total exceed limit", Millis: time.Now().Unix() * 1000})
		return
	}

	//generate unique invoice id
	sql := `select invoice
from system_billing
where invoice like ?
order by billing_id desc
limit 1 `

	monthsInRome := [13]string{"", "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X", "XI", "XII"}
	invcFormat := "%" + fmt.Sprintf("/POS/%s/%d", monthsInRome[time.Now().Month()], time.Now().Year())
	log.Info("invoice format: %s", invcFormat)

	lastBilling, err := db.Query(sql, invcFormat)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}
	fmt.Println("last invoice number: ", lastBilling["invoice"])
	number := 1
	if len(lastBilling) > 0 {
		idx := strings.Index(utils.ToString(lastBilling["invoice"]), "/")
		number = utils.ToInt(utils.ToString(lastBilling["invoice"])[:idx]) + 1
	}

	invoiceId := fmt.Sprintf("%03d/POS/%s/%d", number, monthsInRome[time.Now().Month()], time.Now().Year())
	log.Info("new invoice: %s", invoiceId)

	admin, err := db.Query("select business_name, name, email, phone, register_ref_code from admin where admin_id = ?", adminId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	businessName := utils.ToString(admin["business_name"])
	rReplaceUnwantedChar := regexp.MustCompile(`[^a-zA-Z]`)
	rRemoveAbundantSpace := regexp.MustCompile(`\s+`)
	res := rReplaceUnwantedChar.ReplaceAllString(businessName, " ")
	businessName = rRemoveAbundantSpace.ReplaceAllString(res, " ")

	//phone := utils.ToString(admin["phone"])
	//if strings.HasPrefix(phone, "08") {
	//	phone = phone[1:]
	//}
	//if strings.HasPrefix(phone, "62") {
	//	phone = phone[2:]
	//}

	//create request to payment gateway
	req := utils.HttpRequest{
		Method: "POST",
		Url:    "https://api.xendit.co/callback_virtual_accounts",
		Header: map[string]interface{}{
			"Authorization": "Basic " + base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:", os.Getenv("XENDIT_SECRET_KEY")))),
		},
		PostRequest: utils.PostRequest{Body: map[string]interface{}{
			"external_id": invoiceId,
			"bank_code":   paymentMethod,
			"name":        businessName,
			//"virtual_account_number": phone,
			"is_single_use":   true,
			"is_closed":       true,
			"expected_amount": subtotal,
			"expiration_date": time.Now().AddDate(0, 0, 3).Format(time.RFC3339),
		}},
	}
	resp, err := req.Execute()
	log.Info("payment gateway response: %s - err: %v", string(resp), err)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}
	var paymentCallBack map[string]interface{}
	err = json.Unmarshal(resp, &paymentCallBack)
	log.IfError(err)

	err = db.WithTransaction(func(transaction db.Transaction) error {
		resp, _ := transaction.Insert("system_billing", map[string]interface{}{
			"invoice":            invoiceId,
			"unique_code":        "0",
			"admin_fkid":         adminId,
			"detail_total":       subtotal,
			"time_order":         time.Now().Unix() * 1000,
			"time_order_expired": time.Now().AddDate(0, 0, 3).Unix() * 1000,
			"payment_code":       paymentCallBack["account_number"],
			"payment_type":       paymentMethod + " virtual account",
			"payment_result":     utils.SimplyToJson(paymentCallBack),
			"billing_status":     "pending",
			"data_created":       time.Now().Unix() * 1000,
		})
		id, _ := resp.LastInsertId()
		fmt.Println("system_billing id: ", id)

		_, _ = transaction.Insert("system_billing_detail", map[string]interface{}{
			"billing_fkid":       id,
			"sys_service_fkid":   serviceId,
			"qty":                slot,
			"service_length_day": billingService["service_length_day"],
			"service_period":     period,
			"price":              billingService["price"],
			"service_type":       billingService["service_type"],
		})
		return nil
	})
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if strings.ToLower(utils.ToString(admin["register_ref_code"])) == "wolvem2022" {
		admin["phone"] = "*************"
	}

	//inform owner
	go sendInvoiceInfo(admin, paymentCallBack)

	// go func(admin, payment map[string]interface{}) {
	// }(admin, paymentCallBack)

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Data: paymentCallBack})
}

func sendInvoiceInfo(admin map[string]interface{}, paymentCallBack map[string]interface{}) {
	message := `
*[CUSTOMER INVOICE]*
*Dear %s*,
Kami informasikan bahwa Invoice telah dibuat pada tanggal %s dengan nomor Invoice *%s*

*STATUS*
PENDING

*TOTAL TAGIHAN*
Rp%s

*METODE PEMBAYARAN*
%s

*KODE VIRTUAL ACCOUNT*
%s

Terima Kasih telah mimilih UNIQ untuk kemudahan bisnis Anda.
www.uniq.id
`

	message = fmt.Sprintf(message,
		utils.ToString(admin["name"]),
		time.Now().Format("02/01/2006"),
		paymentCallBack["external_id"],
		utils.CurrencyFormat(utils.ToInt(paymentCallBack["expected_amount"])),
		utils.ToString(paymentCallBack["bank_code"])+" Virtual Account",
		paymentCallBack["account_number"])
	_, err := db.Insert("scheduled_message", map[string]interface{}{
		"title":        "Customer Invoice",
		"message":      message,
		"time_deliver": time.Now().Unix() * 1000,
		"data_created": time.Now().Unix() * 1000,
		"media":        "whatsapp",
		"receiver":     admin["phone"],
	})
	log.IfError(err)
}

func GetSubscriptionStatus(ctx *fasthttp.RequestCtx) {
	deviceId := string(ctx.QueryArgs().Peek("device_id"))
	adminId := string(ctx.Request.Header.Peek("admin_id"))

	//check subscription attached to device
	deviceLogin, err := db.Query("select dl.system_subscribe_fkid from device_login dl "+
		"join devices d on dl.device_fkid = d.device_id where imei= ? LIMIT 1 ", deviceId)
	log.IfError(err)

	//get trial status
	trial, err := getTrialStatus(adminId, false)
	log.IfError(err)

	sql := `
select service_time_expired,
datediff(from_unixtime(service_time_expired / 1000 + 25200, '%Y-%m-%d'),
                from_unixtime((select unix_timestamp(now())) + 25200, '%Y-%m-%d')) as dayLeft
from system_subscribe
where admin_fkid = ?
  and feature = 'device'
  and service_time_expired > (SELECT UNIX_TIMESTAMP() * 1000)
 $where_condition
order by service_time_expired limit 1
`
	addWhereCond := ""
	if len(deviceLogin) > 0 {
		addWhereCond = fmt.Sprintf("and id = %v", deviceLogin["system_subscribe_fkid"])
	}

	sql = strings.Replace(sql, "$where_condition", addWhereCond, -1)

	subscription, err := db.Query(sql, adminId)
	log.IfError(err)

	result := make(map[string]interface{})

	result["subscription_type"] = "trial"
	result["time_expired_count"] = trial.DaysLeft
	result["count_unit"] = "day"
	result["time_now"] = time.Now().Unix() * 1000
	if trial.DaysLeft >= 0 {
		result["subscription_message"] = fmt.Sprintf("Trial %d days left", trial.DaysLeft)
	} else {
		result["subscription_message"] = "Trial Ended"
	}

	if len(subscription) > 0 {
		result["subscribed"] = true
		result["subscription_message"] = fmt.Sprintf("%v - subscribed", result["subscription_message"])
	}

	if trial.DaysLeft <= 0 && len(subscription) > 0 {
		result["subscription_type"] = "subscription"
		result["time_expired"] = subscription["service_time_expired"]
		result["time_expired_count"] = subscription["dayLeft"]

		if utils.ToInt(subscription["dayLeft"]) <= 0 {
			result["subscription_message"] = "Subscription Ended"
		} else if utils.ToInt(subscription["dayLeft"]) <= 7 {
			result["subscription_message"] = fmt.Sprintf("Expires in %v days", subscription["dayLeft"])
		} else {
			expired := time.Unix(utils.ToInt64(subscription["service_time_expired"])/1000, 0).Format("02 Jan 2006")
			result["subscription_message"] = fmt.Sprintf("Expires on %s", expired)
		}
	}

	result["subscription_url"] = fmt.Sprintf("%s/settings/subscription-add", utils.BaseUrl())

	//data := map[string]interface{}{
	//	"subscription_type" : "trial", //trial or subscription
	//	"time_expired" : 1564382753105,
	//	"time_now" : 1564382753105,
	//	"time_expired_count" : 5,
	//	"count_unit" : "day", //day, month, week
	//	"warning_message": "Masa trial anda akan berakhir dalam waktu 3 hari, segera lakukan perpanjangan. Setelah masa trial habis, akun ini tidak dapat digunakan lagi",
	//}

	//log.Info("subscription adminId %v (deviceId: %v) -> %v", adminId, deviceId, utils.SimplyToJson(result))
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Data: result})
}
