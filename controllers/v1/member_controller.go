package v1

import (
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/integration/behave"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

func GetMemberType(ctx *fasthttp.RequestCtx) {
	adminId := ctx.Request.Header.Peek("admin_id")
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: true}

	data, err := db.QueryArray("SELECT * FROM member_type_detail WHERE admin_fkid=?", adminId)
	utils.CheckErr(err)

	response.Data = data

	json.NewEncoder(ctx).Encode(response)
}

func GetMembers(ctx *fasthttp.RequestCtx) {
	adminId := ctx.Request.Header.Peek("admin_id")

	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: true}

	sql := `SELECT m.*, p.name as type_name, mt.type_id
FROM members m
         join members_detail md on m.member_id = md.member_fkid
         join members_type mt on md.type_fkid = mt.type_id
         join products p on mt.product_fkid = p.product_id
where md.admin_fkid = ? `
	data, err := db.QueryArray(sql, adminId)
	log.IfError(err)

	response.Data = data

	_ = json.NewEncoder(ctx).Encode(response)
}

func ExtractMemberIdFromQr(qr string) int {
	memberId := qr
	if strings.HasPrefix(strings.TrimSpace(strings.ToLower(qr)), "https") {
		u, err := url.Parse(qr)
		log.IfError(err)
		if err == nil {
			text := u.Query().Get("text")
			log.Info("get barcode from url, value: %v, origin: %s", text, qr)
			if text != "" {
				memberId = text
			}
		}
	} else {
		log.Info("%v is not https...", qr)
	}

	//first, check from UNIQ member
	if memberIdDec := utils.DecryptWithKey(memberId, utils.KEY_MEMBER_BARCODE); memberIdDec != "" {
		memberId = memberIdDec
	}
	log.Info("member id : '%s' -- qrCode: '%s'", memberId, qr)

	//if its not number, try to change uppercase to lowercase, and vice versa
	if !utils.IsNumber(memberId) {
		tmpMemberId := utils.DecryptWithKey(utils.ReverseString(memberId), utils.KEY_MEMBER_BARCODE)
		if utils.IsNumber(tmpMemberId) {
			memberId = tmpMemberId
		}
	}

	if len(qr) > 5 && memberId == "" {
		log.IfError(fmt.Errorf("can not decrypt member id with barcode: %s", qr))
	}
	return cast.ToInt(memberId)
}

func CheckMember(ctx *fasthttp.RequestCtx) {
	outletId := ctx.UserValue("outletId")
	adminId := ctx.Request.Header.Peek("admin_id")
	barcode := string(ctx.QueryArgs().Peek("barcode"))

	if barcode == "" {
		log.Warn("[CheckMember] barcode is empty...")
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "barcode is required"})
		return
	}
	if valid := auth.ValidateOutlet(ctx); !valid {
		log.Warn("[CheckMember] unauthorize outlet")
		ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		return
	}

	memberId := ExtractMemberIdFromQr(barcode)

	isHasToCheckToBehave := true
	//if decrypt result is number, check to uniq member database
	if utils.IsNumber(memberId) {
		sql := `SELECT member_id, m.name, phone, type_fkid, p.name as type_name
FROM members m
         join members_detail md on m.member_id = md.member_fkid
         join members_type mt on md.type_fkid = mt.type_id 
         join products p on mt.product_fkid = p.product_id 
WHERE member_id = ?
  and md.admin_fkid = ? `
		data, err := db.Query(sql, memberId, string(adminId))
		if log.IfErrorSetStatus(ctx, err) {
			return
		}

		if len(data) > 0 {
			log.Info("member %s is found as UNIQ member with name %s", memberId, data["name"])
			isHasToCheckToBehave = false
			data["source"] = "uniq"
			_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: data})
			return
		} else if !utils.IsAllowedToIntegrateWithBehave() {
			log.Info("member '%s' not found in uniq db", memberId)
			isHasToCheckToBehave = false
		}
	}

	if isHasToCheckToBehave && utils.IsAllowedToIntegrateWithBehave() {
		log.Debug("checking member in behave server...")
		outlet, _ := strconv.Atoi(utils.ToString(outletId))
		data := map[string]interface{}{
			"store_code": fmt.Sprintf("%03d", outlet),
			"uid":        barcode,
		}

		resp, err := behave.SendRequest(behave.CHECK_MEMBER, data)
		if utils.CheckErr(err) {
			_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "Member not found!"})
			return
		}

		fmt.Println("check member resp : ", string(resp))
		behaveResp := make(map[string]interface{})
		//behaveResult := make(map[string]interface{})
		var memberBehave models.MemberBehave
		err = json.Unmarshal(resp, &behaveResp)
		log.IfError(err)

		if behaveResp["result"] != nil {
			jsonResult, err := json.Marshal(behaveResp["result"])
			utils.CheckErr(err)

			err = json.Unmarshal(jsonResult, &memberBehave)
			utils.CheckErr(err)
			//behaveResult["source"] = "behave"
			//behaveResult["member_id"] = behaveResult["uid"]
			memberBehave.Source = "behave"
			memberBehave.MemberId = memberBehave.UID

			go addMemberFromBehave(memberBehave, string(adminId))
		}

		status := utils.ToString(behaveResp["status"]) != "fail"
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: status, Message: "Member not found!", Data: memberBehave})
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "Member Not Found!"})
}

func CheckSecretCode(ctx *fasthttp.RequestCtx) {
	code := string(ctx.QueryArgs().Peek("code"))
	adminId := ctx.Request.Header.Peek("admin_id")

	sql := `select member_id, m.name, phone, type_fkid, p.name as type_name
from members m
         join members_detail md on m.member_id = md.member_fkid
         join members_type mt on md.type_fkid = mt.type_id
         join products p on mt.product_fkid = p.product_id
where md.admin_fkid = ? and `

	params := make([]interface{}, 0)
	params = append(params, adminId)
	if utils.IsNumber(code) {
		sql += " (secret_id = ? and secret_id_expired >= (SELECT UNIX_TIMESTAMP() * 1000) ) "
		params = append(params, code)
	} else {
		memberId := utils.DecryptWithKey(code, utils.KEY_MEMBER_BARCODE)
		log.Info("member id : '%s' -- qrCode: %s", memberId, code)

		if len(code) > 5 && memberId == "" {
			log.IfError(fmt.Errorf("can not decrypt member id with barcode: %s", code))
		}
		sql += " m.member_id = ? "
		params = append(params, memberId)
	}

	data, err := db.Query(sql, params...)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(data) > 0 {
		log.Info("secret code %s is found as UNIQ member with name %s", code, data["name"])
		data["source"] = "uniq"
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: data})
	} else {
		log.Info("secret code %s is not found or expired", code)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "Not Found!"})
	}
}
