package v1

import (
	"encoding/json"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

func GetHelpData(ctx *fasthttp.RequestCtx) {
	data := []models.Help{
		{Title: "Cara Melakukan Transaksi di Android", Content: "https://www.uniq.id/blog/?p=423#tutorial-aplikasi-uniq", ContentType: "web"},
		{Title: "Cara Mem-Void atau Membatalkan Pembelian Item", Content: "https://www.uniq.id/blog/?p=423#transaksi-dengan-void", ContentType: "web"},
		{Title: "Cara Melakukan Refund atau Membatalkan Transaksi", Content: "https://www.uniq.id/blog/?p=423#transaksi-dengan-refund", ContentType: "web"},
		{Title: "Cara Men-Split Transaksi", Content: "https://www.uniq.id/blog/?p=423#transaksi-split-bill", ContentType: "web"},
		{Title: "Cara Menambahkan Produk/Menu Dari Android", Content: "https://www.uniq.id/blog/?p=423#menambah-catalog", ContentType: "web"},
		{Title: "Menambahkan Produk/Menu Dari Web", Content: "https://www.uniq.id/blog/?p=451#menambah-produk-dari-web", ContentType: "web"},
		{Title: "Export/Import Menu Dari Web", Content: "https://www.uniq.id/blog/?p=451#export-import-product", ContentType: "web"},
		{Title: "Mengubah Data Varian Produk", Content: "https://www.uniq.id/blog/?p=451#ubah-varian", ContentType: "web"},
		{Title: "Mengatur Komposisi Menu", Content: "https://www.uniq.id/blog/?p=451#menambah-brekdown", ContentType: "web"},
		{Title: "Melakukan Purchase/Pembelian Produk", Content: "https://www.uniq.id/blog/?p=451#purchase-product", ContentType: "web"},
		{Title: "Penyesuaian Stock Dengan Sistem atau Stock Opname", Content: "https://www.uniq.id/blog/?p=451#stock-opname", ContentType: "web"},
		{Title: "Menambahkan Data Barang/Produk Yang Rusak", Content: "https://www.uniq.id/blog/?p=451#spoil", ContentType: "web"},
		{Title: "Memperpanjang Langganan UNIQ", Content: "https://www.uniq.id/blog/?p=451#subscribe", ContentType: "web"},
	}

	//data := []models.Help{
	//	{Title: "Cara Melakukan Transaksi di Android", Content: "https://www.uniq.id/blog/?p=522", ContentType: "web"},
	//	{Title: "Cara Mem-Void atau Membatalkan Pembelian Item", Content: "https://www.uniq.id/blog/?p=529", ContentType: "web"},
	//	{Title: "Cara Melakukan Refund atau Membatalkan Transaksi", Content: "https://www.uniq.id/blog/?p=531", ContentType: "web"},
	//	{Title: "Cara Men-Split Transaksi", Content: "https://www.uniq.id/blog/?p=534", ContentType: "web"},
	//	{Title: "Cara Menambahkan Produk/Menu Dari Android", Content: "https://www.uniq.id/blog/?p=536", ContentType: "web"},
	//	{Title: "Menambahkan Produk/Menu Dari Web", Content: "https://www.uniq.id/blog/?p=451#menambah-produk-dari-web", ContentType: "web"},
	//	{Title: "Export/Import Menu Dari Web", Content: "https://www.uniq.id/blog/?p=451#export-import-product", ContentType: "web"},
	//	{Title: "Mengubah Data Varian Produk", Content: "https://www.uniq.id/blog/?p=451#ubah-varian", ContentType: "web"},
	//	{Title: "Mengatur Komposisi Menu", Content: "https://www.uniq.id/blog/?p=451#menambah-brekdown", ContentType: "web"},
	//	{Title: "Melakukan Purchase/Pembelian Produk", Content: "https://www.uniq.id/blog/?p=451#purchase-product", ContentType: "web"},
	//	{Title: "Penyesuaian Stock Dengan Sistem atau Stock Opname", Content: "https://www.uniq.id/blog/?p=451#stock-opname", ContentType: "web"},
	//	{Title: "Menambahkan Data Barang/Produk Yang Rusak", Content: "https://www.uniq.id/blog/?p=451#spoil", ContentType: "web"},
	//	{Title: "Memperpanjang Langganan UNIQ", Content: "https://www.uniq.id/blog/?p=451#subscribe", ContentType: "web"},
	//}

	var quickReplies []models.QuickReply
	if !log.IfError(db.GetDbJson().Read("help", "all", &quickReplies)) {
		for _, qr := range quickReplies {
			data = append(data, models.Help{
				Title:       qr.Shortcut,
				ContentType: "text",
				Content:     qr.Message,
			})
		}
	}

	//go LoadHelpFromFirebase()

	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: data})
}

func LoadHelpFromFirebase() {
	url := "https://us-central1-chat-support-102fc.cloudfunctions.net/quickReply"

	req := utils.HttpRequest{
		Method: "GET",
		Url:    url,
		Header: map[string]interface{}{
			"Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhcGktcG9zIiwibmFtZSI6ImFubmFzYmxhY2toYXQiLCJpYXQiOjE1MTYyMzkwMjJ9.BiaMmqRAKjbbv-MSNLMR_rgqc9HfRUbBrxK7mVonF64",
		},
	}

	resp, err := req.Execute()
	if log.IfError(err) {
		return
	}

	//log.Debug("help from firebase : %s", string(resp))
	var quickReply []models.QuickReply
	log.IfError(json.Unmarshal(resp, &quickReply))

	log.Debug("got total help from firebase : %d", len(quickReply))

	err = db.GetDbJson().Write("help", "all", quickReply)
	log.IfError(err)
}
