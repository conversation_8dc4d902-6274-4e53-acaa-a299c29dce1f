package v1

import (
	"fmt"
	"strings"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
)

func ReceiptPointMessage(languange, pointLng string, totalPoint int) string {
	templates := map[string]map[bool][]string{
		"id": {
			//has point
			true: {
				"Dapatkan bonus $total $point setiap kali kamu ngasih feedback!",
				"ngasih ulasan bisa dapetin $total $point bonus nih! 🤩",
				"Makasih udah belanja! Yuk, kasih ulasan kamu dan dapetin $point ekstra, bro/sis! 😄",
				"📣 Hai, jangan lupa tinggalkan ulasan ya. Biar bisa dapetin gratis $total $point!",
				"Feedback kamu sangat berarti buat kita, loo! Kamu bisa dapetin $point ekstra sebagai tanda terima kasih.",
				"Sini, kasih tau kita pendapatmu! Kamu bakal dapetin $total $point bonus, guys! 🌟",
				"Mau punya $point tambahan? Berikan ulasanmu sekarang!",
				"Share pengalamanmu dan nikmati manfaatnya dengan $point bonus",
				"Yuk, berikan pendapatmu dan nikmati keuntungan $point tambahan!",
				"Ulasanmu penting buat kami! Dapatkan $total $point sebagai tanda terima kasih.",
				"Kasih feedback dan dapatkan $point ekstra, yuk!",
				"Jangan lewatkan kesempatan untuk dapat $total $point dengan memberikan ulasanmu!",
				"Berikan ulasanmu dan dapatkan $total $point sebagai bonus!",
				"Ulasan kamu bisa dapetin $total $point ekstra, ayo coba sekarang!",
			},

			//no point collection set
			false: {
				"Jangan lupa kasih feedback nya ya ka",
				"Mohon masukan nya ya kak",
				"Minta feedback nya ya kak",
				"Kami tunggu feedbacknya ya kak",
				"Ingin memberikan masukan? Kami siap mendengarkannya melalui ulasanmu",
				"Selalu senang melayani kamu. Jangan lupa berikan ulasan ya!",
				"Ulasanmu sangat berarti bagi kami. Terima kasih!",
				"Berikan masukanmu agar kami bisa lebih baik lagi!",
				"Feedback dari kamu sangat kami nantikan!",
				"Jangan ragu untuk memberikan ulasanmu!",
				"Kami menghargai setiap masukan dari kamu!",
			},
		},
		"en": {
			true: {
				"we love to hear your feedback",
				"wanna get more $point? send us your feedback",
				"Your feedback is valuable! Get $total $point for sharing it.",
				"Share your thoughts and earn $point bonus!",
				"Give us your feedback and enjoy $total $point extra!",
				"Help us improve by giving feedback and earn $point!",
				"Your opinion matters! Get $total $point for your feedback.",
				"Earn $total $point by sharing your feedback!",
				"Your feedback can get you $total $point extra, try it now!",
			},
			false: {
				"we love to hear your feedback",
				"Thank You! we wait for you feedback!",
				"Your feedback is important to us!",
				"Please share your thoughts with us!",
				"We appreciate your feedback!",
				"Help us improve by sharing your feedback!",
				"Don't hesitate to give us your feedback!",
				"Your feedback helps us get better!",
			},
		},
	}

	//set default language if not set
	if _, ok := templates[languange]; !ok {
		languange = "id"
	}

	options := templates[languange][totalPoint > 0]
	id := utils.RandomNumber(0, len(options)-1)
	message := options[id]
	rpl := strings.NewReplacer("$total", cast.ToString(totalPoint), "$point", pointLng)
	return rpl.Replace(message)
}

type NotificationParams struct {
	TotalPoint        int
	OutletName        string
	EarnType          string
	TransactionStatus string
	GrandTotal        int
}

var titles = map[string][]string{
	"success": {
		"Kamu dapat %d point",
		"Yeay! %d point buat kamu",
		"Berhasil! Tambah %d point",
	},
	"other": {
		"Point Bertambah!",
		"Point Baru untukmu!",
		"Kumpulkan lebih banyak point!",
	},
	"refund": {
		"Perubahan Point",
		"Oops! Point Berkurang",
		"Pengurangan Point Terjadi",
	},
}

var messages = map[string][]string{
	"nominal_success": {
		"Selamat, kamu mendapatkan %d point karena sudah berbelanja sebesar Rp%d di %s",
		"Hore! %d point masuk ke akunmu setelah belanja Rp%d di %s",
		"Terima kasih sudah belanja di %s! Kamu dapat %d point dari transaksi Rp%d",
	},
	"other_success": {
		"Selamat, kamu mendapatkan tambahan point karena sudah berbelanja di %s",
		"Point kamu bertambah! Terima kasih telah belanja di %s",
		"Berbelanja di %s memang menyenangkan! Point kamu bertambah lagi!",
	},
	"refund": {
		"Ops, point kamu harus berkurang %d. Transaksi senilai Rp%d di %s telah di refund",
		"Oops! Point kamu berkurang %d karena ada refund transaksi Rp%d di %s",
		"Maaf, ada perubahan point! Transaksi Rp%d di %s telah di-refund, jadi point berkurang %d",
	},
}

func getRandomNotification(params NotificationParams) (string, string) {
	params.TransactionStatus = strings.ToLower(params.TransactionStatus) //usually the data is like 'Success' / 'Refund'
	if params.TransactionStatus == "" {
		params.TransactionStatus = "success"
	}

	var category string
	if params.TransactionStatus == "refund" {
		category = "refund"
	} else {
		if params.EarnType == "nominal" {
			category = "nominal_success"
		} else {
			category = "other_success"
		}
	}

	fmt.Printf("transStatus: %s, category: %s, size titles: %v, size message: %v\n", params.TransactionStatus, category, len(titles[params.TransactionStatus]), len(messages[category]))
	title := fmt.Sprintf(titles[params.TransactionStatus][utils.RandomNumber(0, len(titles[params.TransactionStatus]))], params.TotalPoint)
	message := fmt.Sprintf(messages[category][utils.RandomNumber(0, len(messages[category]))], params.TotalPoint, params.GrandTotal, params.OutletName)

	return title, message
}
