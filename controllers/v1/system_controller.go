package v1

import (
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/gdrive"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	aes256 "gitlab.com/uniqdev/backend/api-pos/core/security"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

func UploadLog(ctx *fasthttp.RequestCtx) {
	adminId := ctx.Request.Header.Peek("admin_id")
	fileHeader, err := ctx.FormFile("file")
	log.IfError(err)

	formValue := GetFormValueMap(ctx)
	device := formValue["device"]
	imei := formValue["imei"]
	outletId := formValue["outlet_id"]

	if device == "STA ZH960" && !strings.HasPrefix(imei, "iq") {
		imei = outletId + "_" + imei
	}

	outletInt, _ := strconv.Atoi(outletId)
	if !auth.ValidateOutletId(ctx, outletInt) {
		return
	}

	file, err := fileHeader.Open()
	if log.IfError(err) {
		return
	}

	defer file.Close()
	//fmt.Fprintf(ctx, "%v", fileHeader.Header)

	baseDir := "temp/logs"
	if _, err = os.Stat(baseDir); os.IsNotExist(err) {
		log.IfError(os.MkdirAll(baseDir, os.ModePerm))
	}

	tempFilename := fmt.Sprintf("%s/%s_%s_%d", baseDir, fileHeader.Filename, imei, time.Now().Unix())
	f, err := os.OpenFile(tempFilename, os.O_WRONLY|os.O_CREATE, 0666)
	if log.IfError(err) {
		fmt.Println("openfile Error : ", err)
		json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false})
		return
	}

	//fmt.Println("File size : ", fileHeader.Size)

	defer f.Close()
	_, err = io.Copy(f, file)
	log.IfError(err)

	log.Info("log size : %d - %s", fileHeader.Size, tempFilename)
	fileSizeMb := fileHeader.Size / 1000000

	//or use
	//err = fasthttp.SaveMultipartFile(fileHeader, tempFilename)
	//log.IfError(err)

	fileName := fileHeader.Filename

	go func(fileName, tempFilename, adminId, outletId, imei string, fileSizeMb int64) {
		year := fileName[:7]
		month := fileName[:10]

		input, err := ioutil.ReadFile(tempFilename)
		log.IfError(err)

		lines := strings.Split(string(input), "\n")
		if len(lines) > 0 && strings.HasPrefix(lines[0], "version") && lines[0] != "version: 0" {
			log.Info("log version : %s", lines[0])
			for i, line := range lines {
				if i == 0 {
					lines[i] = line
				} else {
					lines[i] = aes256.Decrypt(line, "sERycGn9k3bRSmHnq7bgJwJ59KVpcL6G")
				}
			}
		}

		output := strings.Join(lines, "\n")
		err = ioutil.WriteFile(tempFilename, []byte(output), 0666)
		log.IfError(err)

		admin, err := db.Query("SELECT business_name, name FROM admin WHERE admin_id = ?", adminId)
		utils.CheckErr(err)

		outlet, err := db.Query("SELECT name FROM outlets WHERE outlet_id = ?", outletId)
		utils.CheckErr(err)

		businessName := admin["business_name"]
		if businessName == nil {
			businessName = admin["name"]
		}

		r := strings.NewReplacer("'", " ")
		businessName = r.Replace(utils.ToString(businessName))
		outletName := r.Replace(utils.ToString(outlet["name"]))

		//2018_12_14
		destinationPath := fmt.Sprintf("UNIQ/APP LOG %s/%s/%s/%s - %s/%s/%s/%s",
			strings.ToUpper(os.Getenv("ENV")), businessName, outletName, device, imei, year, month, fileName)

		//fmt.Println("origin : ", tempFilename)
		//fmt.Println("destin : ", destinationPath)

		//_, err = gdrive.UploadToGoogleDrive(tempFilename, destinationPath)
		driverFile, err := gdrive.UploadToGoogleDriveV2(gdrive.GDriveUploader{
			OriginPath:      tempFilename,
			DestinationPath: destinationPath,
			ParentIndex:     1,
			SubParentIndex:  4,
			SubParentId:     imei,
		})

		if fileSizeMb >= 3 { //notif if log file more/equal than 2 MB
			log.Error("log file size more than expected: %d MB. id: '%s' | a_%s (o_%s) '%s' -> %s", fileSizeMb, driverFile.Id, adminId, outletId, imei, tempFilename)
		}

		if log.IfError(err) {
			db := db.GetDbJson()
			id := fmt.Sprintf("%v", time.Now().UnixNano())
			err = db.Write("system_log", id, models.SystemLog{FilePath: tempFilename, DestinationPath: destinationPath, Id: id})
			log.IfError(err)
		} else {
			fmt.Printf("device %s uploaded to: %s\n", imei, driverFile.Id)
			if err = os.Remove(tempFilename); err != nil {
				fmt.Println("Removing tmp file : ", tempFilename, "error : ", err)
			}
		}

	}(fileName, tempFilename, string(adminId), outletId, imei, fileSizeMb)

	json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true})
}

func MacAddressVendorLookup(ctx *fasthttp.RequestCtx) {
	macAddress := ctx.UserValue("macaddress")
	log.Info("MacAddressVendorLookup: %v", macAddress)
	req := utils.HttpRequest{
		Method: "GET",
		Url:    "https://www.macvendorlookup.com/api/v2/" + utils.ToString(macAddress),
	}

	body, err := req.Execute()
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if body == nil {
		return
	}

	var result []struct {
		Company string `json:"company,omitempty"`
		Country string `json:"country,omitempty"`
	}

	// result := make(map[string]map[string]interface{})
	err = json.Unmarshal(body, &result)
	if log.IfErrorSetStatus(ctx, err) {
		log.Info("body --> %v", string(body))
		return
	}

	jsonResult := make(map[string]interface{})
	if len(result) > 0 {
		jsonResult = map[string]interface{}{
			"company": result[0].Company,
		}

		_, _ = db.Insert("mac_vendor", map[string]interface{}{
			"mac_address": macAddress,
			"vendor":      result[0].Company,
			"info":        string(body),
		})

	} else {
		fmt.Println("Error - macvendors.co not returning result")
		fmt.Println(string(body))
	}

	//result --->
	/*{
		"address": "1 Infinite Loop,Cupertino  CA  95014,US",
		"company": "Apple, Inc.",
		"country": "US",
		"end_hex": "087402FFFFFF",
		"mac_prefix": "08:74:02",
		"start_hex": "087402000000",
		"type": "MA-L"
	}*/

	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Data: jsonResult})
}

func SetFirebaseToken(ctx *fasthttp.RequestCtx) {
	deviceId := ctx.FormValue("device_id")
	token := ctx.FormValue("token")

	_, err := db.Update("devices", map[string]interface{}{
		"firebase_token": string(token),
	}, "device_id = ?", string(deviceId))

	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success"})
}

func UpdateAppVersion(ctx *fasthttp.RequestCtx) {
	versionCode := ctx.FormValue("version_code")
	versionName := utils.TakeMax(string(ctx.FormValue("version_name")), 20)
	releaseNote := ctx.FormValue("release_note")
	platform := ctx.FormValue("platform")
	track := ctx.FormValue("track")

	_, err := db.Insert("system_app_release", map[string]interface{}{
		"version_code": versionCode,
		"version_name": versionName,
		"track":        track,
		"release_note": releaseNote,
		"time_created": time.Now().Unix() * 1000,
		"platform":     platform,
	})
	if log.IfError(err) {
		return
	}

	//send notification
	sql := `select firebase_token
from devices
where last_sync > (unix_timestamp(date_add(now(), interval -1 month)) * 1000)
  and firebase_token is not null`
	tokens, err := db.QueryArray(sql)
	if len(tokens) == 0 {
		return
	}

	ids := make([]string, len(tokens))
	for _, token := range tokens {
		ids = append(ids, utils.ToString(token["firebase_token"]))

		err = google.SendNotification(google.NotificationData{
			Token: cast.ToString(token["firebase_token"]),
			Data: map[string]string{
				"type":    "new_version",
				"message": "klik untuk mengupdate UNIQ POS versi terbaru",
				"title":   fmt.Sprintf("Update Versi %s Terbaru Tersedia", versionName),
			},
		})
		log.IfError(err)
	}

	fmt.Println("sending notification about new release to: ", len(ids), "users")
	fileHeader, err := ctx.FormFile("file")
	if err != nil {
		fmt.Println(err)
		return
	}

	//fasthttp.SaveMultipartFile(fileHeader, fileHeader.Filename)
	file, err := fileHeader.Open()
	if err != nil {
		fmt.Println("open file err: ", err)
		return
	}

	filePath := os.Getenv("server") + "/apk/" + fileHeader.Filename
	fileUrl, err := google.UploadFile(file, filePath, true)
	if err != nil {
		fmt.Println("upload err: ", err)
		return
	}

	fmt.Println("fileUrl -> ", fileUrl)
	fmt.Println(fileHeader.Filename)

	_, err = db.Update("system_app_release", map[string]interface{}{
		"file": fileUrl,
	}, "version_code = ?", versionCode)
	log.IfError(err)
	ctx.SetStatusCode(fasthttp.StatusOK)
}

func GetAppVersion(ctx *fasthttp.RequestCtx) {
	deviceId := string(ctx.Request.Header.Peek("Device"))
	fmt.Println("device id", deviceId)

	data, err := db.Query("select * from system_app_release where platform is null order by version_code desc limit 1")
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: data})
}

func GetAppVersionV2(ctx *fasthttp.RequestCtx) {
	platform := string(ctx.QueryArgs().Peek("platform"))
	currentVersion := string(ctx.QueryArgs().Peek("current_version_name"))
	currentVersion = strings.TrimSpace(currentVersion)

	if platform == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "patform is required"})
		return
	}

	sql := "select * from system_app_release where platform=? order by version_code desc LIMIT 10"
	data, err := db.QueryArray(sql, platform)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}

	isUpdateAvailable := false
	if currentVersion != "" && len(data) > 0 {
		isUpdateAvailable = isLatestVersionGreater(currentVersion, cast.ToString(data[0]["version_name"]))
	}

	//returns only updated history
	if isUpdateAvailable {
		tmpResult := make([]map[string]interface{}, 0)
		for _, history := range data {
			if isLatestVersionGreater(currentVersion, cast.ToString(history["version_name"])) {
				tmpResult = append(tmpResult, history)
			} else {
				break
			}
		}
		data = tmpResult
	}

	result := map[string]interface{}{
		"release_history":     data,
		"is_update_available": isUpdateAvailable,
	}
	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: result})
}

func isLatestVersionGreater(currentVersion, latestVersion string) bool {
	currentParts := strings.Split(currentVersion, ".")
	latestParts := strings.Split(latestVersion, ".")

	// Pad shorter version with "0" to ensure equal length
	maxLength := len(currentParts)
	if len(latestParts) > maxLength {
		maxLength = len(latestParts)
	}
	for i := len(currentParts); i < maxLength; i++ {
		currentParts = append(currentParts, "0")
	}
	for i := len(latestParts); i < maxLength; i++ {
		latestParts = append(latestParts, "0")
	}

	for i := 0; i < maxLength; i++ {
		currentPart, _ := strconv.Atoi(currentParts[i])
		latestPart, _ := strconv.Atoi(latestParts[i])

		if latestPart > currentPart {
			return true
		} else if latestPart < currentPart {
			return false
		}
	}

	return false // Versions are equal
}
