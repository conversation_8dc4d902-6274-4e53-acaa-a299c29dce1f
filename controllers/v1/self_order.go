package v1

import (
	"encoding/json"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

func GetSelfOrder(ctx *fasthttp.RequestCtx) {
	adminId := ctx.Request.Header.Peek("admin_id")
	orderCode := ctx.UserValue("code")

	sql := `
select s.*,
       m.member_id,
       m.name as name,
       m.phone,
       p.name as customer_level
from self_order s
         left join members m on s.member_fkid = m.member_id
         left join members_detail md on m.member_id = md.member_fkid
         left join members_type mt on md.type_fkid = mt.type_id
         left join products p on mt.product_fkid = p.product_id
where order_code = ?
  and if(m.member_id > 0, md.admin_fkid = ?, true)
order by s.self_order_id desc
LIMIT 1 `

	data, err := db.Query(sql, orderCode, adminId)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(data) == 0 {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "Not Found!"})
		return
	}

	if !auth.ValidateOutletId(ctx, utils.ToInt(data["outlet_fkid"])) {
		ctx.SetStatusCode(fasthttp.StatusOK) //force to ok
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "order tidak dapat dilakukan di outlet ini"})
		return
	}

	if utils.ToInt64(data["expired_at"]) < (time.Now().Unix() * 1000) {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "Order is expired"})
		return
	}

	go func() {
		_, err := db.Update("self_order", map[string]interface{}{"applied_at": time.Now().Unix() * 1000}, "self_order_id = ?", data["self_order_id"])
		log.IfError(err)
	}()

	orderList := make(map[string]interface{})
	err = json.Unmarshal([]byte(utils.ToString(data["order_item"])), &orderList)
	log.IfError(err)

	orderList["customer_name"] = data["customer_name"]
	orderList["contact"] = data["contact"]
	orderList["member"] = utils.TakesOnly(data, "member_id", "phone", "name", "customer_level")
	orderList["dining_table"] = orderList["dining_table"]

	delete(data, "order_item")
	data["detail"] = orderList
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success", Data: orderList})
}
