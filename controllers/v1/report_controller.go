package v1

import (
	"encoding/json"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"strconv"
	"strings"
	"time"
)

func GetCashRecap(ctx *fasthttp.RequestCtx)  {
	millis := time.Now().Unix() * 1000

	if valid := auth.ValidateOutlet(ctx); !valid{
		return
	}

	data, err := db.QueryArray("SELECT cr.*, e.name FROM cash_recap cr JOIN employee e " +
		"ON cr.employee_fkid=e.employee_id WHERE outlet_fkid=? AND time_modified >= ?", ctx.UserValue("outletId"), ctx.UserValue("lastSync"))
	utils.CheckErr(err)

	json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func GetStockSummary(ctx *fasthttp.RequestCtx) {
	limit := ctx.UserValue("limit")
	offset := ctx.UserValue("offset")
	alert := ctx.UserValue("alert")
	adminId := ctx.UserValue("adminId")

	//start := time.Now()
	jsonData, err := db.QueryArray("SELECT * FROM view_stock_summaries WHERE admin_fkid=? LIMIT ?,? ", adminId, offset, limit)
	utils.CheckErr(err)
	//elapsed := time.Since(start)
	//fmt.Println("Select view took : ", elapsed)

	productDetailIds :=  make([]interface{}, 0)
	for _, id := range jsonData{
		productDetailIds = append(productDetailIds, id["product_detail_id"])
	}

	if len(productDetailIds) > 0 {

		stckOpnames, err := db.QueryArray("SELECT MAX(time_created) as last_date, product_detail_fkid FROM stock_opname WHERE product_detail_fkid " +
			"IN ("+ strings.Repeat("?,", len(productDetailIds)-1)+ "?) GROUP BY product_detail_fkid", productDetailIds...)
		utils.CheckErr(err)

		//sales, err := database.Query("SELECT CAST(SUM(qty) AS UNSIGNED ) AS qty FROM sales_detail WHERE  time_created > ? AND product_detail_fkid IN () ",
		//	stckSumm["product_detail_id"], lastDate)

		for _, stckSumm := range jsonData{
			stckOpname := make(map[string]interface{}, 0)
			for _, data := range stckOpnames{
				if data["product_detail_fkid"] == stckSumm["product_detail_id"] {
					stckOpname = data
				}
			}
			//fmt.Println(stckOpname)

			lastDate := stckOpname["last_date"]
			//fmt.Println(lastDate)
			if lastDate == nil{
				lastDate = int64(0)
			}

			//start := time.Now()
			sales, err := db.Query("SELECT CAST(SUM(qty) AS UNSIGNED ) AS qty FROM sales_detail WHERE product_detail_fkid=? AND time_created > ?",
				stckSumm["product_detail_id"], lastDate)
			utils.CheckErr(err)

			//elapsed := time.Since(start)
			//fmt.Println("Sales took : ", elapsed)
			//
			//fmt.Println("product_detail_id : ", stckSumm["product_detail_id"])
			//fmt.Println("last date : ", lastDate)

			qtySales := sales["qty"]
			if qtySales == nil{
				qtySales = int64(0)
			}

			//start = time.Now()

			dateStr, err := utils.MillisToDateTime(lastDate.(int64))
			utils.CheckErr(err)
			spoils, err := db.Query("SELECT qty FROM spoils WHERE product_detail_fkid = ? AND data_created > ?", stckSumm["product_detail_id"], dateStr)
			utils.CheckErr(err)

			//elapsed = time.Since(start)
			//fmt.Println("Spoil took : ", elapsed)

			qtySpoil := spoils["qty"]
			if qtySpoil == nil{
				qtySpoil = int64(0)
			}

			opnames, err := db.Query("SELECT opname FROM stock_opname WHERE product_detail_fkid = ? AND time_created = ?", stckSumm["product_detail_id"], lastDate)
			utils.CheckErr(err)

			qtyOpname := opnames["opname"]
			if qtyOpname == nil{
				qtyOpname = int64(0)
			}

			purchase, err := db.Query("select CAST(SUM(pp.qty_stok - COALESCE(rp.qty_stok, 0)) AS UNSIGNED) as qty from purchase_products pp " +
				"left join retur_products rp ON pp.products_fkid=rp.purchase_product_fkid WHERE pp.products_fkid = ? AND pp.data_created > ?", stckSumm["product_detail_id"], dateStr)
			utils.CheckErr(err)

			qtyPurchase := purchase["qty"]
			if qtyPurchase == nil{
				qtyPurchase = int64(0)
			}

			transfers, err := db.Query("select qty FROM transfer t join transfer_products t2 on " +
				"t.transfer_id = t2.transfer_fkid where t2.product_detail_fkid = ? AND t.data_created > ?", stckSumm["product_detail_id"], dateStr)

			qtyTransfer := transfers["qty"]
			if qtyTransfer == nil{
				qtyTransfer = int64(0)
			}

			//closing = open+purchase-sales-spoil+transfer
			closing := qtyOpname.(int64) +
				qtyPurchase.(int64) -
				qtySales.(int64) -
				qtySpoil.(int64) +
				qtyTransfer.(int64)

			stckSumm["sales"] = qtySales
			stckSumm["spoils"] = qtySpoil
			stckSumm["open"] = qtyOpname
			stckSumm["purchase"] = qtyPurchase
			stckSumm["transfer"] = qtyTransfer
			stckSumm["closing"] = closing

			alertInt, err := strconv.ParseInt(alert.(string), 10, 64)
			if closing < alertInt {
				stckSumm["alert"] = "Butuh"
			}else {
				stckSumm["alert"] = "Aman"
			}
		}
	}

	//jsonData, err := database.QueryArray("SELECT `i`.`product_detail_id` AS `product_detail_id`, `p`.`product_id` AS `product_id`, `p`.`name` AS `ingridient_name`, `p`.`catalogue_type` AS `catalogue_type`, `o`.`outlet_id` AS `outlet_id`, `o`.`name` AS `outlet_name`, `c`.`name` AS `category`, `sc`.`name` AS `subcategory`, `u`.`name` AS `unit`, coalesce(sum(`spoil`.`qty`), 0) AS `qty`, coalesce(sum(`tf`.`qty`), 0) AS `transfer`, coalesce(sum(`prc`.`qty_stok`), 0) AS `purchase`, `p`.`admin_fkid` AS `admin_fkid` FROM ((((((((`products_detail` `i` LEFT JOIN `outlets` `o` ON ((`i`.`outlet_fkid` = `o`.`outlet_id`))) LEFT JOIN `spoils` `spoil` ON ((`i`.`product_detail_id` = `spoil`.`product_detail_fkid`))) LEFT JOIN `products` `p` ON ((`i`.`product_fkid` = `p`.`product_id`))) LEFT JOIN `products_category` `c` ON ((`p`.`product_category_fkid` = `c`.`product_category_id`))) LEFT JOIN `products_subcategory` `sc` ON ((`p`.`product_subcategory_fkid` = `sc`.`product_subcategory_id`))) LEFT JOIN `transfer_products` `tf` ON ((`i`.`product_detail_id` = `tf`.`product_detail_fkid`))) LEFT JOIN `unit` `u` ON ((`p`.`unit_fkid` = `u`.`unit_id`))) LEFT JOIN `purchase_products` `prc` ON ((`i`.`product_detail_id` = `prc`.`products_fkid`))) WHERE (`p`.`stock_management` = 1) GROUP BY `i`.`product_detail_id` LIMIT 10")
	//checkErr(err)

	ctx.SetContentType("application/json")
	ctx.Response.Header.Set("Access-Control-Allow-Origin", "*")
	//ctx.Response.Header.Set("Access-Control-Allow-Headers", "*")
	json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Data: jsonData})
}
