package v1

import (
	"encoding/json"
	"os"
	"testing"

	"gitlab.com/uniqdev/backend/api-pos/models"
)

func Test_generateDynamicLink(t *testing.T) {
	appInfoJson := `{"ios": {"bundle_id": "id.uniq.crm.yamiepanda"}, "web": {"url": "https://yamiepanda-crm.web.app/#"}, "android": {"package_name": "com.uniq.uniqmembership.yamiepanda"}}`

	var appConf models.CrmAppInfo
	err := json.Unmarshal([]byte(appInfoJson), &appConf)
	if err != nil {
		t.Error(err)
	}

	os.Setenv("FIREBASE_CRM_KEY", "AIzaSyDa1RGqlfhiRbqZx8yYg7GVwYoLKDoG0tg")
	os.Setenv("ENV", "localhost")
	os.Setenv("FIREBASE_DYNAMIC_LINK", "https://uniqapps.page.link")

	type args struct {
		appConf          models.CrmAppInfo
		shortFeedbackUrl string
		salesId          string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test-yamie", args{appConf: appConf, shortFeedbackUrl: "https://y.uniq.id", salesId: "1N9HO4S2H010I"}, ""},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := generateDynamicLink(tt.args.appConf, tt.args.shortFeedbackUrl, tt.args.salesId); got != tt.want {
				t.Errorf("generateDynamicLink() = %v, want %v", got, tt.want)
			}
		})
	}
}
