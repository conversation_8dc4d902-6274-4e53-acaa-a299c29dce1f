package v1

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"html/template"
	"sort"
	"strings"
	templateText "text/template"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/format"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"go.mongodb.org/mongo-driver/bson"
)

func SendReportCashRecap(recap models.CashRecap) {
	t := time.Unix(recap.TimeCreated/1000+25200, 0)
	date := t.Format("15:04 02-01-2006")

	shiftReport := CalculateShiftReport(recap)
	shiftReport.Date = date

	//save recap to db
	_, err := db.Mongo().Collection("close_shift").InsertOne(context.Background(), shiftReport)
	log.IfError(err)
	fmt.Printf("close shift %d saved to mongodb\n", shiftReport.OpenShiftId)

	templateWaString, err := utils.ReadFile("config/template/close_shift_template_wa.tmpl")
	if log.IfError(err) {
		return
	}

	templateEmailString, err := utils.ReadFile("config/template/close_shift_template.html")
	if log.IfError(err) {
		return
	}

	currencyFunc := template.FuncMap{
		"Currency": func(num int) string {
			return format.Currency(num)
		},
		"Add": func(nums ...int) int {
			result := 0
			for _, n := range nums {
				result += n
			}
			return result
		},
		"Minus": func(nums ...int) int {
			result := 0
			for i, n := range nums {
				if i == 0 {
					result = n
				} else {
					result -= n
				}
			}
			return result
		},
	}

	currencyFuncText := templateText.FuncMap{}
	for k, v := range currencyFunc {
		currencyFuncText[k] = v
	}

	tmplateWa, err := templateText.New("close_shift_report_wa").Funcs(currencyFuncText).Parse(templateWaString)
	if log.IfError(err) {
		fmt.Println("error - ", err)
	}

	tmplateEmail, err := template.New("close_shift_report_email").Funcs(currencyFunc).Parse(templateEmailString)
	if log.IfError(err) {
		fmt.Println("error - ", err)
	}

	//save total sales calculation to database
	if recap.CashRecapID > 0 {
		_, err = db.Update("cash_recap", map[string]any{
			"sales_total":   shiftReport.ItemSalesTotal,
			"time_modified": time.Now().UnixNano() / 1e6,
		}, "cash_recap_id = ?", recap.CashRecapID)
		log.IfError(err)
	} else {
		log.Warn("no cash recap id, sales total wont be updated!")
	}

	//check user subscription, warn user if subscription or trial is about to end
	subscriptionWarning := ""
	phone := shiftReport.Admin["phone"].(string)

	sub, err := GetSubscriptionDetail(shiftReport.Admin["admin_fkid"])
	log.IfError(err)

	warn := sub.GenerateMessage()

	if warn != "" {
		subscriptionWarning = warn
		log.Error("%s - %v \n%s", shiftReport.Admin["outlet_name"], shiftReport.Admin["admin_fkid"], subscriptionWarning)
		go autoGenerateInvoice(utils.ToInt(shiftReport.Admin["admin_fkid"]))
	}

	//if user has only one outlet and one shift, report the increase/decrease percentage
	////

	//set sender
	sentVia := ""
	socialConnect, err := db.Query(`SELECT sc.identity from admin_social_connect sc 
left join admin_social_connect_receipts scr on sc.id=scr.admin_social_connect_fkid 
where sc.type='wa' and sc.admin_fkid= ?
order by sc.id desc limit 1`, shiftReport.Admin["admin_id"])
	if !log.IfError(err) && len(socialConnect) > 0 {
		sentVia = utils.ToString(socialConnect["identity"])
	}

	var reportWa bytes.Buffer
	err = tmplateWa.Execute(&reportWa, shiftReport)
	log.IfError(err)
	log.Info(reportWa.String())

	scheduleMsg := map[string]any{
		"title":        "CLOSE SHIFT REPORT",
		"message":      reportWa.String() + subscriptionWarning,
		"media":        "whatsapp",
		"receiver":     phone,
		"time_deliver": time.Now().Unix() * 1000,
		"data_created": time.Now().Unix() * 1000,
		"sent_via":     sentVia,
	}

	// err = google.PublishMessage(scheduleMsg, "messaging-gateway-production")
	// if err != nil {
	_, err = db.Insert("scheduled_message", scheduleMsg)
	log.IfError(err)
	// }

	var reportEmail bytes.Buffer
	err = tmplateEmail.Execute(&reportEmail, shiftReport)
	log.IfError(err)

	//also send to email
	//_, err = db.Insert("scheduled_message", map[string]any{
	//	"title":        "CLOSE SHIFT REPORT",
	//	"message":      reportEmail.String(),
	//	"media":        "email",
	//	"receiver":     shiftReport.Admin["email"],
	//	"time_deliver": time.Now().Unix() * 1000,
	//	"data_created": time.Now().Unix() * 1000,
	//})

	sql := `
select coalesce(ej.phone, rraj.address, ej.email) as address,
       (case
            when ej.phone is not null then 'whatsapp'
            when ej.email is not null then 'email'
            else rraj.media
           end) as media
from report_recipient rr
         join report_recipient_type rrt on rr.report_recipient_id = rrt.report_recipient_fkid
         left join (
    select e.employee_id, e.phone, e.email
    from employee e
             join employee_outlet eo on e.employee_id = eo.employee_fkid
    where eo.outlet_fkid = ?
) ej on ej.employee_id = rr.employee_fkid
         left join (
    select rra.report_recipient_fkid, rra.media, rra.address
    from report_recipient_address rra
             join report_recipient_outlet rro on rra.report_recipient_fkid = rro.report_recipient_fkid
    where outlet_fkid = ?
) rraj on rraj.report_recipient_fkid = rr.report_recipient_id
where rr.admin_fkid = ?
  and rrt.report_type_fkid = 'close_shift'
having address is not null `

	receivers, err := db.QueryArray(sql, recap.OutletFkid, recap.OutletFkid, shiftReport.Admin["admin_fkid"])
	if log.IfError(err) {
		return
	}

	for _, receiver := range receivers {
		if utils.ToString(receiver["media"]) == "whatsapp" {
			msgContent := reportWa.String()
			if utils.ToInt(shiftReport.Admin["admin_fkid"]) == 297 {
				msgContent += subscriptionWarning
			}

			scheduleMsg = map[string]any{
				"title":        "CLOSE SHIFT REPORT",
				"message":      msgContent,
				"media":        "whatsapp",
				"receiver":     utils.ToString(receiver["address"]),
				"time_deliver": time.Now().Unix() * 1000,
				"data_created": time.Now().Unix() * 1000,
				"sent_via":     sentVia,
			}
			// err = google.PublishMessage(scheduleMsg, "messaging-gateway-production")
			// if err != nil {
			_, err = db.Insert("scheduled_message", scheduleMsg)
			log.IfError(err)
			// }
		} else if utils.ToString(receiver["media"]) == "email" {
			_, err = db.Insert("scheduled_message", map[string]any{
				"title":        "CLOSE SHIFT " + strings.ToUpper(shiftReport.OutletName),
				"message":      utils.MinifyHtml(reportEmail.String()),
				"media":        "email",
				"receiver":     utils.ToString(receiver["address"]),
				"time_deliver": time.Now().Unix() * 1000,
				"data_created": time.Now().Unix() * 1000,
			})
			log.IfError(err)
		} else {
			log.Info("we will not send to %s", receiver["address"])
		}
	}
}

// get report shift from db
func GetShiftReportFromCachedDb(openShiftId int) models.CloseShiftReport {
	shiftReport := models.CloseShiftReport{}
	filter := bson.D{{Key: "open_shift_id", Value: openShiftId}}
	//filter := bson.M{"date_time": bson.M{"$gt": 1616384881000, "$lt": 1616384892000}}
	err := db.Mongo().Collection("close_shift").FindOne(context.Background(), filter).Decode(&shiftReport)
	log.IfError(err)
	log.Info("get shift report, openShiftId: %d - sales total: %d", openShiftId, shiftReport.ItemSalesTotal)
	return shiftReport
}

func GetCloseShiftAdminAndDate(adminId int, date string) []models.CloseShiftReport {
	log.Info("getCloseShiftAdminAndDate %v | %v", adminId, date)
	filter := bson.M{
		"admin.admin_id": adminId,
		"date":           bson.M{"$regex": fmt.Sprintf(".*%s.*", date)},
	}

	var shiftReport []models.CloseShiftReport
	collection := db.Mongo().Collection("close_shift")
	cursor, err := collection.Find(context.Background(), filter)
	if log.IfError(err) {
		return shiftReport
	}

	defer cursor.Close(context.Background())
	for cursor.Next(context.Background()) {
		var result models.CloseShiftReport
		err := cursor.Decode(&result)
		if log.IfError(err) {
			return shiftReport
		}
		shiftReport = append(shiftReport, result)
	}

	log.IfError(cursor.Err())
	fmt.Println("--size of closeShift: ", len(shiftReport))
	return shiftReport
}

func SendRecapAllOutlet(recap models.CashRecap) {
	data, err := db.Query("select time_open from open_shift where open_shift_id = ?", recap.OpenShiftFkid)
	log.IfError(err)

	if len(data) == 0 {
		log.Info("no data open shift with id %v", recap.OpenShiftFkid)
		return
	}

	dateSecond := utils.ToInt64(data["time_open"])

	sql := `
select cr.*, s.name as shift_name, o.name as outlet_name, o.outlet_id, a.business_name, a.admin_id
from cash_recap cr
         join open_shift os on cr.open_shift_fkid = os.open_shift_id
         join outlets o on cr.outlet_fkid = o.outlet_id
         join admin a on o.admin_fkid = a.admin_id
         join shift s on os.shift_fkid = s.shift_id
where from_unixtime(os.time_open / 1000 + 25200, '%d-%m-%Y') = from_unixtime(? / 1000 + 25200, '%d-%m-%Y')
  and s.admin_fkid = (select admin_fkid
                      from open_shift ops
                               join outlets o2 on ops.outlet_fkid = o2.outlet_id
                      where ops.open_shift_id = ? )
order by os.outlet_fkid`

	cashRecaps, err := db.QueryArray(sql, dateSecond, recap.OpenShiftFkid)
	if log.IfError(err) {
		return
	}
	if len(cashRecaps) == 0 {
		return
	}

	reportResult := ""
	phone := ""
	adminId := ""
	businessName := ""
	totalRecapToday := 0
	totalPerOutlet := 0
	shiftCounter := 0
	cashRecapReport := make([]models.CashRecapReport, 0)

	// Convert milliseconds to time.Time
	t := time.Unix(0, dateSecond*int64(time.Millisecond))
	// Reduce 1 day
	t = t.Add(-24 * time.Hour)
	// Format the date as "dd-MM-yyyy"
	prevDate := t.Format("02-01-2006")
	prevCloseShift := GetCloseShiftAdminAndDate(cast.ToInt(cashRecaps[0]["admin_id"]), prevDate)
	prevCloseShiftByOutlet := make(map[int]int)
	for _, close := range prevCloseShift {
		prevCloseShiftByOutlet[close.Outlet.OutletId] += close.ItemSalesTotal
	}
	log.Info("total from prev data: %v", len(prevCloseShift))

	for index, cashRecap := range cashRecaps {
		cashRecapId := cashRecap["cash_recap_id"]
		delete(cashRecap, "cash_recap_id")
		jsonByte, err := json.Marshal(cashRecap)
		log.IfError(err)

		recapCurrent := models.CashRecap{}
		err = json.Unmarshal(jsonByte, &recapCurrent)
		log.IfError(err)
		recapCurrent.CashRecapID = utils.ToInt64(cashRecapId)

		//report := CalculateShiftReport(recapCurrent)
		report := GetShiftReportFromCachedDb(recapCurrent.OpenShiftFkid)
		if report.DateTime == 0 { //just, in case failed to get from db, do the old fashion
			log.Error("failed to get shift from mongo-db (report all outlet)")
			report = CalculateShiftReport(recapCurrent)
		}

		if index == 0 {
			phone = utils.ToString(report.Admin["phone"])
			adminId = utils.ToString(report.Admin["admin_fkid"])
			businessName = utils.ToString(cashRecap["business_name"])
		}

		if reportResult == "" {
			reportResult += fmt.Sprintf("*%s* \n", strings.TrimSpace(utils.ToString(cashRecap["outlet_name"])))
		}

		shiftCounter++
		totalPerOutlet += report.ItemSalesTotal
		totalRecapToday += report.ItemSalesTotal
		reportResult += fmt.Sprintf("Shift %v : %s \n", cashRecap["shift_name"], utils.CurrencyFormat(report.ItemSalesTotal))

		//add total
		if index == (len(cashRecaps)-1) || cashRecap["outlet_id"] != cashRecaps[index+1]["outlet_id"] {
			if shiftCounter > 1 {
				reportResult += fmt.Sprintf("*TOTAL*     *%s*\n", utils.CurrencyFormat(totalPerOutlet))
			}
			cashRecapReport = append(cashRecapReport, models.CashRecapReport{Total: totalPerOutlet, Report: reportResult, OutletId: cast.ToInt(cashRecap["outlet_id"])})
			totalPerOutlet = 0
			shiftCounter = 0
			reportResult = ""
		}
	}

	if len(cashRecapReport) == 0 {
		log.Info(">> NO REPORT <<")
		return
	}

	//sort descending
	sort.Slice(cashRecapReport, func(i, j int) bool {
		return cashRecapReport[j].Total < cashRecapReport[i].Total
	})

	for rank, report := range cashRecapReport {
		rate := ""
		if totalPrev, ok := prevCloseShiftByOutlet[report.OutletId]; ok {
			if report.Total > totalPrev {
				rate = "⬆️ "
			} else if report.Total < totalPrev {
				rate = "⬇️ "
			} else {
				rate = "↕️ "
			}
		}
		reportResult += fmt.Sprintf("%s*#%d.* %s \n", rate, rank+1, report.Report)
	}

	reportResult += fmt.Sprintf("\n\n*TOTAL SELURUH OUTLET*   *%v*", utils.CurrencyFormat(totalRecapToday))
	reportResult = fmt.Sprintf("UNIQ POS - REKAP LAPORAN TUTUP KASIR SELURUH OUTLET *%s* \n"+
		"_Tanggal : %v_ \n\n%s", strings.ToUpper(businessName), time.Unix(dateSecond/1000, 0).Format("02-01-2006"), reportResult)

	log.Info("REKAP LAPORAN TUTUP KASIR SELURUH OUTLET *%s* ", strings.ToUpper(businessName))
	log.Info("send to %v", phone)

	//_ = utils.SendWhatsAppMessage(reportResult, "secure", adminId, utils.Encrypt(phone))
	//_ = utils.SendMessageToGateWay(reportResult, phone, 0) //param adminId set 0, we wan this sent through UNIQ whatsapp number

	//identifierId is used to check whether recap has sent
	//take a look at file: job/report_job.go @SendAllOutletReport()

	identifierId := fmt.Sprintf("recapalloutlet_%s_%s", adminId, time.Unix(dateSecond/1000, 0).Format("02-01-2006"))
	scheduledMessage := map[string]interface{}{
		"title":         "RECAP SHIFT REPORT",
		"message":       reportResult,
		"media":         "whatsapp",
		"receiver":      phone,
		"identifier_id": identifierId,
		"time_deliver":  time.Now().Unix() * 1000,
		"data_created":  time.Now().Unix() * 1000,
	}

	//set the sender
	socialConnect, err := db.Query(`SELECT sc.identity from admin_social_connect sc 
left join admin_social_connect_receipts scr on sc.id=scr.admin_social_connect_fkid 
where sc.type='wa' and sc.admin_fkid= ?
order by sc.id desc limit 1`, adminId)
	if !log.IfError(err) && len(socialConnect) > 0 {
		scheduledMessage["sent_via"] = utils.ToString(socialConnect["identity"])
	}

	err = google.PublishMessage(scheduledMessage, "messaging-gateway-production")

	//still insert to scheduled_message, for checking (take a look at file: job/report_job.go @SendAllOutletReport())
	//but if successfully sent to pubsub, remove unnecessary data (we care for identifier_id only)
	if err == nil {
		scheduledMessage["status"] = "sent"
		scheduledMessage["message"] = ""
	}
	_, err = db.Insert("scheduled_message", scheduledMessage)
	log.IfError(err)

	////send to others
	sql = `
select DISTINCT rra.address, rra.media
from report_recipient rr
         join report_recipient_type rrt on rr.report_recipient_id = rrt.report_recipient_fkid
         join report_recipient_outlet rro on rr.report_recipient_id = rro.report_recipient_fkid
         join report_recipient_address rra on rr.report_recipient_id = rra.report_recipient_fkid
where admin_fkid = ? and report_type_fkid='recap_outlet' and media='whatsapp' `
	receivers, err := db.QueryArray(sql, adminId)
	if log.IfError(err) {
		return
	}

	log.Info("total receivers report : %d", len(receivers))
	for _, receiver := range receivers {
		//_ = utils.SendWhatsAppMessage(reportResult, "secure", "", utils.Encrypt(utils.ToString(receiver["address"])))
		log.Info("recap all outlet '%s' send also to : %v", businessName, receiver["address"])
		//_ = utils.SendMessageToGateWay(reportResult, utils.ToString(receiver["address"]), 0)

		scheduledMessage = map[string]interface{}{
			"title":         "RECAP SHIFT REPORT",
			"message":       reportResult,
			"media":         "whatsapp",
			"identifier_id": identifierId,
			"receiver":      utils.ToString(receiver["address"]),
			"time_deliver":  time.Now().UnixMilli(),
			"data_created":  time.Now().UnixMilli(),
			"status":        "pending",
		}
		// err = google.PublishMessage(scheduledMessage, "messaging-gateway-production")
		// if err == nil {
		// 	//if not error, set status to sent so messaging-gateway will not send again
		// 	scheduledMessage["status"] = "sent"
		// 	scheduledMessage["message"] = ""
		// }
		if len(socialConnect) > 0 {
			scheduledMessage["sent_via"] = utils.ToString(socialConnect["identity"])
		}
		_, err = db.Insert("scheduled_message", scheduledMessage)
		log.IfError(err)
	}

	//save to mongodb
	// _, err = db.Mongo().Collection("close_shift_all").InsertOne(context.Background(), map[string]interface{}{
	// 	"created_at": utils.CurrentMillis(),
	// 	"admin_id":   cast.ToInt(adminId),
	// 	"report":     cashRecapReport,
	// })
	// log.IfError(err)
}
