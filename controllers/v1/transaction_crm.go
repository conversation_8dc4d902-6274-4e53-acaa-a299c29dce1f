package v1

import (
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
)

func getOrCreateOpenShift(adminId, outletId int) (int, error) {
	//get open shift id
	sql := `SELECT open_shift_id FROM open_shift
where outlet_fkid = ?
order by time_open desc
limit 1`
	openShift, err := db.Query(sql, outletId)
	if log.IfError(err) {
		return 0, err
	}

	if len(openShift) > 0 {
		return cast.ToInt(openShift["open_shift_id"]), nil
	}

	shiftId, err := getOrCreateShift(adminId, outletId)
	if log.IfError(err) {
		return 0, err
	}

	log.Info("add new openShift, shiftId: %v", shiftId)
	res, err := db.Insert("open_shift", map[string]interface{}{
		"outlet_fkid":   outletId,
		"shift_fkid":    shiftId,
		"time_open":     utils.CurrentMillis(),
		"data_created":  utils.CurrentMillis(),
		"data_modified": utils.CurrentMillis(),
	})
	if log.IfError(err) {
		return 0, err
	}
	id, err := res.LastInsertId()
	return int(id), err
}

func getOrCreateShift(adminId, outletId int) (int, error) {
	//check first shift available for the outlet
	sql := `SELECT s.shift_id, s.name, so.outlet_fkid from shift s
	left join shift_outlet so on so.shift_fkid=s.shift_id and so.outlet_fkid=?
	 where s.admin_fkid=? order by so.outlet_fkid desc, s.shift_id limit 1`

	shift, err := db.Query(sql, outletId, adminId)
	if log.IfError(err) {
		return 0, err
	}

	if len(shift) > 0 {
		return cast.ToInt(shift["shift_id"]), nil
	}

	//create new shift
	res, err := db.Insert("shift", map[string]interface{}{
		"name":          "Pagi",
		"admin_fkid":    adminId,
		"data_created":  utils.CurrentMillis(),
		"data_modified": utils.CurrentMillis(),
		"data_status":   "on",
	})
	if log.IfError(err) {
		return 0, err
	}

	id, err := res.LastInsertId()
	return int(id), err
}
