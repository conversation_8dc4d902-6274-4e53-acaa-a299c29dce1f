package v1

import (
	"bytes"
	"encoding/json"
	"fmt"
	"html/template"
	"os"
	"strings"
	templateText "text/template"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/encryption/aes256"
	"gitlab.com/uniqdev/backend/api-pos/core/format"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

// ReceiptService handles all receipt-related operations
type ReceiptService struct {
	timeOffset int64
	outlet     map[string]interface{}
	adminID    interface{}
	sale       models.Sale
}

// NewReceiptService creates a new instance of ReceiptService
func NewReceiptService(outletID int, sales models.Sale) (*ReceiptService, error) {
	outlet, err := db.Query("SELECT o.*, a.email FROM outlets o join admin a on o.admin_fkid = a.admin_id WHERE outlet_id = ?", outletID)
	if err != nil {
		return nil, fmt.Errorf("failed to get outlet data: %w", err)
	}

	return &ReceiptService{
		timeOffset: 25200,
		outlet:     outlet,
		adminID:    outlet["admin_fkid"],
		sale:       sales,
	}, nil
}

// SendReceipt processes and sends a receipt for a sale
func SendReceipt(sale models.Sale) error {
	if !shouldSendReceipt(sale) {
		log.Info("receipt transaction with id '%s' / '%s' will not send to customer. Because receipt receiver is empty: (%s|%s)", sale.NoNota, sale.DisplayNota, sale.ReceiptReceiver, sale.MemberDetail.Phone)
		return nil
	}

	service, err := NewReceiptService(sale.OutletID, sale)
	if err != nil {
		log.Info("failed to create receipt service: %v", err)
		return err
	}

	return service.ProcessAndSendReceipt(sale)
}

func shouldSendReceipt(sale models.Sale) bool {
	if sale.ReceiptReceiver == "" {
		sale.ReceiptReceiver = sale.MemberDetail.Phone
	}
	return sale.ReceiptReceiver != ""
}

func (s *ReceiptService) ProcessAndSendReceipt(sale models.Sale) error {
	items, qtyTotal := s.processOrderItems(sale.OrderList)
	if qtyTotal <= 0 {
		log.Info("receipt transaction with id '%s' / '%s' will not send to customer. Because qty is %d",
			sale.NoNota, sale.DisplayNota, qtyTotal)
		return nil
	}

	receiptData := s.prepareReceiptData(sale, items)
	return s.sendReceiptToCustomer(sale, receiptData)
}

func (s *ReceiptService) processOrderItems(orderList []models.Order) ([]models.Items, int) {
	items := make([]models.Items, 0)
	qtyTotal := 0
	promoMap := make(map[int]map[string]interface{})

	for _, order := range orderList {
		subItems := s.processDiscountsAndExtras(order)
		promotions := s.getPromotions(order, promoMap)

		prefix := ""
		if order.IsItemVoid {
			prefix = "VOID: "
			qtyTotal -= order.Qty
		} else {
			qtyTotal += order.Qty
		}

		items = append(items, models.Items{
			Product:    prefix + order.Product.Name,
			Qty:        utils.CurrencyFormat(order.Qty),
			SubTotal:   utils.CurrencyFormat(order.SubTotal),
			Price:      format.Currency(order.Product.PriceSell),
			SubItems:   subItems,
			Promotions: promotions,
		})
	}

	return items, qtyTotal
}

func (s *ReceiptService) processDiscountsAndExtras(order models.Order) []models.Items {
	subItems := make([]models.Items, 0)

	// Process discounts
	if order.Discount.Discount > 0 {
		percentage := s.formatDiscountPercentage(order.Discount.Discount, order.Discount.DiscountType)
		subItems = append(subItems, models.Items{
			Product:  "#Discount " + percentage,
			SubTotal: "-" + utils.CurrencyFormat(order.Discount.DiscountNominal),
		})
	}

	// Process vouchers
	if order.Discount.Voucher > 0 {
		percentage := s.formatDiscountPercentage(order.Discount.Voucher, order.Discount.VoucherType)
		subItems = append(subItems, models.Items{
			Product:  "#Voucher " + percentage,
			SubTotal: "-" + utils.CurrencyFormat(order.Discount.VoucherNominal),
		})
	}

	// Process extras
	for _, extra := range order.Extra {
		subItems = append(subItems, models.Items{
			Product:  extra.Product.Name,
			Qty:      utils.CurrencyFormat(extra.Qty),
			Price:    format.Currency(extra.Product.PriceSell),
			SubTotal: utils.CurrencyFormat(extra.SubTotal),
		})
	}

	return subItems
}

func (s *ReceiptService) formatDiscountPercentage(value int, discountType string) string {
	if discountType == "percentage" {
		return fmt.Sprintf("(%d%%)", value)
	}
	return ""
}

func (s *ReceiptService) prepareReceiptData(sale models.Sale, items []models.Items) models.Receipt {
	payments := s.processPayments(sale.Payments)
	taxAndDisc := s.processTaxAndDiscounts(sale)
	feedbackUrl := s.generateFeedbackUrl(sale)

	data := models.Receipt{
		ReceiptNo:   sale.DisplayNota,
		Date:        time.Unix((sale.TimeCreated/1000)+s.timeOffset, 0).Format("02-01-2006 03:04"),
		Cashier:     sale.EmployeeName,
		Outlet:      sale.OutletName,
		OutletInfo:  s.formatOutletInfo(),
		OutletLogo:  utils.ToString(s.outlet["receipt_logo"]),
		Items:       items,
		GrandTotal:  utils.CurrencyFormat(sale.GrandTotal),
		TaxAndDisc:  taxAndDisc,
		Payments:    payments.PaymentMedia,
		Return:      utils.CurrencyFormat(payments.TotalPay - sale.GrandTotal),
		FeedbackUrl: feedbackUrl,
		Customer:    sale.Customer,
		Payment:     sale.Payment,
		Table:       sale.Table,
		SalesTag:    sale.SalesTag.Name,
	}

	if sale.MemberId != "" {
		s.enrichReceiptWithMemberData(&data, sale)
	}

	return data
}

func (s *ReceiptService) sendReceiptToCustomer(sale models.Sale, receiptData models.Receipt) error {
	receiverType := "email"
	if utils.IsNumber(sale.ReceiptReceiver) {
		receiverType = "whatsapp"
	}

	message, attachments, messageDetail := s.generateReceiptContent(receiptData, receiverType)

	scheduledMessage := s.createScheduledMessage(sale, message, receiverType, attachments, messageDetail)

	_, err := db.Insert("scheduled_message", scheduledMessage)
	log.IfError(err)
	return err
}

// PaymentResult contains processed payment information
type PaymentResult struct {
	PaymentMedia []models.PaymentMedia
	TotalPay     int
}

func (s *ReceiptService) processPayments(payments []models.Payment) PaymentResult {
	result := PaymentResult{
		PaymentMedia: make([]models.PaymentMedia, 0),
	}

	for _, pay := range payments {
		result.PaymentMedia = append(result.PaymentMedia, models.PaymentMedia{
			Method: pay.Method,
			Total:  utils.CurrencyFormat(pay.Pay),
		})
		result.TotalPay += pay.Pay
	}

	return result
}

func (s *ReceiptService) processTaxAndDiscounts(sale models.Sale) []models.TaxAndDisc {
	taxAndDisc := make([]models.TaxAndDisc, 0)

	// Process main discounts
	if sale.Discount.Discount > 0 {
		taxAndDisc = append(taxAndDisc, models.TaxAndDisc{
			Name:  "Discount",
			Total: utils.CurrencyFormat(sale.Discount.DiscountNominal),
		})
	}

	if sale.Discount.Voucher > 0 {
		taxAndDisc = append(taxAndDisc, models.TaxAndDisc{
			Name:  "Voucher",
			Total: utils.CurrencyFormat(sale.Discount.VoucherNominal),
		})
	}

	// Process promotions
	voucherCodes := make([]string, 0)
	for _, promo := range sale.Promotions {
		if promo.PromoNominal > 0 {
			if promo.Name == "" {
				data, err := db.Query("select name from promotions where promotion_id = ?", promo.PromotionId)
				if !log.IfError(err) {
					promo.Name = utils.ToString(data["name"])
				}
			}
			taxAndDisc = append(taxAndDisc, models.TaxAndDisc{
				Name:  promo.Name,
				Total: utils.CurrencyFormat(promo.PromoNominal),
			})
		}
		if promo.Code != "" {
			voucherCodes = append(voucherCodes, strings.ToUpper(promo.Code))
		}
	}

	// Process taxes
	for _, tax := range sale.Taxes {
		taxAndDisc = append(taxAndDisc, models.TaxAndDisc{
			Name:  tax.Name,
			Total: utils.CurrencyFormat(tax.Total),
		})
	}

	return taxAndDisc
}

func (s *ReceiptService) getPromotions(order models.Order, promoMap map[int]map[string]interface{}) []models.Promotion {
	promotions := make([]models.Promotion, 0)
	if order.Promotion.PromotionId > 0 || order.Promotion.PromotionTypeFkid > 0 {
		if promoMap[order.Promotion.PromotionId] == nil {
			if order.Promotion.PromotionId == 0 {
				order.Promotion.PromotionId = getPromotionId(s.sale.Promotions[0], s.adminID)
			}

			promo, err := db.Query("select name from promotions where promotion_id = ?", order.Promotion.PromotionId)
			if !log.IfError(err) {
				promoMap[order.Promotion.PromotionId] = promo
			}
		}
		promotions = append(promotions, models.Promotion{
			Name: utils.ToString(promoMap[order.Promotion.PromotionId]["name"]),
		})
	}
	return promotions
}

func (s *ReceiptService) formatOutletInfo() string {
	address := utils.OneOfThese(s.outlet["receipt_address"], s.outlet["address"], "-")
	phone := utils.OneOfThese(s.outlet["receipt_phone"], s.outlet["phone"], "-")

	outletInfo := fmt.Sprintf("%s", address)
	if utils.ToString(phone) != "" {
		outletInfo += fmt.Sprintf("\n%s", phone)
	}
	return outletInfo
}

func (s *ReceiptService) generateFeedbackUrl(sale models.Sale) string {
	salesIdEnc := aes256.Encrypt(sale.NoNota, utils.KEY_FEEDBACK)
	feedbackUrl := fmt.Sprintf("%s/feedback?data=%s", utils.BaseUrl(), salesIdEnc)

	appConf := getAppCrmConfig(utils.ToInt(s.adminID))
	if sale.MemberId != "" {
		feedbackUrl = generateDynamicLink(appConf, sale.NoNota, feedbackUrl)
	} else {
		shortFeedbackUrl := utils.ShortUrl(utils.ShortUrlModel{
			LongUrl: feedbackUrl,
			Title:   "Feedback " + sale.DisplayNota,
			Tags:    []string{"receipt"},
		})
		if shortFeedbackUrl != "" {
			feedbackUrl = shortFeedbackUrl
		}
	}

	return feedbackUrl
}

func (s *ReceiptService) enrichReceiptWithMemberData(data *models.Receipt, sale models.Sale) {
	point, err := db.Query("select point_earned from sales where sales_id = ?", sale.NoNota)
	log.Info(">>> point: %v", cast.ToJson(point))
	if !log.IfError(err) && len(point) > 0 {
		data.PointName = "Point"
		data.PointTotal = cast.ToInt(point["point_earned"])

		// Get custom point name
		var crmAppConfig models.CrmAppConfig
		crmApp, err := db.Query("select app_config from crm_app where admin_fkid = ?", s.adminID)
		if !log.IfError(err) && len(crmApp) > 0 {
			err = json.Unmarshal([]byte(cast.ToString(crmApp["app_config"])), &crmAppConfig)
			if !log.IfError(err) && crmAppConfig.Language.Point != "" {
				data.PointName = crmAppConfig.Language.Point
			}
		}
	}
}

func (s *ReceiptService) generateReceiptContent(data models.Receipt, receiverType string) (string, []string, string) {
	var result bytes.Buffer
	attachments := make([]string, 0)
	messageDetail := ""

	tmplReceipt, err := template.ParseFiles("config/template/receipt_wa_snippet.html")
	if log.IfError(err) {
		return "", nil, ""
	}
	if log.IfError(tmplReceipt.Execute(&result, data)) {
		return "", nil, ""
	}

	message := result.String()

	if receiverType == "whatsapp" {
		message, attachments, messageDetail = s.generateWhatsAppContent(data, result.String())
	}

	return message, attachments, messageDetail
}

func (s *ReceiptService) generateWhatsAppContent(data models.Receipt, htmlContent string) (string, []string, string) {
	css, err := utils.ReadFile("config/template/receipt_wa.css")
	if log.IfError(err) {
		return s.fallbackToTextTemplate(data)
	}

	lng := cast.ToString(s.outlet["receipt_language"])
	if lng == "" {
		lng = "id"
	}

	receiptLang := receiptTranslation(lng)
	receiptGenerated := utils.ReplaceWithMap(htmlContent, "$", receiptLang)

	url, err := utils.HtmlToImage(receiptGenerated, css)
	if log.IfError(err) || url == "" {
		return s.fallbackToTextTemplate(data)
	}

	attachments := []string{url}
	messageDetail := ""

	receiptImgUrl := s.processReceiptImage(url)
	if receiptImgUrl != "" {
		attachments = []string{receiptImgUrl}
		messageDetail = utils.SimplyToJson(models.MessageContentDetail{
			Type: "receipt",
			Attributes: models.ReceiptAttribute{
				ImageUrl:    receiptImgUrl,
				FeedbackUrl: data.FeedbackUrl,
				Language:    lng,
			},
		})
	}

	log.Info(">>> data: %v", cast.ToJson(data))
	message := s.formatWhatsAppMessage(lng, data.FeedbackUrl, data)
	return message, attachments, messageDetail
}

func (s *ReceiptService) fallbackToTextTemplate(data models.Receipt) (string, []string, string) {
	var result bytes.Buffer
	tmplReceipt, err := templateText.ParseFiles("config/template/receipt_template_wa.tmpl")
	if log.IfError(err) {
		return "", nil, ""
	}

	if log.IfError(tmplReceipt.Execute(&result, data)) {
		return "", nil, ""
	}

	return result.String(), nil, ""
}

func (s *ReceiptService) processReceiptImage(url string) string {
	if strings.HasPrefix(url, "https://") {
		return url
	}

	receiptImgUrl, err := utils.NewImageUploader(models.ImageUploadRequest{
		SourceType: models.ImageSourceBase64,
		Source:     url,
	}).Upload()
	if err != nil {
		log.Info("upload image error: %v", err)
		return ""
	}

	return receiptImgUrl
}

func (s *ReceiptService) formatWhatsAppMessage(language string, feedbackUrl string, data models.Receipt) string {
	//if user has point collection (for feedback), inform that to user
	var pointCollection models.PointCollectionEntity
	if s.sale.MemberId != "" {
		pointCollection = getPointCollectionFeedback(cast.ToInt(s.adminID), cast.ToInt(s.sale.MemberId))
	}

	msgBody := ReceiptPointMessage(language, data.PointName, pointCollection.Point)

	if language == "en" {
		return fmt.Sprintf("*YOUR E-RECEIPT*\n\n %s \n\nClick: %s", msgBody, feedbackUrl)
	}

	return fmt.Sprintf("*NOTA PEMBELIAN*\n\n%s \n\n_jika link tidak dapat di klik, silahkan balas *YA* pada pesan ini_\n\nklik: %s", msgBody, feedbackUrl)
}

func (s *ReceiptService) createScheduledMessage(sale models.Sale, message, receiverType string, attachments []string, messageDetail string) map[string]interface{} {
	adminID := s.determineAdminID()
	sender := s.determineSender()

	scheduledMessage := map[string]interface{}{
		"title":         "E-Receipt",
		"message":       message,
		"media":         receiverType,
		"time_deliver":  time.Now().Unix() * 1000,
		"data_created":  time.Now().Unix() * 1000,
		"receiver":      sale.ReceiptReceiver,
		"admin_fkid":    adminID,
		"sent_via":      sender,
		"identifier_id": sale.NoNota,
	}

	if len(attachments) > 0 {
		scheduledMessage["attachments"] = utils.SimplyToJson(attachments)
	}
	if messageDetail != "" {
		scheduledMessage["notification_detail"] = messageDetail
	}

	// log.Info(">>>>> scheduled message: %v", scheduledMessage)
	return scheduledMessage
}

func (s *ReceiptService) determineAdminID() interface{} {
	if useCustomNumber() {
		sosConn, err := db.Query("select id from admin_social_connect where admin_fkid = ? and type = 'wa' and is_active = 1", s.adminID)
		if !log.IfError(err) && len(sosConn) == 0 {
			adminIdSenders := map[string]int{
				"production": 143,
				"staging":    17,
			}
			if sender, ok := adminIdSenders[os.Getenv("ENV")]; ok {
				return sender
			}
		}
	}
	return s.adminID
}

func (s *ReceiptService) determineSender() string {
	//check receipt setting
	// s.sale.OutletID
	sql := `SELECT sc.identity from admin_social_connect sc 
left join admin_social_connect_receipts scr on sc.id=scr.admin_social_connect_fkid 
where sc.type='wa' and sc.receipt != 'off' and sc.admin_fkid= ?
and ((sc.receipt = 'partial' and scr.outlet_fkid=?) or sc.receipt='all')`
	socialConnect, err := db.Query(sql, s.adminID, s.sale.OutletID)
	if !log.IfError(err) && len(socialConnect) > 0 {
		return utils.ToString(socialConnect["identity"])
	}

	if !useCustomNumber() {
		return ""
	}

	//for temporary [send using other adminId]
	adminIdSenders := map[string]int{
		"production": 143,
		"staging":    17,
	}
	if _, ok := adminIdSenders[os.Getenv("ENV")]; !ok {
		return ""
	}

	sql = `select identity from admin_social_connect where admin_fkid = ? and type = 'wa' and is_active = 1`
	socialConnect, err = db.Query(sql, adminIdSenders[os.Getenv("ENV")])
	if !log.IfError(err) && len(socialConnect) > 0 {
		return utils.ToString(socialConnect["identity"])
	}

	return ""
}

func AskResendReceipt(receiver string) {
	log.Info("Requesting receipt resend for receiver: %s", receiver)
	receiver = normalizePhoneNumber(receiver)

	// Create variations of the phone number
	variations := getPhoneNumberVariations(receiver)

	//check if system has sent the message
	sql := `SELECT id from scheduled_message 
	where time_deliver >  UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 24 HOUR))*1000 
	and (receiver = ? OR receiver = ? OR receiver = ?) 
	order by id desc limit 1`

	result, err := db.Query(sql, variations[0], variations[1], variations[2])
	if log.IfError(err) {
		return
	}
	if len(result) == 0 {
		log.Info("No scheduled message found for receiver: %s", receiver)
		return
	}

	log.Info("Resending receipt for scheduledId: %v", result["id"])
	_, err = db.Update("scheduled_message", map[string]interface{}{
		"status": "pending",
	}, "id=?", result["id"])
	if log.IfError(err) {
		log.Error("Failed to update scheduled message status for receiver: %s", receiver)
		return
	}
	log.Info("Notification sent to receiver: %s", receiver)
}

func normalizePhoneNumber(phone string) string {
	// Remove any non-digit characters
	phone = strings.Map(func(r rune) rune {
		if r >= '0' && r <= '9' {
			return r
		}
		return -1
	}, phone)

	// Remove leading zeros
	phone = strings.TrimLeft(phone, "0")

	// Remove leading 62 if exists
	if strings.HasPrefix(phone, "62") {
		phone = phone[2:]
	}

	return phone
}

func getPhoneNumberVariations(normalized string) []string {
	return []string{
		"0" + normalized,  // Format: 0857...
		"62" + normalized, // Format: 62857...
		normalized,        // Format: 857...
	}
}
