package v1

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"go.mongodb.org/mongo-driver/bson"
)

var muNote sync.Mutex //to prevent overload database

func fetchSalesNote(adminId int) ([]models.ProductNotes, error) {
	maxData := 500

	// this query is very slow...
	sql := `select (note) as note, count(*) as total, product_fkid
from sales_detail sd
		join products p on sd.product_fkid = p.product_id
where sd.time_created > ? 
 and p.admin_fkid = ?
 and sd.note is not null
group by note, product_fkid
order by total desc, product_fkid `

	timeStart := time.Now().AddDate(0, -1, 0).Unix() * 1000
	notes, err := db.QueryArray(sql, timeStart, adminId)
	if log.IfError(err) {
		return nil, err
	}

	result := make([]models.ProductNotes, 0)
	if len(notes) == 0 {
		return result, nil
	}

	notesArray := make([]string, 0)
	for i, note := range notes {
		txtNote := strings.TrimSpace(utils.ToString(note["note"]))
		if len(txtNote) <= 2 {
			continue
		}
		notesArray = append(notesArray, txtNote)
		if i == len(notes)-1 || note["product_fkid"] != notes[i+1]["product_fkid"] {
			result = append(result, models.ProductNotes{
				ProductID: cast.ToInt(note["product_fkid"]),
				Notes:     notesArray,
			})
			notesArray = make([]string, 0)
		}
	}

	log.Info("total notes: %d, adminId: %v", len(result), adminId)
	if len(result) > maxData {
		result = result[:maxData]
	}
	return result, nil
}

func fetchSalesNoteCached(adminId int) ([]models.ProductNotes, error) {
	maxData := 500

	var notesCache models.SalesNote
	filter := bson.D{{Key: "admin_id", Value: adminId}}
	client := db.MongoDbClient()
	if client == nil {
		return nil, fmt.Errorf("mongodb client not initialized")
	}
	mongoDb := client.Database("products")
	err := mongoDb.Collection("notes").FindOne(context.Background(), filter).Decode(&notesCache)
	if err == nil && notesCache.Data != "" {
		var productNotes []models.ProductNotes
		err = json.Unmarshal([]byte(notesCache.Data), &productNotes)
		if !log.IfError(err) && len(productNotes) > 0 {
			if len(productNotes) > maxData {
				log.Info("total notes is too high: %v", len(productNotes))
				productNotes = productNotes[:maxData]
			}
		}

		minStart := time.Now().AddDate(0, 0, -7).Unix() * 1000
		//refresh cache if exceeding certain days
		if notesCache.CreatedAt < minStart {
			log.Info("cache already expired for %v, cratedAt: %v | minStart: %v ", adminId, notesCache.CreatedAt, minStart)
			go createNewSalesCache(adminId)
		}
		log.Info("notes from cache: %v", len(productNotes))
		return productNotes, nil
	} else {
		log.Info("no data from mongodb..., err: %v", err)
		go createNewSalesCache(adminId)
	}
	return nil, err
}

func cacheSalesNote(adminId, result interface{}) {
	if result == nil {
		return
	}
	dataJson, err := json.Marshal(result)
	log.IfError(err)

	client := db.MongoDbClient()
	if client == nil {
		fmt.Println("mongo client not initialzed..")
		return
	}
	mongoDb := client.Database("products")
	_, err = mongoDb.Collection("notes").DeleteOne(context.Background(), bson.D{{Key: "admin_id", Value: adminId}})
	fmt.Println(err)

	res, err := mongoDb.Collection("notes").InsertOne(context.Background(), map[string]interface{}{
		"created_at": time.Now().Unix() * 1000,
		"admin_id":   adminId,
		"data":       string(dataJson),
	})
	if !log.IfError(err) {
		fmt.Printf("notes inserted with id: %v \n", res.InsertedID)
	}
}

func createNewSalesCache(adminId int) error {
	//skipp in rush hour
	if time.Now().Hour() < 21 {
		log.Info("skip createNewSalesCache %v as it in rush hour (%v)", adminId, time.Now().Hour())
		return nil
	}

	muNote.Lock()
	defer muNote.Unlock()

	//first let check it cache expired already
	var notesCache models.SalesNote
	filter := bson.D{{Key: "admin_id", Value: adminId}}
	client := db.MongoDbClient()
	if client == nil {
		return fmt.Errorf("mongodb client not initialized")
	}
	mongoDb := client.Database("products")
	err := mongoDb.Collection("notes").FindOne(context.Background(), filter).Decode(&notesCache)
	if err == nil && notesCache.Data != "" {
		minStart := time.Now().AddDate(0, 0, -7).Unix() * 1000
		if notesCache.CreatedAt > minStart {
			log.Info("cache for %v not expired yet, createdAt: %v", adminId, notesCache.CreatedAt)
			return nil
		}
	}

	log.Info("createNewSalesCache: %v", adminId)
	newSalesNote, err := fetchSalesNote(adminId)
	if err == nil && len(newSalesNote) > 0 {
		cacheSalesNote(adminId, newSalesNote)
	}

	return nil
}
