package v1

import (
	"context"
	"encoding/json"
	"firebase.google.com/go"
	"fmt"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"google.golang.org/api/option"
	"strings"
)
func RegisterCustomer(ctx *fasthttp.RequestCtx) {
	//6DAQdJKtgr5PaRzcd9CJmUkNzZTzpyxCNRpgft5ygwdUufNTfGSwfcxbJ
	auth := ctx.Request.Header.Peek("Authorization")
	if string(auth) != "6DAQdJKtgr5PaRzcd9CJmUkNzZTzpyxCNRpgft5ygwdUufNTfGSwfcxbJ" {
		ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		return
	}

	name := string(ctx.PostArgs().Peek("name"))
	email := string(ctx.PostArgs().Peek("email"))
	//phone := string(ctx.PostArgs().Peek("phone"))
	company := string(ctx.PostArgs().Peek("company"))
	campaign := string(ctx.PostArgs().Peek("campaign"))
	address := string(ctx.PostArgs().Peek("address"))
	idToken := string(ctx.PostArgs().Peek("id_token"))

	if status, msg := utils.IsValidPostInput(ctx, "name","email","company","address","id_token"); !status {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: msg})
		return
	}

	ctxBack := context.Background()
	opt := option.WithCredentialsFile("config/credential/firebase-cred.json")
	app, err := firebase.NewApp(ctxBack, nil, opt)
	if log.IfErrorSetStatus(ctx, err){
		return
	}

	client, err := app.Auth(ctxBack)
	if log.IfErrorSetStatus(ctx, err){
		return
	}

	token, err := client.VerifyIDToken(ctxBack, string(idToken))
	if log.IfErrorSetStatus(ctx, err){
		return
	}

	if token.Claims["phone_number"] == nil {
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "backend fail to recognize token. it doesn't contain required field"})
		return
	}

	phone := utils.ToString(token.Claims["phone_number"])
	if strings.HasPrefix(phone, "+") {
		phone = phone[1:]
	}

	log := fmt.Sprintf("Name : %s\nEmail : %s\nPhone : %s\nCompany : %s\nCampaign : %s\nAddress : %s", name, email, phone, company, campaign, address)

	if len(phone) < 11 {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		utils.SendMessageToSlack("someone request quotation with invalid phone number : \n" + log)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "invalid phone number"})
		return
	}
	if !(strings.HasPrefix(phone, "08") || strings.HasPrefix(phone, "62")) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		utils.SendMessageToSlack("someone request quotation with invalid phone number format : \n" + log)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: false, Message: "invalid phone number format!"})
		return
	}

	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Status: true, Message: "success"})
}
