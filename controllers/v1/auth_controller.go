package v1

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

func Token(ctx *fasthttp.RequestCtx) {
	username := ctx.FormValue("admin_id")
	password := ctx.FormValue("admin_secret")
	deviceId := ctx.FormValue("device")

	//log.Info("Request token with id : %s", deviceId)

	token := ctx.Request.Header.Peek("Authorization")
	if string(token) != os.Getenv("token") {
		ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		return
	}

	userType := "admin"
	res, err := db.Query("SELECT password, admin_id FROM admin WHERE email = ? ", string(username))
	utils.CheckErr(err)

	role := utils.ROLE_OWNER
	insurer := ""
	var passwordHash, adminId, employeeId string
	if len(res) <= 0 {
		data, err := db.Query("SELECT password, employee_id, admin_fkid FROM employee WHERE email = ? ", username)
		utils.CheckErr(err)

		if len(data) <= 0 {
			ctx.SetStatusCode(http.StatusUnauthorized)
			return
		}

		passwordHash = data["password"].(string)
		insurer = utils.Encrypt(utils.ToString(data["employee_id"]))
		employeeId = utils.ToString(data["employee_id"])
		adminId = utils.ToString(data["admin_fkid"])
		userType = "employee" //should match with table name
	} else {
		passwordHash = res["password"].(string)
		adminId = strconv.FormatInt(res["admin_id"].(int64), 10)
	}

	//if !auth.VerifyPassword(string(password), passwordHash){
	if !utils.CheckPasswordHash(string(password), passwordHash) {
		fmt.Println("Password didn't match!")
		ctx.SetStatusCode(http.StatusUnauthorized)
		return
	}

	var data []map[string]interface{}
	var outlets = make([]string, 0)

	if userType == "admin" {
		data, err = db.QueryArray("select outlet_id from outlets where admin_fkid = ?", adminId)
		log.IfError(err)
	} else {
		data, err = db.QueryArray("select outlet_fkid as outlet_id from employee_outlet where employee_fkid = ?", employeeId)
		log.IfError(err)
		role = utils.ROLE_EMPLOYEE
	}

	for _, outlet := range data {
		outlets = append(outlets, utils.ToString(outlet["outlet_id"]))
	}

	uniqId := string(deviceId)
	if uniqId == "" {
		uniqId = adminId
	}

	//Generate Token
	auth := auth.InitJWTAuth()
	var adminIdEnc string
	localDb := db.GetDbJson()
	err = localDb.Read("enc_id", uniqId, &adminIdEnc)
	if err != nil {
		adminIdEnc = utils.Encrypt(adminId)
		err = localDb.Write("enc_id", uniqId, adminIdEnc)
		log.IfError(err)
	}
	authToken := auth.GenerateToken(adminIdEnc, insurer, role, strings.Join(outlets, ","))

	ctx.SetContentType("application/json")
	ctx.SetStatusCode(http.StatusOK)
	json.NewEncoder(ctx).Encode(authToken)
}

func LoginAdmin(ctx *fasthttp.RequestCtx) {
	response := models.Response{Message: "Username or password invalid!"}

	email := ctx.FormValue("email")
	password := ctx.FormValue("password")

	data, err := db.Query("SELECT * FROM admin WHERE email=?", email)
	log.IfError(err)

	status := "failed"
	if len(data) > 0 {
		if utils.CheckPasswordHash(string(password), data["password"].(string)) {
			delete(data, "password")
			response.Status = true
			response.Data = data
			response.Message = "Login success"
			status = "success"
		}
		//if auth.VerifyPassword(string(password), data["password"].(string)){}

		//write log login db
		go func() {
			_, err = db.GetDb().Exec("INSERT INTO log_login (date_time, ip_address, user_id, type, status) "+
				"VALUES (?,?,?,?,?)", time.Now().Unix()*1000, utils.GetIPAdress(ctx), data["admin_id"], "admin", status)
			log.IfError(err)

			//update last login
			_, err = db.UpdateDb("admin", map[string]interface{}{"last_login": time.Now().Unix() * 1000}, map[string]interface{}{"admin_id": data["admin_id"]})
			log.IfError(err)
		}()
	} else {
		data, err := db.Query("SELECT * FROM employee WHERE email=?", email)
		log.IfError(err)
		if len(data) > 0 {
			//if data["password"] != nil && auth.VerifyPassword(string(password), data["password"].(string)){
			if data["password"] != nil && utils.CheckPasswordHash(string(password), data["password"].(string)) {
				roleMobile := models.RoleMobile{}
				err := json.Unmarshal([]byte(data["role_mobile"].(string)), &roleMobile)
				log.IfError(err)

				if roleMobile.MasterLogin {
					delete(data, "password")
					delete(data, "role")
					data["admin_id"] = data["admin_fkid"]
					response.Status = true
					response.Data = data
					response.Message = "Login success"
					status = "success"
				} else {
					response.Message = "Mobile app login access denied"
				}
			}

			//write log login db
			go func() {
				_, err = db.GetDb().Exec("INSERT INTO log_login (date_time, ip_address, user_id, type, status) "+
					"VALUES (?,?,?,?,?)", time.Now().Unix()*1000, utils.GetIPAdress(ctx), data["employee_id"], "employee", status)
				utils.CheckErr(err)
			}()
		}
	}

	ctx.SetContentType("application/json")
	log.Info("login admin, email: %s - response: %s", email, utils.SimplyToJson(response))
	json.NewEncoder(ctx).Encode(response)
}

func LoginEmployee(ctx *fasthttp.RequestCtx) {
	employeeId := ctx.FormValue("employee_id")
	pin := ctx.FormValue("pin")
	outletId := ctx.FormValue("outlet_id")
	deviceId := ctx.FormValue("device_id")
	deviceName := ctx.FormValue("device_name")
	version := ctx.FormValue("version")
	firebaseToken := ctx.FormValue("firebase_token")
	deviceInfo := ctx.FormValue("device_info")
	adminId := ctx.Request.Header.Peek("admin_id")

	versionMajor, versionMinor, versionPatch := extractVersion(string(version))
	log.Info("app version: %d.%d.%d", versionMajor, versionMinor, versionPatch)

	if !auth.ValidateOutletId(ctx, utils.ByteToInt(outletId)) {
		log.Info("current authentication has no access to login at outlet: %s - header: %s - employeeId: %s", string(outletId), string(ctx.Request.Header.Peek("outlet")), string(employeeId))
		return
	}

	response := models.Response{Millis: time.Now().Unix() * 1000}
	responseData := make(map[string]interface{}, 2)
	sql := `SELECT * FROM employee WHERE employee_id=? 
	AND data_status='on' AND access_status_mobile='activated' AND (access_mode='mobile' OR access_mode='all') `
	employee, err := db.Query(sql, employeeId)
	log.IfError(err)

	log.Info("login employee: %s - at outlet: %s (%s) - with device: %s", string(employeeId), string(outletId), string(adminId), string(deviceId))
	if len(employee) > 0 {
		if utils.ToString(employee["pin"]) == string(pin) {
			//validate outlet access
			sql = `SELECT * from employee_outlet where outlet_fkid=? and employee_fkid=?`
			employeeOutlet, err := db.Query(sql, outletId, employee["employee_id"])
			log.IfError(err)
			if len(employeeOutlet) == 0 {
				response.Status = false
				response.Message = "you have no access to this outlet"
			} else {
				response.Status = true
				response.Message = "Login Success!"
				delete(employee, "password")
			}

			//check billing
			if utils.IsBillingEnable() {
				subscribe, err := getSubscription(string(adminId), string(deviceId))
				if err == nil {
					subsType := "Berlangganan"
					if subscribe.Type == "trial" {
						subsType = "Uji Coba Gratis"
					}

					log.Info("AdminID : %s - Subs Msg : %v >> subscribe detail : %v", adminId, response.Message, utils.SimplyToJson(subscribe))
					if !subscribe.IsSlotAvailable {
						response.Status = false
						response.Message = fmt.Sprintf("Maaf, saat ini anda hanya berlangganan %d slot device, dan anda hanya dapat login di %d device secara bersamaan. \n"+
							"Silahkan lakukan pembelian slot device sesuai kebutuhan anda,\n"+
							"\natau Logout device yang telah Login.\n\n"+
							"Daftar device yang sedang login: \n%s", subscribe.TotalSlot, subscribe.TotalSlot, strings.Join(subscribe.DeviceLoginNames, "\n"))
						response.Code = 100
					} else if subscribe.DaysLeft <= 0 {
						if subscribe.TimeMillisExpired > (time.Now().Unix() * 1000) {
							response.Code = 3
							response.Message = fmt.Sprintf("Masa %s anda akan berakhir hari ini. Pastikan anda segera memperpanjang langganan anda", subsType)
						} else {
							response.Code = 101
							response.Status = false
							response.Message = fmt.Sprintf("Maaf, masa %s anda telah berakhir. Silahkan lakukan pembayaran untuk terus dapat menggunakan UNIQ", subsType)
						}
					} else if subscribe.DaysLeft <= 7 {
						response.Code = 3
						response.Message = fmt.Sprintf("Masa %s anda akan berakhir dalam waktu %v hari", subsType, subscribe.DaysLeft)
						if subscribe.DaysLeft <= 3 {
							response.Message = fmt.Sprintf("%s\nSetelah masa %s berakhir, akun ini tidak dapat lagi digunakan. Silahkan perpanjang masa berlangganan anda melalui website back-office UNIQ", response.Message, subsType)
						}

						log.Info("version: %s", string(version))
						//if user using old version, set login to fail at first temp
						if subscribe.DaysLeft <= 7 && versionMajor <= 1 && versionMinor <= 1 && versionPatch < 86 {
							db := db.GetDbJson()
							var sent bool
							dateNow := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02")
							dbKey := fmt.Sprintf("%v#%v", employeeId, dateNow)
							if err := db.Read("notif", dbKey, &sent); err != nil {
								log.Error("Read error - %v", err)
							}

							if !sent {
								response.Status = false
								response.Message = response.Message + "\n\nSilahkan tutup pesan dialog ini dan coba login kembali.\nUpdate aplikasi anda agar pesan dialog ini tidak tampil lagi"
								if err := db.Write("notif", dbKey, true); err != nil {
									log.Error("Write error - %v", err)
								}
							}
						}
					}
				}

				log.Info("AdminID : %s - Subs Msg : %v >> subscribe detail : %v", adminId, response.Message, utils.SimplyToJson(subscribe))

				if !response.Status {
					go func(adminId, outletId []byte, msg string) {
						sql := "select o.name, a.business_name, a.name from outlets o join admin a on a.admin_id = o.admin_fkid where o.outlet_id= ? and o.admin_fkid = ? "
						data, err := db.Query(sql, outletId, adminId)
						log.IfError(err)
						log.Error("<SUBSCRIPTION WARNING> \n %s (%s) \nMessage: %s ", data["business_name"], data["name"], msg)
					}(adminId, outletId, response.Message)

					//check subscription freeze
					if os.Getenv("server") == "production" {
						go func(adminId string) {
							sql := `
select ssf.*,
       datediff(from_unixtime(ssf.service_time_expired / 1000), from_unixtime(ssf.time_freeze_start / 1000)) days,
		ss.id
from system_subscribe_freeze ssf
         join system_subscribe ss on ssf.system_subscribe_fkid = ss.id
    and ssf.time_freeze_start = ss.service_time_expired
where admin_fkid = ?
limit 1 `
							freeze, err := db.Query(sql, adminId)
							log.IfError(err)
							if len(freeze) > 0 {
								daysRemain := utils.ToInt(freeze["days"])
								timeExpanded := time.Now().AddDate(0, 0, daysRemain).Unix() * 1000
								_, err = db.Update("system_subscribe", map[string]interface{}{
									"service_time_expired": timeExpanded,
								}, "id = ?", freeze["id"])
								if !log.IfError(err) {
									log.IfError(errors.New(fmt.Sprintf("[UNFREEZE SUBSCRIBTION] admin %s successfully unfreezed the subscription", adminId)))
								}
							}
						}(string(adminId))
					}
				}
			}

			if response.Status {
				//check latest version
				if response.Code != 3 && (os.Getenv("server") == "demo" || os.Getenv("server") == "staging" || os.Getenv("server") == "development") {
					sql := `select * from system_app_release order by version_code desc  limit 1`
					latestVersion, err := db.Query(sql)
					log.IfError(err)
					latestVerMajor, latestVerMinor, latestVerPatch := extractVersion(utils.ToString(latestVersion["version_name"]))
					if versionMajor < latestVerMajor || (versionMajor == latestVerMajor && versionMinor < latestVerMinor) || (versionMajor == latestVerMajor && versionMinor == latestVerMinor && versionPatch < latestVerPatch) {
						log.Info("user %s using version: %s - inform to update...", string(deviceId), latestVersion["version_name"])
						releaseNote := ""
						if latestVersion["release_note"] != nil && utils.ToString(latestVersion["release_note"]) != "" {
							releaseNote = "\nUpdate Info: \n" + utils.ToString(latestVersion["release_note"])
						}
						response.Code = 3
						response.Message = fmt.Sprintf("Update Versi %s telah tersedia %s\n Update sekarang melalui ", latestVersion["version_name"], releaseNote)
						if os.Getenv("server") == "demo" || os.Getenv("server") == "staging" {
							response.Message += "Google Play Store"
						} else if os.Getenv("server") == "development" {
							response.Message += "Slack"
						}
					}
				} else if response.Code != 3 && os.Getenv("server") == "production" {
					if versionMajor <= 1 && versionMinor < 16 {
						response.Code = 3
						response.Message = "Maaf, versi aplikasi yang anda gunakan terlalu lawas, silahkan coba update melalui Google Play"
					}
				}

				fmt.Println("response success, continue to insert/update device")
				responseData["employee"] = employee
				responseData["shift"] = getShift(outletId)

				response.Data = responseData

				openShift, err := db.Query("SELECT * FROM open_shift WHERE time_close IS NULL "+
					"AND outlet_fkid=?", outletId)
				log.IfError(err)

				openShiftId := openShift["open_shift_id"]

				//if user has no access to payment, openShiftId can be null or zero,
				roleMobile := models.RoleMobile{}
				err = json.Unmarshal([]byte(employee["role_mobile"].(string)), &roleMobile)
				if !log.IfError(err) {
					if !roleMobile.Pembayaran {
						openShiftId = 0
					}
					log.Info("login employee: %s (%v) | openShiftId: %v | role payment: %v", employee["name"], employee["employee_id"], openShiftId, roleMobile.Pembayaran)
				}

				deviceDb, err := db.Query("SELECT device_id, admin_fkid, outlet_fkid FROM devices WHERE imei=? LIMIT 1", deviceId)
				log.IfError(err)
				if len(deviceDb) > 0 {
					_, err := db.UpdateDb("devices", map[string]interface{}{
						"name":            deviceName,
						"device_status":   "on",
						"open_shift_fkid": openShiftId,
						"outlet_fkid":     outletId,
						"admin_fkid":      adminId,
						"employee_fkid":   employee["employee_id"],
						"version":         version,
						"firebase_token":  firebaseToken,
						"device_info":     deviceInfo,
					}, map[string]interface{}{"imei": deviceId})
					if log.IfError(err) {
						//UNCOMMENT THIS ONCE YOU FEEL READY
						response.Status = false
						response.Message = "Login Gagal! Slot Device Tidak Mencukupi!"
					}
					if utils.ToString(deviceDb["outlet_fkid"]) != string(outletId) || utils.ToString(deviceDb["admin_fkid"]) != string(adminId) {
						log.Error("Device is not same. Device '%s' (%s) \nnew outletId : %s - db : %d | new adminId : %s - db : %d", deviceName, deviceId, outletId, deviceDb["outlet_fkid"], adminId, deviceDb["admin_fkid"])
					}
				} else {
					log.Info("adding new device id... '%s' - outlet: '%s'", string(deviceId), string(outletId))
					data := map[string]interface{}{
						"open_shift_fkid": openShiftId,
						"imei":            deviceId,
						"outlet_fkid":     outletId,
						"admin_fkid":      adminId,
						"employee_fkid":   employee["employee_id"],
						"name":            deviceName,
						"device_status":   "on",
						"data_created":    time.Now().Unix() * 1000,
						"data_modified":   time.Now().Unix() * 1000,
						"data_status":     1,
						"version":         version,
						"firebase_token":  firebaseToken,
						"device_info":     deviceInfo,
					}
					_, err := db.Insert("devices", data)

					if log.IfError(err) {
						//UNCOMMENT THIS ONCE YOU FEEL READY
						// response.Status = false
						// response.Message = "Login Gagal! Slot Device Tidak Mencukupi!"
					}
				}
			}
		} else {
			response.Message = "Invalid PIN"
		}

		//write log to db
		go func() {
			status := "failed"
			if response.Status {
				status = "success"
			}
			_, err = db.GetDb().Exec("INSERT INTO log_login (date_time, ip_address, user_id, type, status) "+
				"VALUES (?,?,?,?,?)", time.Now().Unix()*1000, utils.GetIPAdress(ctx), employee["employee_id"], "employee", status)
			log.IfError(err)
		}()

	} else {
		response.Message = "Employee not found!"
	}

	_ = json.NewEncoder(ctx).Encode(response)
}

func extractVersion(versionName string) (int, int, int) {
	r := strings.NewReplacer("-", ".", "v", "")
	versionName = r.Replace(versionName)
	versionSplit := strings.Split(versionName, ".")
	for i := len(versionSplit); i <= 3; i++ {
		versionSplit = append(versionSplit, "0")
	}
	versionMajor := utils.ToInt(versionSplit[0])
	versionMinor := utils.ToInt(versionSplit[1])
	versionPatch := utils.ToInt(versionSplit[2])
	return versionMajor, versionMinor, versionPatch
}

func GetSubscriptionDetail(adminId interface{}) (models.SubscriptionUser, error) {
	var subscription models.SubscriptionUser
	sql := `select (sbd.service_length_day * sbd.service_period)                       as service_length_day,
       datediff(from_unixtime((select unix_timestamp(now())) + 25200, '%Y-%m-%d'),
                from_unixtime(sb.time_confirm / 1000 + 25200, '%Y-%m-%d')) as dayUsed,
       date_add(from_unixtime(sb.time_confirm / 1000 + 25200), interval
                (sbd.service_length_day * sbd.service_period)
                day)                                                       as expired,
       (select cast(unix_timestamp(date_add(from_unixtime(sb.time_confirm / 1000 + 25200), interval
                                            (sbd.service_length_day * sbd.service_period) day)) as signed)) *
       1000                                                                as time_expired
from system_billing sb
         join system_billing_detail sbd on sb.billing_id = sbd.billing_fkid
         join system_service ss on sbd.sys_service_fkid = ss.sys_service_id
where ss.price = 0
  and ss.service_feature = 'device'
  and admin_fkid = ?
  and sb.invoice like '%/TRIAL/%'
LIMIT 1 `
	trial, err := db.Query(sql, adminId)
	if log.IfError(err) {
		return subscription, err
	}

	subscription.TrialLeft = utils.ToInt(trial["service_length_day"]) - utils.ToInt(trial["dayUsed"])
	if len(trial) == 0 {
		subscription.TrialLeft = -1
	}

	sql = `select ss.service_time_expired,
       datediff(from_unixtime(ss.service_time_expired / 1000, '%Y-%m-%d'),
                from_unixtime((select unix_timestamp(now())), '%Y-%m-%d')) as day_left,
       IF(service_time_start < unix_timestamp() * 1000, 1, 0)              as has_started
from system_subscribe ss
where admin_fkid = ?
  and feature = 'device'
  and service_time_expired > unix_timestamp() * 1000 `
	subscriptions, err := db.QueryArray(sql, adminId)
	if log.IfError(err) {
		return subscription, err
	}

	slots := make([]models.Slot, 0)
	for i, sub := range subscriptions {
		slots = append(slots, models.Slot{
			DayLeft:  utils.ToInt(sub["day_left"]),
			IsActive: utils.ToInt(sub["has_started"]) == 1,
		})
		if utils.ToInt(sub["has_started"]) == 1 {
			subscription.TotalActiveSlot += 1
			if i == 0 {
				subscription.MinDayLeft = utils.ToInt(sub["day_left"])
			}

			if dl := utils.ToInt(sub["day_left"]); dl > subscription.MaxDayLeft {
				subscription.MaxDayLeft = dl
			} else if dl < subscription.MinDayLeft {
				subscription.MinDayLeft = dl
			}
		} else {
			subscription.TotalFeatureSlot += 1
		}
	}
	subscription.Slots = slots
	return subscription, nil
}

func PrintSubscription(adminId string) {
	sub, err := getSubscription(adminId, "")
	fmt.Println(err)
	fmt.Println(adminId, "subscription is: ", utils.SimplyToJson(sub))
}

func getShift(outletId []byte) map[string]interface{} {
	openShift, err := db.Query("SELECT * FROM open_shift WHERE time_close IS NULL "+
		"AND outlet_fkid=?", outletId)
	utils.CheckErr(err)
	return openShift
}

func LogoutEmployee(ctx *fasthttp.RequestCtx) {
	deviceId := ctx.FormValue("device_id")
	res, err := db.Update("devices", map[string]interface{}{
		"device_status": "off",
	}, "imei = ?", string(deviceId))
	log.IfError(err)
	if string(deviceId) == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		json.NewEncoder(ctx).Encode(map[string]interface{}{
			"message": "device_id can not be empty",
		})
		return
	}

	response := models.Response{Status: true}
	if val, _ := res.RowsAffected(); val > 0 {
		response.Message = "Successfully updated!"
	} else {
		log.Warn("logout, but no data effected! IMEI : '%s'", deviceId)
		response.Message = "No row effected!"
	}

	ctx.SetContentType("application/json")
	json.NewEncoder(ctx).Encode(response)
}
