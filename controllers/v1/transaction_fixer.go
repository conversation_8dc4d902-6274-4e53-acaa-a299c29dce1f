package v1

import (
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
)

func FixPromoDiscNominal() {
	log.Info("Starting FixPromoDiscNominal process...")

	processedCount := 0

	for {
		// Get invalid data
		sql := `SELECT s.sales_id, sp.sales_promotion_id, sp.promotion_value, sd.sales_detail_id, 
(SELECT count(*) from sales_detail sd 
join sales_detail_discount sdd on sdd.sales_detail_fkid=sd.sales_detail_id
join sales_promotion sp on sp.sales_fkid=sd.sales_fkid
where sd.sales_fkid=s.sales_id) as _cnt
from sales_promotion sp 
        join promotions p on p.promotion_id=sp.promotion_fkid
        join sales s on s.sales_id=sp.sales_fkid
        join sales_detail sd on sd.sales_fkid=s.sales_id
        left join sales_detail_discount sdd on sdd.sales_detail_fkid=sd.sales_detail_id        
        where p.promotion_type_id=12 and p.discount_type='nota' and sdd.sales_detail_discount_id is null 
        HAVING _cnt=0
        limit 1`

		data, err := db.QueryArray(sql)
		if log.IfError(err) {
			return
		}

		if len(data) == 0 {
			log.Info("No more invalid data found. Process completed. Total processed: %d", processedCount)
			break
		}

		record := data[0]
		salesId := utils.ToString(record["sales_id"])
		salesPromotionId := utils.ToInt(record["sales_promotion_id"])
		promotionValue := utils.ToInt(record["promotion_value"])

		//skip if promo value is 0
		if promotionValue == 0 {
			log.Info("Promotion value is 0 for sales_id: %s", salesId)
			// continue
		}

		//validate is sales_detail_discount with promotion exist
		sql = `SELECT count(*) as cnt from sales_detail_discount sdd join sales_promotion sp on sp.sales_promotion_id=sdd.sales_promotion_fkid where sp.sales_fkid=?`
		dataCnt, err := db.Query(sql, salesId)
		if log.IfError(err) {
			return
		}
		if utils.ToInt(dataCnt["cnt"]) > 0 {
			log.Info("sales_detail_discount already exist for sales_id: %s", salesId)
			continue
		}

		log.Info("Processing sales_id: %s, promotion_value: %d", salesId, promotionValue)

		// Get all items sold
		itemsSql := `SELECT sales_detail_id, (sd.sub_total-COALESCE(abs(sv.sub_total),0)) as sub_total 
		from sales_detail sd 
		left join sales_void sv on sv.sales_detail_fkid=sd.sales_detail_id
		where sd.sales_fkid=? and sd.sub_total>0`

		items, err := db.QueryArray(itemsSql, salesId)
		if log.IfError(err) {
			continue
		}

		if len(items) == 0 {
			log.Warn("No valid items found for sales_id: %s", salesId)
			continue
		}

		// Calculate total subtotal
		totalAllSubtotal := 0
		for _, item := range items {
			totalAllSubtotal += utils.ToInt(item["sub_total"])
		}

		log.Info("Total subtotal for sales_id %s: %d", salesId, totalAllSubtotal)

		if totalAllSubtotal == 0 {
			log.Warn("Total subtotal is 0 for sales_id: %s", salesId)
			continue
		}

		// Insert proportional discounts
		for _, item := range items {
			salesDetailId := utils.ToInt(item["sales_detail_id"])
			subtotal := utils.ToInt(item["sub_total"])

			// Calculate proportional discount
			proportionalDiscount := (subtotal * promotionValue) / totalAllSubtotal

			log.Info("Inserting discount for sales_detail_id: %d, subtotal: %d, discount: %d",
				salesDetailId, subtotal, proportionalDiscount)

			_, err = db.Insert("sales_detail_discount", map[string]any{
				"sales_detail_fkid":    salesDetailId,
				"type":                 "promotion",
				"total":                proportionalDiscount,
				"sales_promotion_fkid": salesPromotionId,
			})

			if log.IfError(err) {
				log.Error("Failed to insert discount for sales_detail_id: %d", salesDetailId)
				continue
			}
		}

		processedCount++
		log.Info("Successfully processed sales_id: %s (%d/%d items)", salesId, len(items), processedCount)
		// break
	}
}
