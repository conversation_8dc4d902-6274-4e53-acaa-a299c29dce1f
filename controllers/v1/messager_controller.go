package v1

import (
	"encoding/json"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
	"os"
)

func SendWhatsapp(ctx *fasthttp.RequestCtx) {
	bearer := "Bearer N5t9JNaCW4HfwhG9yXaQL0mdtx1M3BOX"
	groups := map[string]string{
		"tech": "6285742257881-1495197570",
		"uniq":   "6287838362747-1495704774",
	}

	headerToken := string(ctx.Request.Header.Peek("Authorization"))
	msg := ctx.FormValue("message")
	phone := string(ctx.FormValue("phone"))
	msgType := ctx.FormValue("type")
	id := ctx.FormValue("id")

	if headerToken != bearer {
		ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		return
	}

	if string(msgType) == "admin" {
		data, err := db.Query("SELECT phone FROM admin where admin_id=?", id)
		utils.CheckErr(err)
		if len(data) > 0 {
			phone = data["phone"].(string)
		}
	}else if string(msgType) == "employee" {
		data, err := db.Query("SELECT phone FROM employee where employee_id=?", id)
		utils.CheckErr(err)
		if len(data) > 0 {
			phone = data["phone"].(string)
		}
	}else if string(msgType) == "group" {
		phone = groups[string(id)]
	}else if string(msgType) == "secure" {
		phone = utils.Decrypt(phone)
	}

	if phone == "" || string(msg) == "" {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	requestResult := "Message sent!"
	err := utils.SendWhatsAppMessage(string(msg), "","", phone)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		requestResult = "Error - "+err.Error()
	}

	if  os.Getenv("server") == "production" {
		//utils.SendWhatsAppMessage("*>>> COPY OF MESSAGE <<<* \n\n"+string(msg), "","", "6285742257881")
	}

	ctx.SetContentType("application/json")
	json.NewEncoder(ctx).Encode(models.ResponseAny{Status: err == nil, Message:requestResult})
}

