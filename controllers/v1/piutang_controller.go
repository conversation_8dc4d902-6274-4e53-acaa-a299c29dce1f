package v1

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

func GetPiutang(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Message: "Success"}
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	data, err := db.QueryArray("SELECT p.* FROM piutang p JOIN sales s ON p.sales_fkid=s.sales_id "+
		"WHERE s.outlet_fkid=? AND data_modified >= ? and s.status = 'Success'", outletId, lastSync)
	utils.CheckErr(err)

	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetPiutangHistory(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Message: "Success"}
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}
	data, err := db.QueryArray("select ph.*, sp.* from piutang_history ph join sales_payment sp "+
		"on ph.sales_payment_fkid = sp.payment_id join sales s on sp.sales_fkid=s.sales_id WHERE s.outlet_fkid=? "+
		"AND ph.data_modified >= ?", outletId, lastSync)
	utils.CheckErr(err)

	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}

func AddPiutangPayment(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Status: true, Millis: time.Now().Unix() * 1000, Message: "Success"}
	responseData := make(map[string]interface{})
	piutang := models.PiutangHistory{}
	adminId := ctx.Request.Header.Peek("admin_id")

	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&piutang)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	fmt.Println("pay piutang: ", utils.SimplyToJson(piutang))

	//validate
	data, err := db.Query("select s.outlet_fkid, p.piutang_id from piutang p join sales s on p.sales_fkid = s.sales_id "+
		"WHERE p.piutang_id=?", piutang.PiutangFkid)
	if len(data) <= 0 || log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		log.Warn("piutang id not found in db, or error happend: ", piutang.PiutangFkid)
		return
	}

	if !auth.ValidateOutletId(ctx, int(data["outlet_fkid"].(int64))) {
		log.Warn("user has no access to add piutang for outletId : %v | authorized list : %v", data["outlet_fkid"], ctx.Request.Header.Peek("outlet"))
		return
	}

	piutangHistoryId := int64(0)
	salesPaymentId := int64(0)

	err = db.WithTransaction(func(transaction db.Transaction) error {
		data = map[string]interface{}{
			"sales_fkid":   piutang.SalesFkid,
			"method":       piutang.Method,
			"total":        piutang.Total,
			"pay":          piutang.Pay,
			"info":         piutang.Info,
			"time_created": time.Now().Unix() * 1000,
		}
		res, err := db.Insert("sales_payment", data)
		if err != nil {
			return err
		}

		salesPaymentId, _ = res.LastInsertId()

		data = map[string]interface{}{
			"piutang_fkid":       piutang.PiutangFkid,
			"data_created":       piutang.DataCreated,
			"data_modified":      time.Now().Unix() * 1000,
			"sales_payment_fkid": salesPaymentId,
			"employee_fkid":      piutang.EmployeeFkid,
		}
		res, err = db.Insert("piutang_history", data)
		if err != nil {
			return err
		}
		piutangHistoryId, _ = res.LastInsertId()

		return nil
	})
	if log.IfErrorSetStatus(ctx, err) {
		fmt.Println("save piutang err", err)
		return
	}

	//publish to pubsub
	go publishPiutangToSubscriber(piutang.SalesFkid, utils.ToInt64(adminId), int64(piutang.PiutangFkid), salesPaymentId)

	responseData["piutang_id"] = data["piutang_id"]
	responseData["sales_payment_id"] = salesPaymentId
	responseData["piutang_history_id"] = piutangHistoryId
	response.Data = responseData
	_ = json.NewEncoder(ctx).Encode(response)
}

func publishPiutangToSubscriber(salesId string, adminId, piutangId, paymentId int64) {
	ctx := context.Background()
	client := google.GetPubSubClient()

	if client == nil {
		return
	}

	data := map[string]interface{}{
		"sales_id":   salesId,
		"payment_id": paymentId,
		"piutang_id": piutangId,
		"admin_id":   adminId,
	}

	env := os.Getenv("server")
	if env == "demo" {
		env = "staging"
	}
	topicId := fmt.Sprintf("sales_piutang_%s", env)
	t := client.Topic(topicId)

	result := t.Publish(ctx, &pubsub.Message{
		Data: []byte(utils.SimplyToJson(data)),
	})

	id, err := result.Get(ctx)
	if log.IfError(err) {
		fmt.Printf("failed to publish to topic '%s' : %v", topicId, err)
	}
	fmt.Printf("piutang: '%d' with payment: '%d' published to topic: '%s', msg ID: %v\n", piutangId, paymentId, topicId, id)
}
