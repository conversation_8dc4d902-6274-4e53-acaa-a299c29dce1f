package v1

import (
	"fmt"
	"time"

	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

func getSubscription(adminId, imei string) (models.SubscriptionStatus, error) {
	//first, make sure the current device is off
	_, _ = db.Update("devices", map[string]interface{}{
		"device_status": "off",
	}, "imei = ?", imei)
	var subscription models.SubscriptionStatus
	subscription.IsSlotAvailable = true

	device, err := db.Query("select device_id from devices where imei = ?", imei)
	log.IfError(err)

	//1. Check slot
	sql := `
select ss.id, service_time_expired,
       datediff(from_unixtime(service_time_expired / 1000 + 25200, '%Y-%m-%d'),from_unixtime((select unix_timestamp(now())) + 25200, '%Y-%m-%d')) as dayLeft
from system_subscribe ss
         join system_service s on ss.sys_service_fkid = s.sys_service_id
where s.service_feature = 'device' and ss.admin_fkid = ? and service_time_start < (SELECT  UNIX_TIMESTAMP()*1000) AND
        service_time_expired > (SELECT  UNIX_TIMESTAMP()*1000)
  and ss.id not in (select system_subscribe_fkid from device_login)
  and (ss.outlet_fkid is null or outlet_fkid = (SELECT outlet_fkid from devices d where device_id = ?))
order by service_time_expired desc limit 1
`
	data, err := db.Query(sql, adminId, device["device_id"])
	if log.IfError(err) {
		return subscription, err
	}

	if len(data) > 0 {
		//if return any data it's mean there is slot available
		subscription.DaysLeft = utils.ToInt(data["dayLeft"])
		subscription.TimeMillisExpired = utils.ToInt64(data["service_time_expired"])
		subscription.DateExpired = time.Unix(subscription.TimeMillisExpired, 0).Format("2006-01-02")
		return subscription, nil
	}

	//if check slot not return any data, get all subscription from database
	sql = `
select service_time_start,
       service_time_expired,
       ss.id,
       d.name,
       o.name as outlet_name,
       e.name as employee_name,
       d.device_id
from system_subscribe ss
         join system_billing s on ss.billing_fkid = s.billing_id
         join system_service ss2 on ss.sys_service_fkid = ss2.sys_service_id
         left join device_login dl on ss.id = dl.system_subscribe_fkid
         left join devices d on dl.device_fkid = d.device_id
         left join outlets o on d.outlet_fkid = o.outlet_id
         left join employee e on d.employee_fkid = e.employee_id
where s.admin_fkid = ?
  and ss2.service_feature = 'device' 
`

	dataArr, err := db.QueryArray(sql, adminId)
	if log.IfError(err) {
		return subscription, err
	}

	// log.Info("subscription, total purchased %v : %v", adminId, len(dataArr))
	if len(dataArr) == 0 {
		//if no data, it's mean user never purchase any subscription. then check free trial
		return getTrialStatus(adminId, false)
	}

	timeNow := time.Now().Unix() * 1000
	isPendingSlotAvailable := false
	totalSlot := 0
	deviceNames := make([]string, 0)
	deviceLogin := make([]models.Device, 0)
	for _, data := range dataArr {
		serviceStart := utils.ToInt64(data["service_time_start"])
		serviceEnd := utils.ToInt64(data["service_time_expired"])
		if serviceEnd > timeNow {
			if serviceStart < timeNow {
				//yea, there is active subscription
				if data["name"] == nil {
					continue
				}
				totalSlot++
				deviceNames = append(deviceNames, fmt.Sprintf("- %s (%s)", data["name"], data["outlet_name"]))
				deviceLogin = append(deviceLogin, models.Device{
					DeviceName:   utils.ToString(data["name"]),
					DeviceId:     utils.ToInt(data["device_id"]),
					OutletName:   utils.ToString(data["outlet_name"]),
					EmployeeName: utils.ToString(data["employee_name"]),
				})
			} else if serviceStart > timeNow {
				isPendingSlotAvailable = true
			}
		}
	}

	if totalSlot > 0 {
		subscription.Type = "subscription"
		subscription.TotalSlot = totalSlot
		subscription.IsSlotAvailable = false
		subscription.DeviceLoginNames = deviceNames
		subscription.DeviceLogin = deviceLogin
		return subscription, nil
	}

	if isPendingSlotAvailable {
		return getTrialStatus(adminId, true)
	} else {
		//nope, all subscription is expired...
		subscription.DaysLeft = 0
	}

	return subscription, nil
}

func getTrialStatus(adminId string, isHasPendingSlot bool) (models.SubscriptionStatus, error) {
	var subscription models.SubscriptionStatus
	subscription.IsSlotAvailable = true

	sql := `
select (sbd.service_length_day * sbd.service_period)                                                               as service_length_day,
       datediff(from_unixtime((select unix_timestamp(now())) + 25200, '%Y-%m-%d'),
                from_unixtime(sb.time_confirm / 1000 + 25200, '%Y-%m-%d'))                                         as dayUsed,
       date_add(from_unixtime(sb.time_confirm / 1000 + 25200), interval
                (sbd.service_length_day * sbd.service_period)
                day)                                                                                               as expired,
       (select cast(unix_timestamp(date_add(from_unixtime(sb.time_confirm / 1000 + 25200), interval
                                            (sbd.service_length_day * sbd.service_period) day)) as signed)) *
       1000                                                                                                        as time_expired
from system_billing sb
         join system_billing_detail sbd on sb.billing_id = sbd.billing_fkid
         join system_service ss on sbd.sys_service_fkid = ss.sys_service_id
where ss.price = 0
  and ss.service_feature = 'device'
  and admin_fkid = ?
LIMIT 1 `
	data, err := db.Query(sql, adminId)
	if log.IfError(err) {
		return subscription, err
	}

	subscription.Type = "trial"
	subscription.DaysLeft = utils.ToInt(data["service_length_day"]) - utils.ToInt(data["dayUsed"])
	subscription.TimeMillisExpired = utils.ToInt64(data["time_expired"])

	//if user has pending subscription (will active in the future),
	//then set day-left to high in order to avoid subscription reminder
	if subscription.DaysLeft >= 0 && subscription.DaysLeft <= 7 && isHasPendingSlot {
		subscription.DaysLeft = 15
	}

	// log.Info("subscription free trial for %v : %v days left", adminId, subscription.DaysLeft)
	return subscription, nil
}

func getSubscriptionStatus(adminId, deviceId string) (models.SubscriptionStatus, error) {
	//first, make sure the current device is off
	_, _ = db.Update("devices", map[string]interface{}{
		"device_status": "off",
	}, "imei = ?", deviceId)

	var subscription models.SubscriptionStatus
	subscription.IsSlotAvailable = true

	//# Check if if free trial is active
	sql := `
select sbd.service_length_day,
                   datediff(from_unixtime((select unix_timestamp(now())) + 25200, '%Y-%m-%d'),
                            from_unixtime(sb.time_confirm / 1000 + 25200, '%Y-%m-%d')) as dayUsed
            from system_billing sb
                     join system_billing_detail sbd on sb.billing_id = sbd.billing_fkid
                     join system_service ss on sbd.sys_service_fkid = ss.sys_service_id
            where ss.price = 0
              and ss.service_feature = 'device' and admin_fkid = ? LIMIT 1 `
	data, err := db.Query(sql, adminId)
	if log.IfError(err) {
		fmt.Println("get status trial error - ", err)
		return subscription, err
	}

	subscription.Type = "trial"
	subscription.DaysLeft = utils.ToInt(data["service_length_day"]) - utils.ToInt(data["dayUsed"])

	//if trial is end, check the subscription
	if subscription.DaysLeft <= 0 {
		//#check if user ever buy subscription
		sql = `
select sys_service_id from system_billing sb join system_billing_detail sbd on sb.billing_id = sbd.billing_fkid
join system_service ss on sbd.sys_service_fkid = ss.sys_service_id
where ss.price > 0 and billing_status = 'success' and sb.admin_fkid = ?`
		data, err = db.Query(sql, adminId)
		log.IfError(err)

		if len(data) > 0 {
			subscription.Type = "subscribe"

			//ok, user ever buy subscription, check if subscription is still in active (base on date)
			sql = `
select count(*) as available from system_subscribe ss join system_billing s on ss.billing_fkid = s.billing_id
where s.admin_fkid = ?
  and service_time_start < (SELECT UNIX_TIMESTAMP() * 1000)
  AND service_time_expired > (SELECT UNIX_TIMESTAMP() * 1000)`
			available, err := db.Query(sql, adminId)
			log.IfError(err)

			if len(available) > 0 {
				//finally, check if any slot available
				sql = `
select ss.id, service_time_expired, 
	datediff(from_unixtime(service_time_expired / 1000 + 25200, '%Y-%m-%d'),from_unixtime((select unix_timestamp(now())) + 25200, '%Y-%m-%d')) as dayLeft
        from system_subscribe ss
                 join system_service s on ss.sys_service_fkid = s.sys_service_id
        where s.service_feature = 'device' and ss.admin_fkid = ? and service_time_start < (SELECT  UNIX_TIMESTAMP()*1000) AND
              service_time_expired > (SELECT  UNIX_TIMESTAMP()*1000)
          and ss.id not in (select system_subscribe_fkid from device_login)
        order by service_time_expired desc limit 1 `
				data, err = db.Query(sql, adminId)
				log.IfError(err)

				//if query return data, it's mean there is slot available to use
				if len(data) > 0 {
					subscription.DaysLeft = utils.ToInt(data["dayLeft"])
					subscription.TimeMillisExpired = utils.ToInt64(data["service_time_expired"])
					subscription.DateExpired = time.Unix(subscription.TimeMillisExpired, 0).Format("2006-01-02")
				} else {
					subscription.IsSlotAvailable = false
				}
			} else {
				//nope, all subscription is expired...
				subscription.DaysLeft = 0
			}
		}
	} else if subscription.DaysLeft > 0 && subscription.DaysLeft <= 7 {
		//if trial will expired in the next 7 days, check if user has subscribed to any subscription
		sql = `
select sys_service_id from system_billing sb join system_billing_detail sbd on sb.billing_id = sbd.billing_fkid
join system_service ss on sbd.sys_service_fkid = ss.sys_service_id
where ss.price > 0 and billing_status = 'success' and sb.admin_fkid = ? limit 1`

	}

	return subscription, nil
}
