package v1

import (
	"fmt"
	"testing"
)

func Test_getRandomNotification(t *testing.T) {
	type args struct {
		params NotificationParams
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 string
	}{
		{"test 1", args{params: NotificationParams{
			TotalPoint:        10,
			OutletName:        "Yamie Panda",
			EarnType:          "nominal",
			TransactionStatus: "success",
			GrandTotal:        1000,
		}}, "email", "1001"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := getRandomNotification(tt.args.params)
			if got == "" {
				t.<PERSON><PERSON><PERSON>("getRandomNotification() got = %v, want %v", got, tt.want)
			}
			if got1 == "" {
				t.<PERSON>rrorf("getRandomNotification() got1 = %v, want %v", got1, tt.want1)
			}
			fmt.Println(got, got1)
		})
	}
}
