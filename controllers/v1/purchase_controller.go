package v1

import (
	"bytes"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

func GetOperationalCostByShift(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	openShiftId := ctx.UserValue("openShiftId")

	shift, err := db.Query("SELECT outlet_fkid, time_open, time_close FROM open_shift WHERE open_shift_id=? LIMIT 1", openShiftId)
	log.IfError(err)

	if len(shift) == 0 {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	//outletId := strconv.FormatInt(shift["outlet_fkid"].(int64), 10)
	if !auth.ValidateOutletId(ctx, int(shift["outlet_fkid"].(int64))) {
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return
	}

	timeOpen := shift["time_open"].(int64)
	var timeClose = millis
	if shift["time_close"] != nil {
		timeClose = shift["time_close"].(int64)
	}

	//timeOpenDate, err := millisToDate(timeOpen)
	//log.IfError(err)
	//timeCloseDate, err := millisToDate(timeClose)
	//log.IfError(err)

	timeOffset := 7 //7 hours
	timeOpenDate := time.Unix(timeOpen/1000, 0).Add(time.Hour * time.Duration(timeOffset)).Format("2006-01-02")
	log.Info("timeOpenDate: %s, from: %d, to: %d", timeOpenDate, timeOpen, timeClose)

	data, err := db.QueryArray("SELECT * FROM operationalcost WHERE time_created >= ? AND time_created <= ? AND outlet_fkid = ? and data_created >= ?", timeOpen, timeClose, shift["outlet_fkid"], timeOpenDate)
	log.IfError(err)

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func GetSupplier(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	adminId := ctx.Request.Header.Peek("admin_id")

	data, err := db.QueryArray("SELECT * FROM supplier WHERE admin_fkid=?", adminId)
	log.IfError(err)

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func GetOperationalCostByOutlet(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	if lastSync == nil {
		lastSync = 0
	}

	// log.Info("get operational cost by outlet lastSync: %v", lastSync)
	data, err := db.QueryArray(`select o.*, prc.name as 'purchase_category_name', s.name as 'supplier_name', 
	pmb.name as bank_name, CAST(ROUND(o.harga) AS UNSIGNED) AS harga, CAST(ROUND(o.total) AS UNSIGNED) AS total
		from operationalcost o join purchase_report_category prc on o.prc_category_fkid=prc.purchase_report_category_id 
		left join payment_media_bank pmb on pmb.bank_id=o.payment_bank_fkid 
		join supplier s on s.supplier_id=o.supplier_fkid where o.outlet_fkid=? and time_updated >= ?`, outletId, lastSync)
	log.IfError(err)

	for i, v := range data {
		//modify time_created to use data from data_created, if data_created is not null
		//data_created is in format 2025-02-04
		if v["data_created"] != nil {
			date := v["data_created"].(string)
			date += " 23:59:00"
			timeCreated, err := time.Parse("2006-01-02 15:04:05", date)
			if err != nil {
				log.Error("error parsing time_created: %s", err)
				continue
			}
			data[i]["time_created"] = timeCreated.Unix() * 1000
			// log.Info("time_created: %d, data_created: %s", data[i]["time_created"], date)
		}
	}

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func GetPurchaseReportCategory(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	adminId := ctx.Request.Header.Peek("admin_id")
	lastSync := ctx.UserValue("lastSync")

	data, err := db.QueryArray("SELECT * FROM purchase_report_category WHERE admin_fkid=? AND data_modified>=?", adminId, lastSync)
	log.IfError(err)

	_ = json.NewEncoder(ctx).Encode(models.ResponseArray{Status: true, Millis: millis, Data: data})
}

func SaveOperationalCost(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	adminId := ctx.Request.Header.Peek("admin_id")
	response := models.ResponseAny{Millis: millis}
	oc := models.OperationalCost{}

	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&oc)
	if log.IfError(err) {
		fmt.Println("Parsing OperationalCost failed : ", err)
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	if !auth.ValidateOutletId(ctx, oc.OutletFkid) {
		return
	}

	date, err := millisToDate(oc.TimeCreated / 1000)
	log.IfError(err)

	//validate data
	if oc.Payment == "card" && oc.PaymentBankFkid == nil {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ResponseAny{Message: "Bank id can not empty"})
		return
	}

	if oc.SubCategoryFkid == 0 {
		subCategory, err := db.Query("select product_subcategory_id from products_subcategory where admin_fkid = ? and name= ? LIMIT 1", adminId, oc.PurcaseCategoryName)
		if !log.IfError(err) {
			if len(subCategory) > 0 {
				oc.SubCategoryFkid = utils.ToInt(subCategory["product_subcategory_id"])
			} else {
				resp, err := db.Insert("products_subcategory", map[string]interface{}{
					"name":          oc.PurcaseCategoryName,
					"admin_fkid":    adminId,
					"data_created":  time.Now().Unix() * 1000,
					"data_modified": time.Now().Unix() * 1000,
					"data_status":   "on",
				})
				log.IfError(err)
				id, _ := resp.LastInsertId()
				oc.SubCategoryFkid = int(id)
			}
		}
	}

	// Check if prc_category_fkid exists
	if cast.ToInt(oc.PrcCategoryFkid) > 0 {
		prcCategory, err := db.Query("SELECT purchase_report_category_id FROM purchase_report_category WHERE purchase_report_category_id = ? LIMIT 1", oc.PrcCategoryFkid)
		if !log.IfError(err) {
			if len(prcCategory) == 0 {
				// Category doesn't exist, create new one with [Unknown] name
				resp, err := db.Insert("purchase_report_category", map[string]interface{}{
					"name":          "[Unknown]",
					"admin_fkid":    adminId,
					"data_created":  time.Now(),
					"data_modified": time.Now(),
					"data_status":   "on",
				})
				if !log.IfError(err) {
					id, _ := resp.LastInsertId()
					oc.PrcCategoryFkid = int(id)
				}
			}
		}
	}

	data := map[string]interface{}{
		"opcost_name":       utils.TakeMax(oc.OpcostName, 30),
		"qty":               oc.Qty,
		"harga":             oc.Harga,
		"total":             oc.Total,
		"supplier_fkid":     oc.SupplierFkid,
		"keterangan":        utils.TakeMax(utils.ReplaceEmoji(oc.Keterangan), 50),
		"user_fkid":         adminId,
		"data_created":      date,
		"data_status":       "on",
		"outlet_fkid":       oc.OutletFkid,
		"prc_category_fkid": oc.PrcCategoryFkid,
		"sub_category_fkid": oc.SubCategoryFkid,
		"employe_fkid":      oc.EmployeeFkid,
		"shift_fkid":        oc.ShiftFkid,
		"payment":           oc.Payment,
		"payment_bank_fkid": oc.PaymentBankFkid,
		"time_created":      oc.TimeCreated,
		"time_updated":      time.Now().Unix() * 1000,
	}

	resp, err := db.Insert("operationalcost", data)
	if log.IfErrorSetStatus(ctx, err) {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		log.Info("failed save operational cost: %s", string(ctx.PostBody()))
		return
	}

	id, _ := resp.LastInsertId()
	newData, err := db.Query("select o.*, prc.name as 'purchase_category_name', s.name as 'supplier_name' "+
		"from operationalcost o join purchase_report_category prc on o.prc_category_fkid=prc.purchase_report_category_id "+
		"join supplier s on s.supplier_id=o.supplier_fkid where o.operationalcost_id = ?", id)
	log.IfError(err)

	if id > 0 {
		dataOperationCost := map[string]interface{}{
			"operationalcost_id": id,
		}
		err = google.PublishMessage(dataOperationCost, fmt.Sprintf("operationalcost-%s", os.Getenv("server")))
		log.IfError(err)
	}

	response.Status = true
	response.Message = "Data successfully inserted!"
	response.Data = newData
	_ = json.NewEncoder(ctx).Encode(response)
}

func SavePurchaseReportCategory(ctx *fasthttp.RequestCtx) {
	purchaseName := string(ctx.PostArgs().Peek("category_name"))
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: true}
	adminId := ctx.Request.Header.Peek("admin_id")

	// check if name existing
	data, err := db.QueryArray("SELECT purchase_report_category_id FROM purchase_report_category WHERE name = ?", purchaseName)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	if len(data) == 0 {
		date, _ := utils.MillisToDateTime(time.Now().Unix())
		resp, err := db.Insert("purchase_report_category", map[string]interface{}{
			"name":          purchaseName,
			"data_status":   "on",
			"admin_fkid":    adminId,
			"data_created":  date,
			"data_modified": date,
		})
		if log.IfErrorSetStatus(ctx, err) {
			return
		}
		id, _ := resp.LastInsertId()
		response.Data = id
	} else {
		response.Status = false
		response.Message = "purchase report category name already exist"
	}
	_ = json.NewEncoder(ctx).Encode(response)
}

func DeleteOperationalCost(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000}
	//adminId := ctx.Request.Header.Peek("admin_id")
	operationalCostId := ctx.UserValue("id")

	data, err := db.Query("SELECT * FROM operationalcost WHERE operationalcost_id=? LIMIT 1", operationalCostId)
	log.IfError(err)

	if len(data) == 0 {
		response.Message = "Data Not Found!"
		response.Status = false
		_ = json.NewEncoder(ctx).Encode(response)
	} else {
		if !auth.ValidateOutletId(ctx, int(data["outlet_fkid"].(int64))) {
			return
		}

		_, err = db.GetDb().Exec("DELETE FROM operationalcost WHERE operationalcost_id=?", operationalCostId)
		if log.IfErrorSetStatus(ctx, err) {
			return
		}
		response.Status = false
		response.Message = "Data successfully deleted!"
		_ = json.NewEncoder(ctx).Encode(response)
	}
}

func GetPurchaseProductByOutlet(ctx *fasthttp.RequestCtx) {
	millis := time.Now().Unix() * 1000
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	purchases, err := db.QueryArray("SELECT * FROM purchase WHERE outlet_fkid=? AND data_created >= ?", outletId, lastSync)
	log.IfError(err)

	purchaseDetails, err := db.QueryArray("SELECT pp.* FROM purchase_products pp JOIN purchase p "+
		"ON p.purchase_id=pp.purchase_fkid WHERE p.outlet_fkid=? AND p.data_created >= ?", outletId, lastSync)
	log.IfError(err)

	for _, prcs := range purchases {
		detail := make([]map[string]interface{}, 0)
		for _, pd := range purchaseDetails {
			if pd["purchase_fkid"] == prcs["purchase_id"] {
				detail = append(detail, pd)
			}
		}

		prcs["detail"] = detail
	}

	response := models.ResponseAny{Millis: millis, Data: purchases, Status: true}
	_ = json.NewEncoder(ctx).Encode(response)
}

func SavePurchaseProduct(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: false}

	purchase := models.Purchase{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&purchase)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		response.Message = "Invalid json format!"
		_ = json.NewEncoder(ctx).Encode(response)
		return
	}

	if !auth.ValidateOutletId(ctx, purchase.OutletFkid) {
		return
	}

	employee, _ := db.Query("SELECT name FROM employee WHERE employee_id=?", purchase.EmployeeFkid)

	data := map[string]interface{}{
		"invoice":        purchase.Invoice,
		"keterangan":     purchase.Keterangan,
		"bayar":          purchase.Bayar,
		"status_lunas":   purchase.StatusLunas,
		"supplier_fkid":  purchase.SupplierFkid,
		"employee_fkid":  purchase.EmployeeFkid,
		"admin_fkid":     purchase.AdminFkid,
		"user_input":     purchase.UserInput,
		"outlet_fkid":    purchase.OutletFkid,
		"grand_total":    purchase.GrandTotal,
		"hutang":         purchase.Hutang,
		"jatuh_tempo":    purchase.JatuhTempo,
		"discount_total": purchase.DiscountTotal,
		"total_pajak":    purchase.TotalPajak,
		"sub_total":      purchase.SubTotal,
		"type_discount":  purchase.TypeDiscount,
		"data_status":    "on",
		"data_created":   purchase.DataCreated,
	}

	res, err := db.Insert("purchase", data)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	id, _ := res.LastInsertId()

	for _, pd := range purchase.Detail {
		data = map[string]interface{}{
			"purchase_fkid":  id,
			"unit_fkid_nota": pd.UnitFkidNota,
			"qty_nota":       pd.QtyNota,
			"price_nota":     pd.PriceNota,
			"unit_fkid_stok": pd.UnitFkidStok,
			"qty_stok":       pd.QtyStok,
			"price_stok":     pd.PriceStok,
			"discount":       pd.Discount,
			"total":          pd.Total,
			"tot_dis":        pd.TotDis,
			"data_status":    "on",
			"data_created":   pd.DataCreated,
			"products_fkid":  pd.ProductsFkid,
			"qty_retur":      pd.QtyRetur,
			"retur":          "no",
			"keterangan":     pd.Keterangan,
			"pajak":          pd.Pajak,
		}

		res, err = db.Insert("purchase_products", data)
		if log.IfErrorSetStatus(ctx, err) {
			return
		}

		purchaseProductId, _ := res.LastInsertId()

		data = map[string]interface{}{
			"purchase_product_fkid": purchaseProductId,
			"qty":                   pd.QtyReceive,
			"qty_notConfrim":        pd.QtyStok - pd.QtyReceive,
			"qty_arive":             pd.QtyReceive,
			"retur":                 0,
			"user":                  employee["name"],
			"date_created":          purchase.DataCreated,
			"date_updated":          time.Now().Unix() * 1000,
			"data_status":           "on",
		}
		_, err = db.Insert("purchase_confrim", data)
		if log.IfErrorSetStatus(ctx, err) {
			return
		}
	}

	response.Status = true
	response.Message = "New Purchase successfully inserted!"
	_ = json.NewEncoder(ctx).Encode(response)
}

func AddSupplier(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: false}
	adminId := ctx.Request.Header.Peek("admin_id")

	supplier := models.Supplier{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&supplier)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		response.Message = "Invalid json format!"
		_ = json.NewEncoder(ctx).Encode(response)
		return
	}

	dataCreated, _ := millisToDate(supplier.TimeCreated / 1000)
	data := map[string]interface{}{
		"name":          supplier.Name,
		"address":       supplier.Address,
		"city":          supplier.City,
		"phone":         supplier.Phone,
		"fax":           supplier.Fax,
		"email":         supplier.Email,
		"npwp":          supplier.Npwp,
		"keterangan":    supplier.Keterangan,
		"tempo":         supplier.Tempo,
		"admin_fkid":    adminId,
		"data_created":  dataCreated,
		"time_created":  supplier.TimeCreated,
		"time_modified": time.Now().Unix() * 1000,
		"data_status":   "on",
	}

	res, err := db.Insert("supplier", data)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	id, _ := res.LastInsertId()

	response.Status = true
	response.Data = id
	response.Message = "New Supplier successfully inserted!"
	_ = json.NewEncoder(ctx).Encode(response)
}

func UpdateSupplier(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: false}
	adminId := ctx.Request.Header.Peek("admin_id")

	supplier := models.Supplier{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&supplier)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		response.Message = "Invalid json format!"
		_ = json.NewEncoder(ctx).Encode(response)
		return
	}

	data, _ := db.Query("SELECT * FROM supplier WHERE supplier_id = ? AND admin_fkid=?", supplier.SupplierID, adminId)
	if len(data) <= 0 {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		response.Message = "Supplier not found!"
		_ = json.NewEncoder(ctx).Encode(response)
		return
	}

	dataSupplier := map[string]interface{}{
		"name":        supplier.Name,
		"address":     supplier.Address,
		"city":        supplier.City,
		"phone":       supplier.Phone,
		"fax":         supplier.Fax,
		"email":       supplier.Email,
		"npwp":        supplier.Npwp,
		"keterangan":  supplier.Npwp,
		"tempo":       supplier.Tempo,
		"data_status": "on",
	}

	dataWhere := map[string]interface{}{
		"supplier_id": supplier.SupplierID,
		"admin_fkid":  adminId,
	}

	_, err = db.UpdateDb("supplier", dataSupplier, dataWhere)
	if log.IfError(err) {
		response.Message = "UpdateDb Supplier failed!"
	} else {
		response.Message = "UpdateDb Supplier success!"
	}

	_ = json.NewEncoder(ctx).Encode(response)
}

func GetCashIn(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: true}
	outletId := ctx.UserValue("outletId")
	lastSync := ctx.UserValue("lastSync")

	if valid := auth.ValidateOutlet(ctx); !valid {
		return
	}

	data, err := db.QueryArray("SELECT * FROM cash_in WHERE outlet_fkid=? & data_modified>=?", outletId, lastSync)
	log.IfError(err)

	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}

func SaveCashIn(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: false}
	cashIn := models.CashIn{}

	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&cashIn)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	if !auth.ValidateOutletId(ctx, cashIn.OutletFkid) {
		return
	}

	data := map[string]interface{}{
		"total":           cashIn.Total,
		"cash_info":       cashIn.CashInfo,
		"open_shift_fkid": cashIn.OpenShiftFkid,
		"outlet_fkid":     cashIn.OutletFkid,
		"data_created":    cashIn.DataCreated,
		"data_modified":   time.Now().Unix() * 1000,
	}

	res, err := db.Insert("cash_in", data)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	id, _ := res.LastInsertId()

	response.Status = true
	response.Message = "New Cash In successfully inserted!"
	response.Data = id
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetReturProduct(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: true}
	adminId := ctx.Request.Header.Peek("admin_id")

	data, err := db.QueryArray("SELECT * FROM retur_products WHERE admin_fkid=?", adminId)
	log.IfError(err)

	response.Message = "success"
	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}

func SaveReturProduct(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: false}
	adminId := ctx.Request.Header.Peek("admin_id")
	returProduct := models.ReturProduct{}

	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&returProduct)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		response.Message = "Invalid json format!"
		_ = json.NewEncoder(ctx).Encode(response)
		return
	}

	data := map[string]interface{}{
		"purchase_product_fkid": returProduct.PurchaseProductFkid,
		"admin_fkid":            adminId,
		"employee_fkid":         returProduct.EmployeeFkid,
		"qty_retur":             returProduct.QtyRetur,
		"keterangan_retur":      returProduct.KeteranganRetur,
		"data_status":           "on",
		"data_created":          returProduct.DataCreated,
		"qty_nota":              returProduct.QtyNota,
		"total":                 returProduct.Total,
		"tot_dis":               returProduct.TotDis,
		"qty_stok":              returProduct.QtyStok,
		"harga_stok":            returProduct.HargaStok,
	}

	res, err := db.Insert("retur_products", data)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	id, _ := res.LastInsertId()
	response.Status = true
	response.Message = "New Retur Product Successfully inserted!"
	response.Data = id
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetDebtPayment(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: true, Message: "success"}
	adminId := ctx.Request.Header.Peek("admin_id")

	data, err := db.QueryArray("select dp.* from debt_payment dp join purchase p ON "+
		"dp.purchase_fkid=p.purchase_id WHERE p.admin_fkid=?", adminId)
	log.IfError(err)

	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}

func SaveDebtPayment(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: true, Message: "success"}
	adminId := ctx.Request.Header.Peek("admin_id")

	debt := models.DebtPayment{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&debt)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	purchase, _ := db.Query("SELECT * FROM purchase WHERE purchase_id=? AND admin_fkid=?", debt.PurchaseFkid, adminId)
	if len(purchase) <= 0 {
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return
	}

	data := map[string]interface{}{
		"purchase_fkid":      debt.PurchaseFkid,
		"nominal":            debt.Nominal,
		"keterangan_payment": debt.KeteranganPayment,
		"time_created":       debt.TimeCreated,
		"data_status":        "on",
	}
	res, err := db.Insert("debt_payment", data)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	data = map[string]interface{}{
		"hutang": "hutang+" + strconv.Itoa(debt.Nominal),
	}
	_, err = db.UpdateDb("purchase", data, map[string]interface{}{"purchase_id": debt.PurchaseFkid})
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	id, _ := res.LastInsertId()
	response.Data = id
	response.Message = "New Debt Payment Successfully Inserted!"
	_ = json.NewEncoder(ctx).Encode(response)
}

func GetPurchaseConfirm(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: true, Message: "success"}
	adminId := ctx.Request.Header.Peek("admin_id")

	data, err := db.QueryArray("select pc.* from purchase_confrim pc join purchase_products pd on "+
		"pd.purchase_products_id=pc.purchase_product_fkid join purchase p on pd.purchase_fkid = p.purchase_id "+
		"WHERE p.admin_fkid=?", adminId)
	log.IfError(err)

	response.Data = data
	_ = json.NewEncoder(ctx).Encode(response)
}

func SavePurchaseConfirm(ctx *fasthttp.RequestCtx) {
	response := models.ResponseAny{Millis: time.Now().Unix() * 1000, Status: true, Message: "success"}
	adminId := ctx.Request.Header.Peek("admin_id")

	purchase := models.PurchaseConfirm{}
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&purchase)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	data, _ := db.Query("select pp.qty_stok from purchase_products pp join purchase p on "+
		"pp.purchase_fkid = p.purchase_id WHERE p.admin_fkid=? AND pp.purchase_products_id=?", adminId, purchase.PurchaseProductFkid)
	if len(data) <= 0 {
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return
	}

	data = map[string]interface{}{
		"purchase_product_fkid": purchase.PurchaseProductFkid,
		"qty":                   purchase.Qty,
		"qty_notConfrim":        data["qty_stok"].(int64) - int64(purchase.Qty),
		"qty_arive":             purchase.QtyArive,
		"retur":                 purchase.Retur,
		"user":                  purchase.User,
		"date_created":          purchase.DateCreated,
		"date_updated":          time.Now().Unix() * 1000,
		"data_status":           "on",
	}

	res, err := db.Insert("purchase_confrim", data)
	if log.IfErrorSetStatus(ctx, err) {
		return
	}

	id, _ := res.LastInsertId()

	response.Data = id
	_ = json.NewEncoder(ctx).Encode(response)
}
