package v2

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/models"
)

var MultiAccountTokenSecret = ""

func init() {
	MultiAccountTokenSecret = utils.RandStringBytes(32)
}

// get token using refresh token
func Token(ctx *fasthttp.RequestCtx) {
	outletId := string(ctx.FormValue("scope"))

	role := string(ctx.Request.Header.Peek("role"))
	userId := string(ctx.Request.Header.Peek("user_id"))
	insurerId := string(ctx.Request.Header.Peek("insurer_id"))
	tokenId := string(ctx.Request.Header.Peek("token_id"))
	accessLimitUrl := string(ctx.Request.Header.Peek("access_limit_urls"))

	fmt.Println(">>> accessLimitUrls: ", accessLimitUrl)
	var accessLimitUrls []string
	//split accessLimitUrls by comma, only if accessLimitUrls is not empty
	if accessLimitUrl != "" {
		accessLimitUrls = strings.Split(accessLimitUrl, ",")
	}

	// userData := make([]map[string]interface{}, 0)
	var userData []map[string]interface{}
	var err error

	if role == utils.ROLE_OWNER {
		sql := `select outlet_id, a.activation_status
from admin a
         join outlets o on a.admin_id = o.admin_fkid
where admin_id = ? `
		userData, err = db.QueryArray(sql, userId)
		if log.IfErrorSetStatus(ctx, err) {
			return
		}
	} else if role == utils.ROLE_EMPLOYEE {
		sql := `select eo.outlet_fkid as outlet_id, a.activation_status
from employee e
         join employee_outlet eo on e.employee_id = eo.employee_fkid
join admin a on e.admin_fkid=a.admin_id
where employee_id = ? `
		userData, err = db.QueryArray(sql, insurerId)
		if log.IfErrorSetStatus(ctx, err) {
			return
		}
	} else {
		ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		return
	}

	if len(userData) == 0 {
		log.IfError(fmt.Errorf("refresh token invalid. userData is empty. %v (%v)", userId, insurerId))
		ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		return
	}

	if utils.ToString(userData[0]["activation_status"]) != "activated" {
		// log.IfError(fmt.Errorf("refresh token invalid. user is not active. %v (%v)", userId, insurerId))
		log.Info("refresh token invalid. user is not active. %v (%v)", userId, insurerId)
		ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		return
	}

	if outletId == "*" {
		log.Info("scope: request all. Available : %v", userData)
		var outlets = make([]string, 0)
		for _, outlet := range userData {
			outlets = append(outlets, utils.ToString(outlet["outlet_id"]))
		}
		outletId = strings.Join(outlets, ",")
	} else {
		isScopeAuthorized := false
		for _, outlet := range userData {
			if utils.ToString(outlet["outlet_id"]) == outletId {
				isScopeAuthorized = true
				break
			}
		}

		if !isScopeAuthorized {
			log.Info("scope is not allowed : %s | dataUser : %v", outletId, userData)
			ctx.SetStatusCode(fasthttp.StatusUnauthorized)
			return
		}
	}

	if insurerId != "" {
		insurerId = utils.Encrypt(insurerId)
	}

	log.Info("refresh_token validated. create new token [refresh token]...")

	//Generate Token
	jwtAuth := auth.InitJWTAuth()
	// authToken := jwtAuth.GenerateToken(tokenId, insurerId, role, outletId)
	authToken := jwtAuth.GenerateTokenWithOptions(auth.GenerateTokenOptions{
		Id:              tokenId,
		InsurerId:       insurerId,
		Role:            role,
		AccessAllowed:   outletId,
		AccessLimitUrls: accessLimitUrls,
	})

	ctx.SetContentType("application/json")
	ctx.SetStatusCode(http.StatusOK)
	json.NewEncoder(ctx).Encode(authToken)
}

func LoginAdmin(ctx *fasthttp.RequestCtx) {
	response := models.Response{Message: "Username or password invalid!"}

	email := ctx.FormValue("email")
	password := ctx.FormValue("password")
	firebaseToken := ctx.FormValue("firebase_token")
	token := ctx.Request.Header.Peek("Authorization")

	if string(token) != os.Getenv("token") {
		log.Info("user %s login with invalid authorization \ntoken from user : %s", email, token)
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		return
	}

	log.Info("login app: %s | %s", email, firebaseToken)

	data, err := db.Query("SELECT * FROM admin WHERE email=?", email)
	log.IfError(err)

	outlets := make([]map[string]interface{}, 0)
	result := make(map[string]interface{})
	status := "failed"
	userId := ""
	role := ""
	employeeId := ""

	if len(data) > 0 {
		log.Debug("login app - email registered as admin... %s", string(email))
		if utils.CheckPasswordHash(string(password), data["password"].(string)) {
			delete(data, "password")
			data["user_type"] = "admin"
			response.Status = true
			result["user"] = data
			response.Message = "Login success"
			status = "success"
			userId = utils.Encrypt(utils.ToString(data["admin_id"]))
			role = utils.ROLE_OWNER

			outlets, err = db.QueryArray("SELECT outlet_id FROM outlets WHERE admin_fkid = ?", data["admin_id"])
			log.IfError(err)
		} else {
			response.Message = "invalid password"
		}

		go func() {
			//write log login db
			_, err = db.GetDb().Exec("INSERT INTO log_login (date_time, ip_address, user_id, type, status) "+
				"VALUES (?,?,?,?,?)", time.Now().Unix()*1000, utils.GetIPAdress(ctx), data["admin_id"], "admin", status)
			log.IfError(err)

			//update last login
			_, err = db.UpdateDb("admin", map[string]interface{}{"last_login": time.Now().Unix() * 1000}, map[string]interface{}{"admin_id": data["admin_id"]})
			log.IfError(err)
		}()
	} else {
		log.Debug("login app - email not exist as admin, check employee... %s", string(email))
		data, err := db.Query("SELECT * FROM employee WHERE email=?", email)
		log.IfError(err)
		if len(data) > 0 {
			log.Info("email found as employee...")
			//if data["password"] != nil && auth.VerifyPassword(string(password), data["password"].(string)){
			if data["password"] != nil && utils.CheckPasswordHash(string(password), data["password"].(string)) {
				roleMobile := models.RoleMobile{}
				err := json.Unmarshal([]byte(data["role_mobile"].(string)), &roleMobile)
				log.IfError(err)

				if roleMobile.MasterLogin {
					delete(data, "password")
					delete(data, "role")
					data["admin_id"] = data["admin_fkid"]
					data["user_type"] = "employee"
					response.Status = true
					result["user"] = data
					response.Message = "Login success"
					status = "success"

					role = utils.ROLE_EMPLOYEE
					employeeId = utils.Encrypt(utils.ToString(data["employee_id"]))
					userId = utils.Encrypt(utils.ToString(data["admin_fkid"]))
					outlets, err = db.QueryArray("select outlet_fkid as outlet_id from employee_outlet where employee_fkid = ?", data["employee_id"])
					log.IfError(err)
				} else {
					response.Message = "You have no access for this request!"
				}
			} else {
				log.Info("password doesn't match: %v", email)
			}

			go func() {
				//write log login db
				_, err = db.GetDb().Exec("INSERT INTO log_login (date_time, ip_address, user_id, type, status) "+
					"VALUES (?,?,?,?,?)", time.Now().Unix()*1000, utils.GetIPAdress(ctx), data["employee_id"], "employee", status)
				log.IfError(err)
			}()
		}
	}

	if response.Status {
		var outletIds = make([]string, 0)
		for _, outlet := range outlets {
			outletIds = append(outletIds, utils.ToString(outlet["outlet_id"]))
		}

		//Generate Token
		auth := auth.InitJWTAuth()
		authToken := auth.GenerateToken(userId, employeeId, role, strings.Join(outletIds, ","))
		result["authorization"] = authToken
		response.Data = result
	}

	//check if multi account available
	sql := `SELECT admin_id as user_id, admin_id, name, business_name, 'admin' as account_type, '{}' as role_mobile 
	from admin where account_id=?
	UNION 
	select employee_id, e.admin_fkid,e.name, a.business_name, 'employee', role_mobile from employee e 
	join admin a on a.admin_id=e.admin_fkid
	 where e.account_id=? AND access_status_web = 'activated'`
	accounts, err := db.QueryArray(sql, data["account_id"], data["account_id"])
	log.Info("total account for: %s (%v) --> %d", email, data["account_id"], len(accounts))

	//validate login access
	enabledAccounts := make([]map[string]interface{}, 0)
	for _, account := range accounts {
		if utils.ToString(account["account_type"]) == "employee" {
			roleMobile := make(map[string]interface{})
			json.Unmarshal([]byte(utils.ToString(account["role_mobile"])), &roleMobile)
			log.Info("role mobile for %v : %v", account["account_type"], roleMobile["masterlogin"])
			if isMasterLogin, ok := roleMobile["masterlogin"].(bool); ok && isMasterLogin {
				enabledAccounts = append(enabledAccounts, account)
			}
		} else {
			enabledAccounts = append(enabledAccounts, account)
		}
	}

	if err == nil && len(enabledAccounts) > 1 {
		multiAccountToken, err := GenerateTokenMultiAccount(enabledAccounts)
		if !log.IfError(err) {
			multiAccount := make(map[string]interface{})
			multiAccount["enable_multi_account"] = true
			multiAccount["accounts"] = enabledAccounts
			multiAccount["token"] = multiAccountToken
			multiAccount["token_expired_at"] = time.Now().Add(time.Minute * 5).Unix()
			result["multi_account"] = multiAccount
		}
	}

	log.Debug("login app - '%s' status: %s | %s", string(email), status, response.Message)
	ctx.SetContentType("application/json")
	json.NewEncoder(ctx).Encode(response)
}

func GenerateTokenMultiAccount(accounts []map[string]interface{}) (string, error) {
	adminIids := make([]interface{}, 0)
	for _, account := range accounts {
		adminIids = append(adminIids, account["admin_id"])
	}
	token := jwt.New(jwt.SigningMethodHS512)
	token.Claims = jwt.MapClaims{
		"exp":        time.Now().Add(time.Minute * 5).Unix(),
		"iat":        time.Now().Unix(),
		"context":    "multi_account",
		"ids":        adminIids,
		"authorized": true,
		// "accounts": accounts,
	}

	return token.SignedString([]byte(MultiAccountTokenSecret))
}
