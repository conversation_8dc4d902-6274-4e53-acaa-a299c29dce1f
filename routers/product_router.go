package routers

import (
	"github.com/buaazp/fasthttprouter"
	v1 "gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
)

func SetProductRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	// Initialize product handler
	// productHandler := handler.NewProductHandler(db.GetDb())

	// Product routes
	router.GET("/v1/product/:outletId/:lastSync", auth.ValidateToken(v1.GetProduct))
	router.PUT("/v1/product", auth.ValidateToken(v1.UpdateProduct))
	router.POST("/v1/product", auth.ValidateToken(v1.AddProductMultipart))

	//change to: /v2/category?last_sync=1783837474
	router.GET("/v1/category/:lastSync", auth.ValidateToken(v1.GetCategory))
	router.GET("/v1/subcategory/:lastSync", auth.ValidateToken(v1.GetSubCategory))

	//change to: /v2/tax?outlet_id=29&last_sync=17389394x
	router.GET("/v1/tax/:outletId/:lastSync", auth.ValidateToken(v1.GetTax))
	router.GET("/v1/gratuity/:lastSync", auth.ValidateToken(v1.GetGratuity))
	router.GET("/v1/product_description/:outletId/:lastSync", auth.ValidateToken(v1.GetProductDescription))
	router.GET("/v1/multiple_price/:outletId/:lastSync", auth.ValidateToken(v1.GetMultiplePriceProduct))
	router.GET("/v1/link_menu/:outletId/:lastSync", auth.ValidateToken(v1.GetLinkMenu))
	router.GET("/v1/unit", auth.ValidateToken(v1.GetUnit))
	router.GET("/v1/product_type/:lastSync", auth.ValidateToken(v1.GetProductType))
	router.GET("/v1/product_variant/:lastSync", auth.ValidateToken(v1.GetProductVariant))

	// Generate AI product image
	// router.POST("/v1/product/generate-image", auth.ValidateToken(productHandler.GenerateProductImage))

	router.POST("/v1/photo", v1.TestUpload)
	return router
}
