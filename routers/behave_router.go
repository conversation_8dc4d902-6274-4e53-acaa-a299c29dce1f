package routers

import (
	"github.com/buaazp/fasthttprouter"
	"gitlab.com/uniqdev/backend/api-pos/integration/behave"
)

func SetBehaveRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.POST("/v1/pos/check/member", behave.CheckBehaveMember)
	router.POST("/v1/pos/check/voucher", behave.CheckVoucher)

	router.GET("/behave", behave.WelcomeBehave)
	router.POST("/behave/v1/auth/token", behave.RequestBehaveToken)

	return router
}
