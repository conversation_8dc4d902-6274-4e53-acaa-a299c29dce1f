package routers

import (
	"github.com/buaazp/fasthttprouter"
	v1 "gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
)

func SetOutletRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	// router.GET("/v1/outlet", auth.ValidateToken(v1.GetOutletByAdmin)) //moved to module
	router.GET("/v1/outlet/detail/:outletId", auth.ValidateToken(v1.GetOutletDetail))

	router.GET("/v1/employee/:outletId", auth.ValidateToken(v1.GetEmployeeByOutlet))
	router.GET("/v1/employee/:outletId/:lastSync", auth.ValidateToken(v1.GetEmployeeByOutlet))
	router.GET("/v1/employee/:outletId/:lastSync/:byLastSync", auth.ValidateToken(v1.GetEmployeeByOutlet))

	router.GET("/v1/role_mobile/:employeeId", auth.ValidateToken(v1.GetMobileRole))
	router.GET("/v1/role_mobile/:employeeId/:lastSync/:byLastSync", auth.ValidateToken(v1.GetMobileRole))

	router.GET("/v1/shift", auth.ValidateToken(v1.GetShiftByAdmin))
	router.GET("/v1/shift/:lastSync", auth.ValidateToken(v1.GetShiftByAdmin))

	router.POST("/v1/table", auth.ValidateToken(v1.AddTable))
	router.PUT("/v1/table", auth.ValidateToken(v1.UpdateTable))
	router.GET("/v1/table/:outletId/:lastSync", auth.ValidateToken(v1.GetTable))
	router.DELETE("/v1/table", auth.ValidateToken(v1.DeleteTable))

	router.GET("/v1/history_delete/:lastSync", auth.ValidateToken(v1.GetDeleteHistory))

	//Printer
	router.POST("/v1/printer", auth.ValidateToken(v1.AddPrinter))
	router.DELETE("/v1/printer", auth.ValidateToken(v1.DeletePrinter))
	router.GET("/v1/printer_ticket/:outletId/:lastSync", auth.ValidateToken(v1.GetPrinterTicket))
	//choose one of these later on
	router.GET("/v1/printer/:outletId", auth.ValidateToken(v1.GetPrinter))
	router.GET("/v1/printer/:outletId/:lastSync", auth.ValidateToken(v1.GetPrinter))
	//choose one of these later on
	router.GET("/v1/printer_closingshift/:outletId", auth.ValidateToken(v1.GetPrinterClosingShift))
	router.GET("/v1/printer_closingshift/:outletId/:lastSync", auth.ValidateToken(v1.GetPrinterClosingShift))

	router.GET("/v1/shift_available/:outletId/:timeOffset", auth.ValidateToken(v1.GetShiftAvailable))
	router.GET("/v1/outlet/shift/active/:outletId", auth.ValidateToken(v1.GetActiveShift))

	//Device
	router.GET("/v1/active_device/:openShiftId/:deviceId", auth.ValidateToken(v1.GetActiveDevices))
	router.GET("/v1/device/status/:deviceId", v1.GetDeviceStatus)
	router.GET("/v1/outlet/device/:status/:outletId", auth.ValidateToken(v1.GetDevicesByStatus))
	router.POST("/v1/outlet/device/lastSync", auth.ValidateToken(v1.UpdateLastSyncDevice))

	router.GET("/v1/bank/:outletId", auth.ValidateToken(v1.GetBank))
	router.GET("/v1/bank/:outletId/:lastSync", auth.ValidateToken(v1.GetBank))

	return router
}
