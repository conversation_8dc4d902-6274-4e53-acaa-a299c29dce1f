package routers

import (
	"github.com/buaazp/fasthttprouter"
	"gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
)

func SetPurchaseRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.GET("/v1/purchase/supplier/:lastSync", auth.ValidateToken(v1.GetSupplier))
	router.POST("/v1/purchase/supplier", auth.ValidateToken(v1.AddSupplier))
	router.PUT("/v1/purchase/supplier", auth.ValidateToken(v1.UpdateSupplier))
	router.GET("/v1/purchase/purchase_report_category/:lastSync", auth.ValidateToken(v1.GetPurchaseReportCategory))

	//operational cost
	router.GET("/v1/operational_cost/:openShiftId", auth.ValidateToken(v1.GetOperationalCostByShift))
	router.GET("/v1/purchase/operational_cost/:outletId/:lastSync", auth.ValidateToken(v1.GetOperationalCostByOutlet))
	router.POST("/v1/purchase/operational_cost", auth.ValidateToken(v1.SaveOperationalCost))
	router.DELETE("/v1/purchase/operational_cost/:id", auth.ValidateToken(v1.DeleteOperationalCost))
	router.POST("/v1/purchase/purchase_report_category", auth.ValidateToken(v1.SavePurchaseReportCategory))

	router.GET("/v1/purchase/product/outlet/:outletId/:lastSync", auth.ValidateToken(v1.GetPurchaseProductByOutlet))
	router.POST("/v1/purchase/product", auth.ValidateToken(v1.SavePurchaseProduct))
	router.GET("/v1/purchase/cash_in/:outletId/:lastSync", auth.ValidateToken(v1.GetCashIn))
	router.POST("/v1/purchase/cash_in", auth.ValidateToken(v1.SaveCashIn))

	//Retur Product
	router.GET("/v1/purchase/retur_product/:lastSync", auth.ValidateToken(v1.GetReturProduct))
	router.POST("/v1/purchase/retur_product", auth.ValidateToken(v1.SaveReturProduct))

	//Debt
	router.GET("/v1/purchase/debt_payment/:lastSync", auth.ValidateToken(v1.GetDebtPayment))
	router.POST("/v1/purchase/debt_payment", auth.ValidateToken(v1.SaveDebtPayment))

	//Purchase confirm
	router.GET("/v1/purchase/confirm/:lastSync", auth.ValidateToken(v1.GetPurchaseConfirm))
	router.POST("/v1/purchase/confirm", auth.ValidateToken(v1.SavePurchaseConfirm))
	return router
}