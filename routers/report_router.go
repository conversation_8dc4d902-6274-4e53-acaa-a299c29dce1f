package routers

import (
	"github.com/buaazp/fasthttprouter"
	"gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
)

func SetReportRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.GET("/v1/cash_recap/:outletId/:lastSync", auth.ValidateToken(v1.GetCashRecap))
	router.GET("/v1/stock_summary/:limit/:offset/:alert/:adminId", v1.GetStockSummary)
	return router
}
