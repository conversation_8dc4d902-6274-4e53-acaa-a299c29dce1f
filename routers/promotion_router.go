package routers

import (
	"github.com/buaazp/fasthttprouter"
	v1 "gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
)

func SetPromotionRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.GET("/v1/promotion/:outletId/:lastSync", auth.ValidateToken(v1.GetPromotionByOutlet))
	router.POST("/v1/promotion/void", auth.ValidateToken(v1.VoidPromotion))

	return router
}
