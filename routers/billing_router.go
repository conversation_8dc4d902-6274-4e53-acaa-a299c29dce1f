package routers

import (
	"github.com/buaazp/fasthttprouter"
	v1 "gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
)

func SetBillingRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	//get subscription status
	router.GET("/v1/billing/subscription/status", auth.ValidateToken(v1.GetSubscriptionStatus))
	router.GET("/v1/billing/service", auth.ValidateToken(v1.GetServices))
	router.POST("/v1/billing/subscription", auth.ValidateToken(v1.CreateSubscription))
	router.POST("/v1/billing/subscribe", auth.ValidateToken(v1.SubscribeToMultipleService))

	//api for web pos
	router.POST("/api/v1/subscribe", v1.SubscriptionWebPos)
	router.POST("/api/v1/billing/:id/invoice", v1.GetInvoiceWeb)

	//payment gateway callback
	router.POST("/payment_gateway/va/paid", v1.ReceiveVAPaid)
	router.POST("/payment_gateway/va/create", v1.ReceiveVACreated)

	return router
}
