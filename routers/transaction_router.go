package routers

import (
	"github.com/buaazp/fasthttprouter"
	"gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
)

func SetTransactionRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	//order online
	router.POST("/v1/add_sales/order_sales", v1.AddSalesFromOrder)

	router.POST("/v1/sales", auth.ValidateToken(v1.NewSales))
	router.GET("/v1/sales/:outletId/:page/:lastSync", auth.ValidateToken(v1.GetSalesByOutlet))
	router.GET("/v1/sales", auth.ValidateToken(v1.GetSalesByIds))

	router.GET("/v1/open_shift/:outletId/:lastSync", auth.ValidateToken(v1.GetOpenShift))
	//Remove this ..... and edit in controller
	router.GET("/v1/open_shift/:outletId", auth.ValidateToken(v1.GetOpenShift))
	//router.GET("/v1/open_shift/:outletId", v1.GetOpenShift)

	router.POST("/v1/sales/validate", auth.ValidateToken(v1.GetTotalUnSyncSales))
	//router.POST("/v1/sales/sync/:openShiftId", auth.ValidateToken())

	router.GET("/v1/transaction/notes", auth.ValidateToken(v1.GetSalesNotes))

	router.POST("/v1/open_shift", auth.ValidateToken(v1.SaveOpenShift))
	router.POST("/v1/cash_recap", auth.ValidateToken(v1.SaveCashRecap))
	router.POST("/v1/refund", auth.ValidateToken(v1.SaveRefund))

	//Pending Bill
	router.GET("/v1/transaction/sales_cart/status/:salesId", auth.ValidateToken(v1.GetPendingBillStatus))
	router.GET("/v1/sales_cart/:outletId/:lastSync", auth.ValidateToken(v1.GetSalesCart))
	router.POST("/v1/sales_cart", auth.ValidateToken(v1.SaveSalesCart))
	router.PUT("/v1/sales_cart", auth.ValidateToken(v1.UpdateSalesCart))

	//Reservation
	router.GET("/v1/reservation/outlet/:outletId/:lastSync", auth.ValidateToken(v1.GetReservationByOutlet))
	router.POST("/v1/reservation", auth.ValidateToken(v1.SaveReservation))

	//Piutang
	router.GET("/v1/transaction/piutang/:outletId/:lastSync", auth.ValidateToken(v1.GetPiutang))
	router.GET("/v1/transaction/piutang_history/:outletId/:lastSync", auth.ValidateToken(v1.GetPiutangHistory))
	router.POST("/v1/transaction/piutang_history", auth.ValidateToken(v1.AddPiutangPayment))

	router.POST("/v1/cash_recap_report", v1.SendCashRecapReport)

	//Member
	router.GET("/v1/transaction/member/check/:outletId", auth.ValidateToken(v1.CheckMember))
	router.GET("/v1/transaction/member/secret_code", auth.ValidateToken(v1.CheckSecretCode))

	//Promotion
	router.GET("/v1/transaction/promotion/check/:outletId", auth.ValidateToken(v1.CheckVoucherCode))
	router.POST("/v1/transaction/promotion/void", auth.ValidateToken(v1.VoidPromotion))


	//order online
	router.GET("/v1/transaction/online/:outletId/:lastSync", auth.ValidateToken(v1.GetOrderSales))
	router.PUT("/v1/transaction/online", auth.ValidateToken(v1.UpdateOrderSales))

	//self order
	//router.GET("/v1/transaction/self_order/:code", auth.ValidateToken(v1.GetSelfOrder))

	//temporary
	router.GET("/v1/transaction/recap_all_outlet/:adminId", v1.ForceSendRecapAllOutlet)

	return router
}
