package routers

import (
	"github.com/buaazp/fasthttprouter"
	"gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
)

func SetMemberRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.GET("/v1/member/type", auth.ValidateToken(v1.GetMemberType)) //auth.ValidateToken(v1.GetMemberType)
	router.GET("/v1/member/admin", auth.ValidateToken(v1.GetMembers))
	return router
}
