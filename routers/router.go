package routers

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"os"
	"time"

	"github.com/buaazp/fasthttprouter"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-pos/core/cast"
	"gitlab.com/uniqdev/backend/api-pos/core/db"
	"gitlab.com/uniqdev/backend/api-pos/core/google"
	"gitlab.com/uniqdev/backend/api-pos/core/log"
	"gitlab.com/uniqdev/backend/api-pos/core/utils"
	"gitlab.com/uniqdev/backend/api-pos/job"
)

func CreateRoutes() *fasthttprouter.Router {
	router := fasthttprouter.New()
	router = SetAuthenticationRoutes(router)
	router = SetProductRoutes(router)
	router = SetOutletRoutes(router)
	router = SetPurchaseRoutes(router)
	router = SetTransactionRoutes(router)
	router = SetReportRoutes(router)
	router = SetMemberRoutes(router)
	router = SetHomePageRoutes(router)
	router = SetMessagerRoutes(router)
	router = SetSystemRoutes(router)
	router = SetBehaveRoutes(router)
	router = SetPromotionRoutes(router)
	router = SetBillingRoutes(router)
	router = SetHelpRoutes(router)
	router = SetDeviceRoutes(router)

	router.GET("/", Index)
	router.GET("/check/health", CheckHealth)

	//cron
	router.POST("/cron/report/close-shift", job.PubSubSendAllOutletReport)
	router.GET("/cron/piutang_reminder", func(ctx *fasthttp.RequestCtx) {
		if os.Getenv("server") == "development" {
			ctx.Write([]byte("starting...."))
			job.PiutangReminder()
		} else {
			ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		}
	})

	router.POST("/ping", func(ctx *fasthttp.RequestCtx) {
		ctx.SetStatusCode(fasthttp.StatusOK)
		fmt.Printf("Ping... by: %s, ip: %s\n", string(ctx.QueryArgs().Peek("id")), ctx.RemoteIP())

		endPoint := ctx.PostArgs().Peek("end_point")
		if endPoint != nil {
			fmt.Printf("req: %s\n", endPoint)
			req := utils.HttpRequest{
				Method: "GET",
				Url:    string(endPoint),
			}
			resp, err := req.Execute()
			fmt.Printf("err: %v, resp: %s", err, resp)
			if err == nil {
				ctx.Write(resp)
			} else {
				ctx.WriteString(err.Error())
			}
		}
	})

	router.GET("/test/json", func(ctx *fasthttp.RequestCtx) {
		keys := make([]string, 0)
		for i := 0; i < 10; i++ {
			keys = append(keys, utils.RandStringBytes(rand.Intn(50)))
		}
		data := make([]map[string]interface{}, 0)
		for i := 0; i <= 10000; i++ {
			single := make(map[string]interface{})
			for index, key := range keys {
				if index%2 == 0 {
					single[key] = utils.RandStringBytes(rand.Intn(50))
				} else {
					single[key] = rand.Int()
				}
			}
			data = append(data, single)
		}
		time.Sleep(20 * time.Second)
		ctx.SetContentType("application/json")
		_ = json.NewEncoder(ctx).Encode(data)
	})

	router.GET("/test/notification", TestNotification)

	//router.GET("/fish/:id/:ph/:turbidity/:temperature/:do/:feeder", v1.Fishgator)
	return router
}

func Index(ctx *fasthttp.RequestCtx) {
	ctx.SetStatusCode(fasthttp.StatusOK)
}

func CheckHealth(ctx *fasthttp.RequestCtx) {
	log.IfError(fmt.Errorf("test"))
	ctx.Write([]byte(fmt.Sprintf("api-pos, environment %s, running on port : %s", os.Getenv("server"), os.Getenv("PORT"))))
	ctx.SetStatusCode(fasthttp.StatusOK)
}

func TestNotification(ctx *fasthttp.RequestCtx) {
	imei := ctx.QueryArgs().Peek("imei")
	devices, err := db.QueryArray("select firebase_token from devices where imei = ?", string(imei))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		ctx.Write([]byte(err.Error()))
		return
	}

	errs := make(map[string]error)
	for _, device := range devices {
		err = google.SendNotification(google.NotificationData{
			Token: cast.ToString(device["firebase_token"]),
			Data: map[string]string{
				"title":   "Syncing...",
				"message": "start syncing data",
				"type":    "action_upload_log",
			},
		})
		if err != nil {
			errs[cast.ToString(device["firebase_token"])] = err
		}
	}
	if len(errs) > 0 {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		ctx.Write([]byte(fmt.Sprintf("sending notification to %d devices, %v error: %v", len(devices), len(errs), errs)))
	} else {
		ctx.SetStatusCode(fasthttp.StatusOK)
		ctx.Write([]byte(fmt.Sprintf("sending notification to %d devices", len(devices))))
	}
}
