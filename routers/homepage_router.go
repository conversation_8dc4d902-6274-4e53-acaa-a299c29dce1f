package routers

import (
	"github.com/buaazp/fasthttprouter"
	"gitlab.com/uniqdev/backend/api-pos/controllers/v1"
)

func SetHomePageRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.POST("/join", v1.Join)
	router.POST("/customer/register", v1.RegisterCustomer)
	router.GET("/v1/test", v1.Test)
	router.POST("/v1/whatsapp_receive", v1.ReceiveWA)
	router.POST("/webhook/trello", v1.TrelloWebhook)
	router.GET("/webhook/trello", v1.TrelloWebhook)
	router.HEAD("/webhook/trello", v1.TrelloWebhook)

	router.GET("/check/ip", v1.GetIPAddress)
	router.GET("/api/test", v1.GetDetailPost)
	router.POST("/api/test", v1.GetDetailPost)

	//benchmark
	router.GET("/api/test/benchmark/:count", v1.BenchmarkGet)
	router.POST("/api/test/benchmark", v1.BenchmarkInsert)
	return router
}
