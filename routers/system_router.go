package routers

import (
	"github.com/buaazp/fasthttprouter"
	"gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
)

func SetSystemRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.POST("/v1/system/log", auth.ValidateToken(v1.UploadLog))
	router.GET("/v1/system/mac_vendor/:macaddress", v1.MacAddressVendorLookup)

	router.POST("/v1/system/firebase_token", auth.ValidateToken(v1.SetFirebaseToken))
	router.POST("/v1/system/app_release", v1.UpdateAppVersion)
	router.GET("/v1/system/app_version", auth.ValidateToken(v1.GetAppVersion))
	router.GET("/v2/system/app_version", (v1.GetAppVersionV2))

	return router
}
