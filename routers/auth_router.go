package routers

import (
	"github.com/buaazp/fasthttprouter"
	v1 "gitlab.com/uniqdev/backend/api-pos/controllers/v1"
	v2 "gitlab.com/uniqdev/backend/api-pos/controllers/v2"
	"gitlab.com/uniqdev/backend/api-pos/core/auth"
)

func SetAuthenticationRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.POST("/v1/auth/token", v1.Token)
	router.POST("/v2/auth/token", auth.ValidateRefreshToken(v2.Token))

	router.POST("/v1/auth/loginadmin", v1.LoginAdmin)
	router.POST("/v2/auth/login", v2.LoginAdmin)

	router.POST("/v1/auth/login_employee", auth.ValidateToken(v1.LoginEmployee))
	router.POST("/v1/auth/pin", auth.ValidateToken(v1.LoginEmployee))

	// router.POST("/v1/auth/logout_employee", auth.ValidateToken(v1.LogoutEmployee))
	return router
}
